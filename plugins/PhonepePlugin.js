const { withProjectBuildGradle } = require('@expo/config-plugins');

const withMyPlugin = (config) => {
    return withProjectBuildGradle(config, (config) => {
        if (config.modResults.language === 'groovy') {
            if(!config.modResults.contents.includes("phonepe.mycloudrepo.io")) {
                config.modResults.contents = modify(config.modResults.contents)
            }
        } else {
            throw new Error("Can't add maven repository to the build.gradle because the project is using the 'kts' language.");
        }
        return config;
    });
};

function modify (str) {
    const firstIndex = str.indexOf("google()");

    if (firstIndex !== -1) {
        const secondIndex = str.indexOf("google()", firstIndex + 1);
        if (secondIndex !== -1) {
            const result = str.slice(0, secondIndex + 8) + `
            maven {
                url  "https://phonepe.mycloudrepo.io/public/repositories/phonepe-intentsdk-android"
           }
            ` + str.slice(secondIndex + 8);
            return result;
        }
    }

    return str;
}




module.exports = withMyPlugin;
