import React, { useEffect, useState } from 'react';
import { checkOnboardingStatus, markOnboardingAsShown } from './src/onboarding/onboardingView';
import AppNavigation from './src/navigation/AppNavigation';
import Onboarding from './src/auth/Onboarding';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import NoInternet from './src/no-internet/NoInternet';
import NetInfo from '@react-native-community/netinfo';
import CustomFontProvider from './src/context/CustomFontProvider';
import { ClientContext } from './src/context/ClientContext';

const App = () => {

	const [isConnected, setIsConnected] = useState<boolean | null>(true);
	const { token } = React.useContext(ClientContext);
	const [showOnboarding, setShowOnboarding] = useState<boolean | null>(null);
	const [initialScreen, setInitialScreen] = useState<string>('SignIn');
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const checkStatus = async () => {
			const status = await checkOnboardingStatus();
			setShowOnboarding(status);
		};

		checkStatus();
	}, []);

	useEffect(() => {
		const unsubscribe = NetInfo.addEventListener((state: any) => {
			setIsConnected(state?.isConnected);
		});

		NetInfo.fetch().then((state) => {
			setIsConnected(state?.isConnected);
		});

		return () => {
			unsubscribe();
		};
	}, []);

	React.useEffect(() => {
		const validateToken = async () => {
			setIsLoading(true);
			await new Promise(resolve => setTimeout(resolve, 100));
			setIsLoading(false);
		};

		validateToken();
	}, [token]);

	if (isLoading) {
		return null;
	}

	if (!isConnected) {
		return <NoInternet />;
	}

	return (
		<GestureHandlerRootView className='flex-1 bg-white dark:bg-dark'>
			<CustomFontProvider>
				{showOnboarding ? (
					<AppNavigation initialScreen={initialScreen} />
				) : (
					<Onboarding
						onFinish={async () => {
							await markOnboardingAsShown();
							setShowOnboarding(true);
						}}
						setInitialScreen={(screen) => setInitialScreen(screen)}
					/>
				)}
			</CustomFontProvider>
		</GestureHandlerRootView>
	);
};

export default App;

