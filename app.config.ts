import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => {
	return {
		...config,
		name: "Lawcube",
		slug: "lawcube",
		scheme: "lawcube",
		version: "1.0.1",
		runtimeVersion: "1.0.1",
		orientation: "portrait",
		icon: "./src/assets/images/app_icon.png",
		userInterfaceStyle: "automatic",
		ios: {
			supportsTablet: true,
			bundleIdentifier: "com.lawcube.in",
			associatedDomains: [
				"applinks:lawcube.org",
				"applinks:www.lawcube.org",
				"applinks:https://www.lawcube.org"
			],
			infoPlist: {
				ITSAppUsesNonExemptEncryption: false,
				NSCameraUsageDescription: "$(PRODUCT_NAME) needs access to your camera to capture photos and videos for profile pictures, posts, or sharing with others.",
				NSPhotoLibraryUsageDescription: "$(PRODUCT_NAME) needs access to your photo library to upload and share images or videos."
			}
		},
		android: {
			adaptiveIcon: {
				foregroundImage: "./src/assets/app-icon/lawcubeappicon.png",
				backgroundColor: "#000000"
			},
			edgeToEdgeEnabled: true,
			package: "com.lawcube.in",
			intentFilters: [
				{
					action: "VIEW",
					autoVerify: true,
					data: [
						{
							scheme: "https",
							host: "www.lawcube.org",
							pathPrefix: "/readings"
						}
					],
					category: ["BROWSABLE", "DEFAULT"]
				}
			],
			permissions: [
				"android.permission.INTERNET",
				"android.permission.READ_EXTERNAL_STORAGE",
				"android.permission.WRITE_EXTERNAL_STORAGE",
				"android.permission.DOWNLOAD_WITHOUT_NOTIFICATION",
				"android.permission.ACCESS_NETWORK_STATE"
			]
		},
		web: {
			favicon: "./src/assets/app-icon/appicon.png"
		},
		plugins: [
			"./plugins/PhonepePlugin",
			"expo-font",
			"@config-plugins/react-native-blob-util",
			"@config-plugins/react-native-pdf",
			["expo-build-properties", { android: { compileSdkVersion: 35, targetSdkVersion: 35 } }],
			"react-native-edge-to-edge",
			[
				"expo-splash-screen",
				{
					image: "./src/assets/icons/logo/lex.png",
					imageWidth: 200,
					resizeMode: "contain",
					backgroundColor: "#ffffff"
				}
			]
		],
		extra: {
			eas: {
				projectId: "b6875ab2-26e5-40a3-85d9-7cc2016ed34a"
			}
		},
		owner: "lawcube",
		updates: {
			url: "https://u.expo.dev/b6875ab2-26e5-40a3-85d9-7cc2016ed34a"
		}
	}
}