{"name": "lawcube", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "clean": "cd android && ./gradlew clean && cd ..", "bundle": "cd android && ./gradlew bundleRelease && cd ..", "build:android": "cd android && ./gradlew assembleRelease && cd ..", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@config-plugins/react-native-blob-util": "^8.0.0", "@config-plugins/react-native-pdf": "^8.0.0", "@expo-google-fonts/dm-sans": "^0.2.3", "@expo-google-fonts/lora": "^0.2.3", "@expo/config-plugins": "~10.1.1", "@expo/metro-runtime": "~5.0.4", "@expo/prebuild-config": "~9.0.0", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.8", "@homielab/react-native-auto-scroll": "^0.0.10", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^6.6.0", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.10.0", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.7", "@types/react-native-htmlview": "^0.16.5", "@types/react-native-snap-carousel": "^3.8.11", "@types/styled-components": "^5.1.34", "axios": "^1.7.2", "debounce": "^2.2.0", "dotenv": "^16.4.7", "expo": "^53.0.20", "expo-av": "~15.1.7", "expo-build-properties": "^0.14.8", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-screen-capture": "~7.2.0", "expo-screen-orientation": "~8.1.7", "expo-sharing": "~13.1.5", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "^5.0.10", "expo-updates": "~0.28.17", "expo-video": "^2.2.2", "html-to-text": "^9.0.5", "lottie-react-native": "7.2.2", "moment": "^2.30.1", "nativewind": "^2.0.11", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.52.1", "react-native": "0.79.5", "react-native-accordion-wrapper": "^1.0.3", "react-native-base64": "^0.2.1", "react-native-blob-util": "^0.22.2", "react-native-edge-to-edge": "^1.6.2", "react-native-element-dropdown": "^2.12.1", "react-native-gesture-handler": "~2.24.0", "react-native-htmlview": "^0.17.0", "react-native-paper": "^5.12.3", "react-native-pdf": "^6.7.7", "react-native-phonepe-pg": "https://phonepe.mycloudrepo.io/public/repositories/phonepe-mobile-react-native-sdk/releases/v2/react-native-phonepe-pg.tgz", "react-native-reanimated": "~3.17.4", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-snap-carousel": "^3.9.1", "react-native-star-rating-widget": "^1.9.1", "react-native-svg": "15.11.2", "react-native-ui-datepicker": "^2.0.3", "sha256": "^0.2.0", "socket.io-client": "^4.7.5", "styled-components": "^6.1.19", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/html-to-text": "^9.0.4", "@types/lodash.debounce": "^4.0.9", "@types/react": "~19.0.10", "@types/react-native-base64": "^0.2.2", "@types/react-native-datepicker": "^1.7.6", "@types/sha256": "^0.2.2", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "tailwindcss": "3.3.2", "typescript": "~5.8.3"}, "private": true}