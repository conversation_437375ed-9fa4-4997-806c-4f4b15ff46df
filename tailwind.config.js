/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ["./App.{js,jsx,ts,tsx}", "./src/**/*.{js,jsx,ts,tsx}"],
	theme: {
		extend: {
			colors: {
				primary: '#FDD066',
				secondary: '#2D2828',
				cardLight: '#F8F6F3',
				dark: '#111',
				ececec: '#ECECEC',
				secondary2: '#8F8F8F',
				greycolor: '#a8a8a8',
				darkcard: '#2F2F2F'
			},
			padding: {
				primary: '4%'
			},
			borderColor: {
				primary: '#A8A8A8'
			},
			fontSize: {
				text13: '13px',
				text14: '14px',
				text15: '15px',
				text16: '16px',
				text17: '17px',
				text19: '19px',
				text20: '20px',
				text21: '21px',
				text22: '22px',
			}
		},
	},
	plugins: [],
}

