import { ClientAxiosInstance } from "../lib/axiosInstance";

export const setLectureFinished = async (topicId: string, lectureId: string, courseId:string, clientId:string) => {
    try {
        await ClientAxiosInstance.post(`/courseprogress/track-progress`, {
            topic_id: topicId,
            lecture_id: lectureId,
            user_id: clientId,
            course_id: courseId
        });

    } catch (error) {
        console.log("Set lecture finish error : ", error);
    }
};