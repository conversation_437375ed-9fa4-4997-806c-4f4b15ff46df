export const getDate = (dateString: string): string => {
	const monthNames = [
		"Jan",
		"Feb",
		"Mar",
		"Apr",
		"May",
		"Jun",
		"Jul",
		"Aug",
		"Sep",
		"Oct",
		"Nov",
		"Dec",
	];
	const d = new Date(dateString);
	const month = monthNames[d.getMonth()];
	const date = String(d.getDate()).padStart(2, "0");
	const year = d.getFullYear();
	return `${date} ${month}, ${year}`;
};

export const getQuizDate = (dateString: string): string => {
	const monthNames = [
		"Jan",
		"Feb",
		"Mar",
		"Apr",
		"May",
		"Jun",
		"Jul",
		"Aug",
		"Sep",
		"Oct",
		"Nov",
		"Dec",
	];
	const d = new Date(dateString);
	const month = monthNames[d.getMonth()];
	const date = String(d.getDate()).padStart(2, "0");
	const year = d.getFullYear();
	return `${date} ${month}, ${year}`;
};

export const getJournalDate = (dateString: string): string => {
	const monthNames = [
		"Jan",
		"Feb",
		"Mar",
		"Apr",
		"May",
		"Jun",
		"Jul",
		"Aug",
		"Sep",
		"Oct",
		"Nov",
		"Dec",
	];
	const d = new Date(dateString);
	const month = monthNames[d.getMonth()];
	const date = String(d.getDate()).padStart(2, "0");
	const year = d.getFullYear();
	return `${date}, ${month} ${year}`;
};

export function separateTimeFromDateTime(date: Date | string) {
	const localDate = new Date(date);
	const options: Intl.DateTimeFormatOptions = {
		hour: "numeric",
		minute: "numeric",
		hour12: true,
	};

	const formattedTime = localDate.toLocaleTimeString("en-US", options);
	return formattedTime;
}

export const getDateDay = (time: string) => {
	const date = new Date(time);
	const weekday = date.toLocaleDateString("en-US", {
		weekday: "long",
		timeZone: "UTC",
	});
	const day = date.toLocaleDateString("en-US", {
		day: "numeric",
		timeZone: "UTC",
	});
	const month = date.toLocaleDateString("en-US", {
		month: "long",
		timeZone: "UTC",
	});
	const year = date.toLocaleDateString("en-US", {
		year: "numeric",
		timeZone: "UTC",
	});

	return `${weekday}, ${month} ${day}, ${year}`;
};

export const getTimeQuiz = (time: string) => {
	const [timePart, meridian] = time?.trim().split(" ");
	let [hour, minute] = timePart?.split(":")?.map(Number);

	if (meridian === "PM" && hour !== 12) {
		hour += 12;
	} else if (meridian === "AM" && hour === 12) {
		hour = 0;
	}

	return (hour % 12) * 60 + minute;
};


export const formatDate = (dateString: any) => {
	const months = [
		'January', 'February', 'March', 'April', 'May', 'June',
		'July', 'August', 'September', 'October', 'November', 'December'
	];

	const date = new Date(dateString);
	const day = date.getDate();
	const month = months[date.getMonth()];
	const year = date.getFullYear();

	return `${month} ${day}, ${year}`;
};


export const isRegistrationExpired = (dateString: string) => {

	const date = new Date(dateString);
	date.setHours(date.getHours() - 6);
	date.setMinutes(date.getMinutes() - 30);

	const currentDate = new Date();
	currentDate.setHours(currentDate.getHours() - 6);
	currentDate.setMinutes(currentDate.getMinutes() - 30);

	return date <= currentDate;
}

export const isExpired = (dateString: string) => {

	const date = new Date(dateString);
	date.setHours(0, 0, 0, 0);

	const currentDate = new Date();
	currentDate.setHours(0, 0, 0, 0);

	return date < currentDate;
}

export const isOngoing = (dateTime: string) => {
	const currentDate = new Date();
	const startTime = new Date(dateTime);
	const midnightTime = new Date(dateTime);

	midnightTime.setDate(midnightTime.getDate() + 1);
	midnightTime.setHours(0, 0, 0, 0);

	return currentDate >= startTime && currentDate < midnightTime;
};

export const isWritingOngoing = (end_date: string, registration_end_datetime: string): boolean => {

	const currentDate = new Date();
	const registrationEnd = new Date(registration_end_datetime);

	const midnightTime = new Date(end_date);
	midnightTime.setDate(midnightTime.getDate() + 1);
	midnightTime.setHours(0, 0, 0, 0);

	return currentDate >= registrationEnd && currentDate < midnightTime;
};

export const isQuizOngoing = (date_time: string, start_time: string, end_time: string): boolean => {
	if (!end_time || !date_time || !start_time) return false;

	const date = new Date(date_time);
	const currentDate = new Date();

	date.setHours(0, 0, 0, 0);
	currentDate.setHours(0, 0, 0, 0);

	if (date < currentDate || date > currentDate) return false;

	const parseTime = (time: string): { hours: number; minutes: number } => {
		const [timePart, meridian] = time.trim().split(" ");
		let [hours, minutes] = timePart.split(":").map(Number);
		if (meridian === "PM" && hours !== 12) hours += 12;
		if (meridian === "AM" && hours === 12) hours = 0;
		return { hours, minutes };
	};

	const start = parseTime(start_time);
	const end = parseTime(end_time);

	const currentTime = new Date();
	const currentHours = currentTime.getHours();
	const currentMinutes = currentTime.getMinutes();

	const isAfterStart = currentHours > start.hours || (currentHours === start.hours && currentMinutes >= start.minutes);
	const isBeforeEnd = currentHours < end.hours || (currentHours === end.hours && currentMinutes <= end.minutes);

	return isAfterStart && isBeforeEnd;
};

export const isQuizExpired = (dateString: string, endTime: string) => {

	if (!endTime) return false;
	if (!dateString) return false;

	const date = new Date(dateString);
	const currentDate = new Date();

	date.setHours(0, 0, 0, 0);
	currentDate.setHours(0, 0, 0, 0);

	if (date < currentDate) return true;
	if (date > currentDate) return false;

	const [timePart, meridian] = endTime?.trim()?.split(" ");
	let [hour, minute] = timePart?.split(":")?.map(Number);

	hour = (meridian === "PM" && hour !== 12) ? hour + 12 : (meridian === "AM" && hour === 12) ? 0 : hour;

	const currentTime = new Date();
	if (currentTime.getHours() < hour) return false;
	if (currentTime.getHours() > hour) return true;

	if (currentTime.getMinutes() < minute) return false;
	if (currentTime.getMinutes() > minute) return true;
	if (currentTime.getMinutes() === minute) return true;

};

export const getRegistrationEndTime = (date: string, career?:boolean) => {

	const d = new Date(date);
	if(career) d.setHours(23, 59, 59, 999);

	const dateString = d.toLocaleDateString("en-US", {
		month: "short",
		day: "2-digit",
	});
	const timeString = d
		.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		})
		.replace(/\b([AP])M\b/g, (_, p1) => p1.toUpperCase() + "M");

	const currentTime = new Date();

	if (currentTime > d) {
		return "na";
	}

	return `${dateString}, ${timeString}`;
};


export const formatDuration = (totalDurationTaken: number) => {
	const minutes = Math.floor(totalDurationTaken / 60);
	const seconds = totalDurationTaken % 60;

	const formattedMinutes = String(minutes).padStart(2, '0');
	const formattedSeconds = String(seconds).padStart(2, '0');

	return `${formattedMinutes}:${formattedSeconds}`;
};

export const formatDateFilter = (date: Date) => {
	const format = date.toISOString().split('T')[0].split("-");
	return `${format[2]}/${format[1]}/${format[0]}`;
};