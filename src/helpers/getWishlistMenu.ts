export const getWishListMenu = (menu: string) => {
  switch (menu) {
    case "quiz":
      return "Quiz";
    case "mootcourt":
      return "Moot Court";
    case "essaywriting":
      return "Essay Writing";
    case "articlewriting":
      return "Article Writing";
    case "debate":
      return "Debate";
    case "collegeevent":
      return "College Events";
    case "seminar":
      return "Seminars";
    case "workshop":
      return "Workshop";
    case "job":
      return "Job Listing";
    case "internship":
      return "Internship";
    case "blog":
      return "Blogs";
    case "news":
      return "News";
    case "article":
      return "Articles";
    case "journal":
      return "Journals";
    case "course":
      return "Course";
    case 'freelancer':
      return "Freelancer"
    default:
      return "";
  }
};

export const getWishList = (menu: string) => {
  switch (menu) {
    case "Quiz":
      return "quiz";
    case "Moot Court":
      return "mootcourt";
    case "Essay Writing":
      return "essaywriting";
    case "Article Writing":
      return "articlewriting";
    case "Debate":
      return "debate";
    case "College Event":
      return "collegeevent";
    case "Seminar":
      return "seminar";
    case "Workshops":
      return "workshop";
    case "Jobs":
      return "job";
    case "Internship":
      return "internship";
    case "Blogs":
      return "blog";
    case "News":
      return "news";
    case "Articles":
      return "article";
    case "Journals":
      return "journal";
  }
};

export const searchPath = (chip: string) => {
  let url = "";
  let path = "";
  if (chip === "quiz" || chip === "mootcourt" || chip === "essaywriting" || chip === "articlewriting" || chip === "debate") {
    url = `currentCompetition`;
    path = "CompetitionDetails";
  } else if (chip === "collegeevent" || chip === "workshop" || chip === "seminar") {
    url = `currentEvent`;
    path = "EventDetails";
  } else if (chip === "blog" || chip === "news" || chip === "article" || chip === "journal") {
    url = `item`;
    path = "ReadingDetail";
  } else if (chip === "course") {
    url = `currentCourse`;
    path = "CourseDetails";
  } else if (chip === "job" || chip === "internship") {
    url = `selectedCareer`;
    path = "CareerDetails";
  } else if (chip === "freelancer") {
    url = `data`;
    path = "LawyerDetails";
  }
  return { url, path };
};


export const getChipPayment = (menu: string) => {
  switch (menu) {
    case "Quiz":
      return "quiz";
    case "Moot Court":
      return "mootcourt";
    case "Essay Writing":
      return "essaywriting";
    case "Article Writing":
      return "articlewriting";
    case "Debate":
      return "debate";
    case "College Event":
      return "collegeevent";
    case "Seminar":
      return "seminar";
    case "Workshops":
      return "workshop";
    case "Beginner":
      return 'coursebeginner';
    case "Intermediate":
      return 'courseintermediate';
    case "Advanced":
      return 'courseadvanced';
    default:
      return "";
  }
};