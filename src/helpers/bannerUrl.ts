export const bannerUrl = (chip: string, id: string) => {
  let url = "";
  let path = "";
  let selected_chip = "";
  if (chip === "quiz") {
    url = `/quiz/${id}?live=true`;
    path = "CompetitionDetails";
    selected_chip = 'Quiz'
  } else if (chip === "moot-court") {
    url = `/mootcourt/${id}`;
    path = "CompetitionDetails";
    selected_chip = 'Moot Court'
  } else if (chip === "essay-writing") {
    url = `/essaywriting/${id}`;
    path = "CompetitionDetails";
    selected_chip = 'Essay Writing'
  } else if (chip === "article-writing") {
    url = `/articlewriting/${id}`;
    path = "CompetitionDetails";
    selected_chip = 'Article Writing'
  } else if (chip === "debate") {
    url = `/debate/${id}`;
    path = "CompetitionDetails";
    selected_chip = 'Debate'
  } else if (chip === "college-events") {
    url = `/collegeevent/${id}`;
    path = "EventDetails";
    selected_chip = 'College Events'
  } else if (chip === "workshop") {
    url = `/workshop/${id}`;
    path = "EventDetails";
    selected_chip = 'Workshop'
  } else if (chip === "seminar") {
    url = `/seminar/${id}`;
    path = "EventDetails";
    selected_chip = 'Seminars'
  } else if (chip === "blog") {
    url = `/blog/${id}`;
    path = "ReadingDetail";
    selected_chip = 'Blogs'
  } else if (chip === "news") {
    url = `/news/${id}`;
    path = "ReadingDetail";
    selected_chip = 'News'
  } else if (chip === "article") {
    url = `/article/${id}`;
    path = "ReadingDetail";
    selected_chip = 'Articles'
  } else if (chip === "journal") {
    url = `/journal/${id}`;
    path = "ReadingDetail";
    selected_chip = 'Journals'
  } else if (chip === "course") {
    url = `/course/${id}`;
    path = "CourseDetails";
    selected_chip = ''
  } else if (chip === "job-listing") {
    url = `/job/${id}`;
    path = "CareerDetails";
    selected_chip = 'Job Listing'
  } else if (chip === "internship") {
    url = `/internship/${id}`;
    path = "CareerDetails";
    selected_chip = 'Internship'
  } else if (chip === "hire-advocate") {
    url = `/freelancer/${id}`;
    path = "LawyerDetails";
    selected_chip = ''
  }
  return { url, path,selected_chip };
};
