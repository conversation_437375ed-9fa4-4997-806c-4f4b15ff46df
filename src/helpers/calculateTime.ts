export function calculateTimeDifference(timestamp: string): string {
    const givenTime = new Date(timestamp);
    const currentTime = new Date();
    const differenceInMs = currentTime.getTime() - givenTime.getTime();
    const minutes = Math.floor((differenceInMs / (1000 * 60)) % 60);
    const hours = Math.floor((differenceInMs / (1000 * 60 * 60)) % 24);
    const days = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));

    let result = '';

    if (days > 0) {
      result += `${days}d ago `;
    }
    if (hours > 0 && days < 1) {
      result += `${hours}h ago`;
    }
    if (minutes > 0 && hours < 1 && days < 1) {
      result += `${minutes}m ago`;
    }

    return result || 'just now';
  }

export const getTotalDuration=(item:any[])=>{
  let total_mins = 0;
  item?.map((topic:any)=>(
      total_mins += topic.total_lecture_duration
  ))
  return `${total_mins === 60 ? '1 hr' : total_mins < 60 ? `${total_mins} mins`: `${total_mins % 60} hrs`}`;
}  