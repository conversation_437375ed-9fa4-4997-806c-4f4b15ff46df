import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Platform } from 'react-native';

export const handleCertificateDownload = async (title: string, url: string) => {
    const filename = `${title}.pdf`;
    try {
        const result = await FileSystem.downloadAsync(url, FileSystem.documentDirectory + filename);
        const mimeType = result.headers["Content-Type"] || "application/pdf";
        if (Platform.OS === 'android') {
            await saveFile(result.uri, filename, mimeType);
        }
        else {
            Sharing.shareAsync(result.uri);
        }

    } catch (error) {
        console.log("Download error : ", error);
    }
};

async function saveFile(uri: string, filename: string, mimetype: string) {
    try {
        const permissions = await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();
        if (permissions.granted) {
            const base64 = await FileSystem.readAsStringAsync(uri, { encoding: FileSystem.EncodingType.Base64 });
            await FileSystem.StorageAccessFramework.createFileAsync(permissions.directoryUri, filename, mimetype)
                .then(async (newUri) => {
                    await FileSystem.writeAsStringAsync(newUri, base64, { encoding: FileSystem.EncodingType.Base64 });
                })
                .catch(e => console.log("File creation error : ", e));
        }

    } catch (error) {
        console.log("Save file error : ", error);
    }
}