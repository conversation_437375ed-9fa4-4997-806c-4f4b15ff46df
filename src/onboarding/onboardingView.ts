import AsyncStorage from '@react-native-async-storage/async-storage';

const ONBOARDING_KEY = '@onboardingShown';

export const checkOnboardingStatus = async () => {
	try {
		const value = await AsyncStorage.getItem(ONBOARDING_KEY);
		return value !== null;
	} catch (error) {
		console.log('Error checking onboarding status : ', error);
		return false;
	}
};

export const markOnboardingAsShown = async () => {
	try {
		await AsyncStorage.setItem(ONBOARDING_KEY, 'true');
	} catch (error) {
		console.log('Error marking onboarding as shown : ', error);
	}
};
