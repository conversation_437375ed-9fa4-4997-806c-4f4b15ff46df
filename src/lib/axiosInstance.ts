import axios, {InternalAxiosRequestConfig} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ClientAxiosInstance = axios.create({
  //@ts-ignore
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
  },
});

const FormAxiosInstance = axios.create({
  //@ts-ignore
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  timeout: 10000,
});

const setAuthHeader = async (
  config: InternalAxiosRequestConfig,
): Promise<InternalAxiosRequestConfig> => {
  const token = await AsyncStorage.getItem('LAWCUBE_TOKEN');
  if (token) {
    config.headers.set('Authorization', `Bearer ${token}`);
  }
  return config;
};

FormAxiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    return setAuthHeader(config);
  },
  error => {
    return Promise.reject(error);
  },
);

ClientAxiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    return setAuthHeader(config);
  },
  error => {
    return Promise.reject(error);
  },
);

export {FormAxiosInstance, ClientAxiosInstance};
