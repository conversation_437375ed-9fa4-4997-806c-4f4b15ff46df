import { Platform, Share } from "react-native";
import { getWishList } from "../helpers/getWishlistMenu";

const generateDeepLink = (currentChip: string, currentChipId: string) => {
	const baseUrl = 'https://www.lawcube.org/readings';
	const chip = getWishList(currentChip)
	return `${baseUrl}/${chip}/${currentChipId}`;
};

export const handleShare = async (currentChip: string, currentChipId: string, image: string) => {

	const link = generateDeepLink(currentChip, currentChipId);

	const options = {
		message: `Check out this ${currentChip} on Lawcube: ${link}`,
		title: 'Lawcube',
		subject: `Check out this ${currentChip} on Lawcube`,
		fallback: Platform.OS === 'android' ? 'https://play.google.com/store/apps/details' : 'https://apps.apple.com/in/app',
		uri: image,
        // dialogTitle: 'Share this reading',
        // url: image,
        // emailSubject: `Check out this ${currentChip} on Lawcube`,
        // emailBody: `I've read this ${currentChip} and thought you might enjoy it: ${link}`,
		// social: Platform.OS === 'ios'? Share.Social.WEBSITE : Share.Social.EMAIL,
        excludedActivityTypes: Platform.OS === 'ios'? ['com.apple.UIKit.activity.SEND'] : ['android.intent.action.SEND']
	};

	Share.share(options)
		.then((res) => {
			console.log('Shared successfully!', res);
		})
		.catch((err) => {
			if (err) {
				console.log('Error sharing : ', err);
			}
		});
};