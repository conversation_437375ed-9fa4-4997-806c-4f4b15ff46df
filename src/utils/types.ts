export interface User {
	name: any;
	_id: string;
	first_name: string;
	last_name: string;
	user_profile: string;
}

export interface Discussion {
	_id: string;
	discussion_title: string;
	createdby_user: User;
	participants: User[];
	messages: Message[];
	total_message_count: number;
	pinned: boolean;
	createdAt: string;
	user_participants: User[];
	admin_participants: User[];
	name: string;
	createdby_admin: User;
	replied_admins: User[];
	replied_users: User[];
}

export interface Message {
	_id: string;
	text: string;
	createdAt: Date;
	discussionId: string;
	senderType: string;
	senderId: string;
	user: {
		_id: string;
		name: string;
	};
}