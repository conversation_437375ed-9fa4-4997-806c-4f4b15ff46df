import * as React from "react"
import Svg, { Path } from "react-native-svg"

function Participate(props) {
    return (
        <Svg
            width={41}
            height={41}
            viewBox="0 0 41 41"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M4.365 37.883v-.656c0-3.142.012-6.283-.012-9.424-.004-.517.149-.646.643-.63 1.14.035 2.28.008 3.42.011 1.073.004 2.02.35 2.79 1.105 1.12 1.102 2.461 1.59 4.022 1.573 2.071-.022 4.143-.012 6.215-.007 1.012.003 1.62.585 1.563 1.467-.039.582-.368 1.012-.937 1.15-.292.07-.6.094-.902.095-2.338.008-4.676.004-7.013.004-.133 0-.267-.002-.4.002-.397.012-.674.204-.688.608-.015.44.267.654.692.656 1.369.006 2.74-.055 4.105.016 2.89.15 5.709-.284 8.534-.807 3.883-.718 7.782-1.353 11.675-2.017 1.065-.182 1.9.327 2.16 1.29.25.928-.263 1.841-1.269 2.18-2.86.966-5.726 1.916-8.59 2.871-2.25.752-4.492 1.536-6.756 2.246-3.21 1.007-6.43.977-9.648-.019-2.028-.628-4.023-1.403-6.162-1.597-.85-.078-1.706-.083-2.56-.116-.263-.01-.525-.002-.882-.002zM25.68 23.187H14.996c0-.297-.003-.575 0-.852.011-1.217-.064-2.442.054-3.648.28-2.834 2.635-4.894 5.402-4.828a5.333 5.333 0 015.215 5.045c.085 1.4.015 2.808.015 4.283zM11.023 23.176H.317C.383 21.43.274 19.699.55 18.03c.425-2.566 2.942-4.348 5.504-4.156 2.667.2 4.793 2.243 4.953 4.874.089 1.456.016 2.922.016 4.428zM40.31 23.192H29.684c-.014-.198-.036-.38-.036-.561-.004-1.142-.01-2.285-.001-3.427.02-2.975 2.381-5.342 5.322-5.346 2.905-.004 5.29 2.35 5.337 5.289.022 1.328.004 2.657.004 4.045z"
                fill="#FFD166"
            />
            <Path
                d="M32.432 13.049c-1.86.855-3.176 2.173-3.765 4.102-.218.715-.299 1.49-.33 2.242-.052 1.25-.014 2.502-.014 3.794h-1.377c0-.226.003-.447 0-.667-.02-1.407.075-2.828-.089-4.219-.276-2.34-1.596-4.018-3.689-5.085a5.634 5.634 0 01-.249-.135c-.014-.008-.02-.03-.056-.086.128-.137.268-.28.402-.43.937-1.046 1.19-2.271.886-3.614-.074-.323.01-.486.243-.67 1.813-1.433 4.689-1.43 6.463.032.132.108.265.338.245.491-.262 2.004-.089 2.596 1.33 4.245zM17.714 12.91c-.82.668-1.683 1.237-2.374 1.968-1.126 1.19-1.634 2.676-1.631 4.326.002 1.31 0 2.62 0 3.976h-1.35c-.008-.19-.024-.39-.024-.592-.003-1.142.028-2.286-.01-3.427-.085-2.638-1.307-4.592-3.612-5.868-.147-.082-.3-.151-.498-.25.117-.135.205-.248.304-.35 1.002-1.021 1.311-2.256 1.03-3.627-.084-.416.016-.624.33-.85 1.858-1.34 4.483-1.336 6.284.048.16.124.345.406.308.566-.372 1.643.095 3.004 1.243 4.08zM3.06 40.518c-.156.016-.266.037-.376.038-.739.003-1.478.001-2.265.001V24.53c.812 0 1.638-.005 2.463.01.069.002.17.135.195.225.036.124.017.264.017.397l-.001 14.786c0 .184-.02.37-.033.57zM8.394 9.924a2.66 2.66 0 01-2.697 2.657C4.216 12.573 3 11.344 3.027 9.882c.027-1.481 1.245-2.68 2.712-2.671 1.474.01 2.66 1.222 2.655 2.713zM32.28 9.894a2.688 2.688 0 012.683-2.684c1.46 0 2.686 1.237 2.682 2.702a2.69 2.69 0 01-2.7 2.67 2.655 2.655 0 01-2.666-2.688zM15.7 3.276a2.67 2.67 0 01-2.7 2.66 2.69 2.69 0 01-2.656-2.715 2.693 2.693 0 012.71-2.663c1.476.01 2.659 1.224 2.646 2.718zM24.973 3.249c.004-1.498 1.188-2.69 2.671-2.69a2.689 2.689 0 01.002 5.378 2.668 2.668 0 01-2.673-2.688zM17.676 9.925c0-1.51 1.223-2.736 2.708-2.716 1.472.02 2.647 1.24 2.63 2.732a2.664 2.664 0 01-2.655 2.64c-1.505.004-2.683-1.162-2.683-2.656z"
                fill="#FFD166"
            />
        </Svg>
    )
}

export default Participate
