import * as React from "react"
import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>lip<PERSON><PERSON> } from "react-native-svg"

function Trophy(props) {
  return (
    <Svg
      width={31}
      height={41}
      viewBox="0 0 41 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G clipPath="url(#clip0_349_3996)" fill="#FFD166">
        <Path d="M12.289 33.134c.52-.88 1.163-1.679 1.729-2.529.136-.206.386-.218.611-.224.372-.01.747-.023 1.117.008.316.027.489-.108.636-.357 1.059-1.793 1.426-3.735 1.323-5.782-.008-.166-.07-.245-.234-.31-2.196-.877-3.82-2.41-5.101-4.323-.171-.255-.36-.349-.67-.381-3.006-.316-5.67-1.358-7.722-3.62-1.239-1.367-2.039-2.976-2.57-4.718C.805 8.918.62 6.892.7 4.835c.044-1.114.71-1.75 1.85-1.752 2.088-.002 4.178-.01 6.267.01.355.003.463-.123.49-.433.046-.519.12-1.035.173-1.553.038-.374.224-.55.623-.55 7.027.006 14.053.005 21.08.008.503 0 .641.117.705.596.068.504.143 1.008.163 1.514.012.319.104.415.437.413 2.036-.015 4.074.038 6.108-.021 1.267-.037 2.051.63 2.081 1.995a19.76 19.76 0 01-.436 4.656c-.386 1.775-1.042 3.437-2.108 4.93-1.702 2.385-4.056 3.774-6.936 4.348-.571.114-1.153.189-1.733.247-.228.022-.322.15-.423.301-.785 1.19-1.72 2.245-2.874 3.11a9.432 9.432 0 01-2.236 1.262c-.246.096-.31.226-.318.464-.056 1.936.312 3.774 1.263 5.49.198.357.408.523.856.523 1.748-.002 1.266-.26 2.248 1.153.365.525.791 1.01 1.073 1.589-.12.007-.239.021-.358.021H12.686c-.132 0-.265-.014-.397-.022zm5.163-22.562v-.003c-.452 0-.904.005-1.356-.002-.293-.004-.576.011-.685.338-.11.331.133.502.362.665.743.525 1.483 1.055 2.237 1.566.215.146.255.285.172.52-.31.886-.6 1.78-.906 2.666-.093.266-.07.496.17.66.215.148.413.071.616-.076.778-.566 1.57-1.114 2.349-1.68.19-.137.327-.144.524.002.742.548 1.5 1.078 2.253 1.612.23.162.457.332.752.109.275-.208.188-.463.107-.71-.277-.84-.552-1.683-.856-2.516-.104-.286-.049-.454.198-.624.762-.523 1.51-1.064 2.265-1.595.204-.143.401-.301.305-.573-.098-.277-.327-.372-.63-.367-.931.014-1.863-.006-2.793.015-.295.007-.423-.093-.506-.36-.274-.883-.573-1.76-.859-2.639-.08-.244-.22-.431-.498-.43-.259 0-.4.176-.476.405-.021.062-.044.123-.064.185-.277.842-.56 1.682-.825 2.528-.07.222-.173.318-.42.31-.478-.018-.957-.006-1.436-.006zM6.587 6.352v-.004H4.553c-.544 0-.552.002-.507.547.099 1.197.31 2.374.733 3.505.932 2.493 2.554 4.323 5.214 5.137.12.037.272.164.363.053.089-.108-.016-.272-.061-.402-.443-1.272-.71-2.587-.896-3.911a30.432 30.432 0 01-.318-4.592c.002-.25-.07-.345-.34-.339-.717.016-1.435.006-2.154.006zm28.196.032c-.705 0-1.412.026-2.115-.011-.332-.018-.396.068-.4.374-.037 2.926-.328 5.819-1.267 8.62-.023.07-.045.168-.013.221.063.107.167.026.25.002 1.544-.458 2.848-1.287 3.848-2.524 1.491-1.846 2.038-4.026 2.237-6.32.027-.31-.099-.373-.384-.367-.718.016-1.437.005-2.156.005z" />
        <Path d="M12.288 33.134c.133.008.265.022.398.022h16.009c.12 0 .238-.014.357-.021l3.153.014c.58.002.695.113.696.69v9.074c0 .514-.134.644-.667.644H9.12c-.546 0-.677-.133-.677-.676v-9.034c0-.583.122-.694.733-.697l3.112-.016zm8.402 2.826l-8.742.001c-.482 0-.626.136-.627.597-.002 1.186-.002 2.372.002 3.559.001.5.127.626.635.626 5.814.002 11.629.003 17.443.002.499 0 .633-.142.634-.639.002-1.147.001-2.294 0-3.441 0-.596-.112-.705-.723-.705H20.69z" />
      </G>
      <Defs>
        <ClipPath id="clip0_349_3996">
          <Path
            fill="#fff"
            transform="translate(.683 .557)"
            d="M0 0H40V43H0z"
          />
        </ClipPath>
      </Defs>
    </Svg>
  )
}

export default Trophy
