import * as React from "react"
import Svg, { <PERSON>, <PERSON>, De<PERSON>, ClipPath } from "react-native-svg"

export default function HireLawyer1(props) {
    return (
        <Svg
            width={25}
            height={26}
            viewBox="0 0 25 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <G clipPath="url(#clip0_1099_1791)" fill="#FDD066">
                <Path d="M22.203 23.863H3.125v-.458-9.193c0-1.092.446-1.541 1.544-1.541h15.98c1.109 0 1.55.435 1.55 1.532v9.193l.004.467zm-9.506-2.737c1.04-.007 1.921-.9 1.924-1.946.003-1.047-.914-1.969-1.964-1.964-1.05.006-1.964.933-1.948 1.98a1.973 1.973 0 001.988 1.93zM17.363 5.432c-.008 2.546-2.165 4.635-4.78 4.628-2.523-.007-4.59-2.11-4.581-4.658.01-2.672 2.062-4.73 4.71-4.72 2.577.004 4.651 2.126 4.651 4.75zM12.689 24.782h8.159c.099-.006.198-.006.297 0 .225.025.371.153.366.451-.005.298-.16.412-.382.434-.074.007-.148 0-.223 0H4.439c-.098 0-.225.04-.29-.016-.125-.108-.306-.286-.295-.414.014-.164.179-.334.312-.447.075-.063.215-.015.326-.015l8.197.007z" />
                <Path d="M19.985 11.754H5.338l-.047-.09c.245-.171.468-.4.738-.507a89.452 89.452 0 013.204-1.179c.113-.04.291.018.408.09 2.026 1.185 4.05 1.182 6.07-.011.123-.076.27-.101.412-.072.993.338 1.988.678 2.965 1.06.344.135.64.393.958.595l-.061.114zM12.69 20.23a1.078 1.078 0 01-1.09-1.053 1.071 1.071 0 012.142 0 1.09 1.09 0 01-1.052 1.053z" />
            </G>
            <Defs>
                <ClipPath id="clip0_1099_1791">
                    <Path fill="#FDD066" transform="translate(0 .681)" d="M0 0H25V25H0z" />
                </ClipPath>
            </Defs>
        </Svg>
    )
}

export function HireLawyer2(props) {
    return (
        <Svg
            width={25}
            height={26}
            viewBox="0 0 25 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <G clipPath="url(#clip0_1099_1791)" fill="#fff">
                <Path d="M22.203 23.863H3.125v-.458-9.193c0-1.092.446-1.541 1.544-1.541h15.98c1.109 0 1.55.435 1.55 1.532v9.193l.004.467zm-9.506-2.737c1.04-.007 1.921-.9 1.924-1.946.003-1.047-.914-1.969-1.964-1.964-1.05.006-1.964.933-1.948 1.98a1.973 1.973 0 001.988 1.93zM17.363 5.432c-.008 2.546-2.165 4.635-4.78 4.628-2.523-.007-4.59-2.11-4.581-4.658.01-2.672 2.062-4.73 4.71-4.72 2.577.004 4.651 2.126 4.651 4.75zM12.689 24.782h8.159c.099-.006.198-.006.297 0 .225.025.371.153.366.451-.005.298-.16.412-.382.434-.074.007-.148 0-.223 0H4.439c-.098 0-.225.04-.29-.016-.125-.108-.306-.286-.295-.414.014-.164.179-.334.312-.447.075-.063.215-.015.326-.015l8.197.007z" />
                <Path d="M19.985 11.754H5.338l-.047-.09c.245-.171.468-.4.738-.507a89.452 89.452 0 013.204-1.179c.113-.04.291.018.408.09 2.026 1.185 4.05 1.182 6.07-.011.123-.076.27-.101.412-.072.993.338 1.988.678 2.965 1.06.344.135.64.393.958.595l-.061.114zM12.69 20.23a1.078 1.078 0 01-1.09-1.053 1.071 1.071 0 012.142 0 1.09 1.09 0 01-1.052 1.053z" />
            </G>
            <Defs>
                <ClipPath id="clip0_1099_1791">
                    <Path fill="#fff" transform="translate(0 .681)" d="M0 0H25V25H0z" />
                </ClipPath>
            </Defs>
        </Svg>
    )
}
