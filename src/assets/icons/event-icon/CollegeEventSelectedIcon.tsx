import * as React from "react"
import Svg, { <PERSON>, <PERSON> } from "react-native-svg"

function CollegeEventSelectedIcon(props:any) {
  return (
    <Svg
      width={21}
      height={22}
      viewBox="0 0 21 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#FDD066">
        <Path d="M7.918 21.497H4.674c-.473 0-.704-.226-.721-.695-.005-.097 0-.196 0-.295V8.751c-.16.06-.284.105-.408.155-.368.147-.709.028-.848-.3-.14-.33-.012-.653.366-.827 1.049-.483 2.098-.958 3.152-1.436 1.14-.518 2.278-1.04 3.422-1.55.197-.087.273-.186.27-.41-.014-1.06-.008-2.121-.005-3.182 0-.478.221-.696.703-.698.81-.004 1.618-.004 2.427 0 .48 0 .704.213.716.694.021.82.093 1.264-1.134 1.208-.489-.021-.98-.003-1.509-.003 0 .737-.009 1.443.012 2.15 0 .084.156.188.262.236 2.1.96 4.2 1.916 6.304 2.87.142.056.278.125.409.205a.584.584 0 01.202.778.618.618 0 01-.752.301c-.15-.052-.293-.126-.488-.213v11.854c0 .744-.168.914-.9.914h-3.059c-.006-.131-.015-.245-.015-.36v-4.035c0-.585-.205-.787-.781-.787h-3.64c-.516 0-.736.214-.738.733-.005 1.356 0 2.712 0 4.067l-.003.382zm5.157-10.455c0-1.429-1.158-2.596-2.572-2.599-1.426 0-2.59 1.18-2.58 2.624a2.576 2.576 0 105.152-.024v-.001zM2.692 21.497c-.712 0-1.399.01-2.086-.005-.39-.009-.602-.27-.603-.705v-5.213c0-.207-.007-.416 0-.622.018-.343.243-.598.567-.604.697-.014 1.395-.004 2.12-.004l.002 7.153zM18.295 21.497v-7.136c.094-.006.188-.019.282-.02h1.64c.575 0 .777.208.777.796v5.638c-.005.495-.228.721-.721.721-.654.004-1.308.001-1.978.001z" />
      </G>
    </Svg>
  )
}

export default CollegeEventSelectedIcon;
