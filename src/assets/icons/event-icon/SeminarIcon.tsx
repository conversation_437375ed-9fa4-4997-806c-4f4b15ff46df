import * as React from "react"
import Svg, { <PERSON>, <PERSON> } from "react-native-svg"

export function SeminarIcon(props:any) {
  return (
    <Svg
      width={22}
      height={24}
      viewBox="0 0 22 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#2d2828">
        <Path d="M6.485 6.436C4.89 7.676 3.973 9.25 3.707 11.198c-.576-.198-.852-.593-1.399-1.972C1.064 9.255.183 8.5.028 7.404a2.064 2.064 0 011.35-2.254c1.038-.371 1.963.01 2.662 1.113.81-.278 1.616-.292 2.445.173zM13.347 18.367c-.037.905-.427 1.59-1.076 2.132.593 1.248.439 2.25-.437 2.94a2.089 2.089 0 01-2.542 0c-.88-.69-1.034-1.7-.445-2.923-.83-.884-1.08-1.403-1.039-2.143 2.594.689 2.744.738 5.539-.006zM7.802 5.675c-.018-.825.207-1.286 1.044-2.194-.587-1.187-.445-2.197.417-2.9a2.091 2.091 0 012.57-.021c.88.694 1.033 1.701.439 2.938.642.546 1.043 1.222 1.07 2.166-.919-.346-1.829-.56-2.778-.556a7.16 7.16 0 00-2.762.567zM14.658 6.45c.695-.441 1.309-.486 2.438-.187.32-.623.81-1.097 1.523-1.191a2.665 2.665 0 011.255.145c.89.343 1.379 1.316 1.22 2.257-.176 1.039-.921 1.612-2.3 1.752-.179.834-.553 1.551-1.37 2.018-.275-1.988-1.187-3.57-2.766-4.794zM2.333 14.783c.178-.856.558-1.558 1.365-2.026.277 1.989 1.187 3.569 2.787 4.808-.81.457-1.608.459-2.42.173-.937 1.201-1.816 1.527-2.835 1.057A2.118 2.118 0 01.031 16.51c.187-1.032 1.003-1.654 2.302-1.727zM17.09 17.728c-.823.297-1.622.293-2.448-.155 1.572-1.229 2.51-2.806 2.779-4.814.822.46 1.187 1.18 1.373 2.017 1.269-.03 2.151.73 2.3 1.829a2.062 2.062 0 01-1.394 2.264c-1.03.337-1.97-.061-2.61-1.141zM7.478 7.543c.167.085.328.143.46.241.232.173.451.36.658.56.185.182.37.244.584.075.077-.063.17-.152.184-.241.054-.347.074-.7.115-1.048.043-.37.264-.574.634-.585.309-.01.618-.01.928 0 .37.01.593.216.634.585.034.307.074.615.078.924 0 .26.153.346.362.44.223.1.316-.055.44-.155.222-.18.436-.37.658-.558.33-.27.626-.26.938.037.208.199.412.401.61.611.279.297.286.594.026.914-.217.264-.445.52-.66.782-.175.216-.023.614.25.65.337.045.678.07 1.016.108.426.049.62.264.627.695.004.277.005.555 0 .832-.008.411-.207.63-.611.677-.339.038-.68.063-1.018.108-.285.037-.437.438-.253.664.209.256.43.502.64.758.273.331.265.628-.028.937-.198.21-.402.413-.61.612-.298.282-.594.293-.915.033-.256-.209-.503-.429-.759-.638-.244-.2-.64-.06-.683.25-.047.336-.068.677-.106 1.016-.045.386-.266.593-.653.601-.297.008-.596.008-.896 0-.388-.01-.606-.215-.65-.604-.038-.328-.057-.658-.102-.985-.05-.354-.436-.49-.716-.257-.23.19-.455.386-.684.578-.37.314-.653.302-1.007-.043-.19-.186-.383-.37-.566-.564-.297-.31-.297-.605-.025-.938.212-.254.432-.502.64-.757.181-.223.029-.627-.257-.668-.338-.047-.679-.067-1.017-.105-.404-.046-.602-.266-.61-.678a22.395 22.395 0 010-.864c.01-.391.212-.613.594-.657.349-.04.7-.061 1.048-.11.271-.037.42-.44.248-.653-.215-.265-.445-.52-.66-.782-.262-.32-.254-.616.027-.913.201-.221.414-.431.636-.63.126-.106.29-.166.451-.255zm4.897 4.44a1.794 1.794 0 10-1.811 1.798 1.78 1.78 0 001.807-1.798h.004z" />
      </G>
    </Svg>
  )
}


export function SeminarIconLight(props:any) {
  return (
    <Svg
      width={22}
      height={24}
      viewBox="0 0 22 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#fff">
        <Path d="M6.485 6.436C4.89 7.676 3.973 9.25 3.707 11.198c-.576-.198-.852-.593-1.399-1.972C1.064 9.255.183 8.5.028 7.404a2.064 2.064 0 011.35-2.254c1.038-.371 1.963.01 2.662 1.113.81-.278 1.616-.292 2.445.173zM13.347 18.367c-.037.905-.427 1.59-1.076 2.132.593 1.248.439 2.25-.437 2.94a2.089 2.089 0 01-2.542 0c-.88-.69-1.034-1.7-.445-2.923-.83-.884-1.08-1.403-1.039-2.143 2.594.689 2.744.738 5.539-.006zM7.802 5.675c-.018-.825.207-1.286 1.044-2.194-.587-1.187-.445-2.197.417-2.9a2.091 2.091 0 012.57-.021c.88.694 1.033 1.701.439 2.938.642.546 1.043 1.222 1.07 2.166-.919-.346-1.829-.56-2.778-.556a7.16 7.16 0 00-2.762.567zM14.658 6.45c.695-.441 1.309-.486 2.438-.187.32-.623.81-1.097 1.523-1.191a2.665 2.665 0 011.255.145c.89.343 1.379 1.316 1.22 2.257-.176 1.039-.921 1.612-2.3 1.752-.179.834-.553 1.551-1.37 2.018-.275-1.988-1.187-3.57-2.766-4.794zM2.333 14.783c.178-.856.558-1.558 1.365-2.026.277 1.989 1.187 3.569 2.787 4.808-.81.457-1.608.459-2.42.173-.937 1.201-1.816 1.527-2.835 1.057A2.118 2.118 0 01.031 16.51c.187-1.032 1.003-1.654 2.302-1.727zM17.09 17.728c-.823.297-1.622.293-2.448-.155 1.572-1.229 2.51-2.806 2.779-4.814.822.46 1.187 1.18 1.373 2.017 1.269-.03 2.151.73 2.3 1.829a2.062 2.062 0 01-1.394 2.264c-1.03.337-1.97-.061-2.61-1.141zM7.478 7.543c.167.085.328.143.46.241.232.173.451.36.658.56.185.182.37.244.584.075.077-.063.17-.152.184-.241.054-.347.074-.7.115-1.048.043-.37.264-.574.634-.585.309-.01.618-.01.928 0 .37.01.593.216.634.585.034.307.074.615.078.924 0 .26.153.346.362.44.223.1.316-.055.44-.155.222-.18.436-.37.658-.558.33-.27.626-.26.938.037.208.199.412.401.61.611.279.297.286.594.026.914-.217.264-.445.52-.66.782-.175.216-.023.614.25.65.337.045.678.07 1.016.108.426.049.62.264.627.695.004.277.005.555 0 .832-.008.411-.207.63-.611.677-.339.038-.68.063-1.018.108-.285.037-.437.438-.253.664.209.256.43.502.64.758.273.331.265.628-.028.937-.198.21-.402.413-.61.612-.298.282-.594.293-.915.033-.256-.209-.503-.429-.759-.638-.244-.2-.64-.06-.683.25-.047.336-.068.677-.106 1.016-.045.386-.266.593-.653.601-.297.008-.596.008-.896 0-.388-.01-.606-.215-.65-.604-.038-.328-.057-.658-.102-.985-.05-.354-.436-.49-.716-.257-.23.19-.455.386-.684.578-.37.314-.653.302-1.007-.043-.19-.186-.383-.37-.566-.564-.297-.31-.297-.605-.025-.938.212-.254.432-.502.64-.757.181-.223.029-.627-.257-.668-.338-.047-.679-.067-1.017-.105-.404-.046-.602-.266-.61-.678a22.395 22.395 0 010-.864c.01-.391.212-.613.594-.657.349-.04.7-.061 1.048-.11.271-.037.42-.44.248-.653-.215-.265-.445-.52-.66-.782-.262-.32-.254-.616.027-.913.201-.221.414-.431.636-.63.126-.106.29-.166.451-.255zm4.897 4.44a1.794 1.794 0 10-1.811 1.798 1.78 1.78 0 001.807-1.798h.004z" />
      </G>
    </Svg>
  )
}