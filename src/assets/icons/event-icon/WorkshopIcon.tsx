import * as React from "react"
import Svg, { <PERSON>, <PERSON> } from "react-native-svg"

export function WorkshopIcon(props:any) {
  return (
    <Svg
      width={21}
      height={20}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#2d2828">
        <Path d="M18.387 12.671c.4 0 .76-.005 1.12 0 .457.007.752.285.759.7.006.415-.273.706-.733.728H.935c-.615 0-.915-.224-.933-.685a.695.695 0 01.676-.745c.194-.01.389 0 .583-.003h.622V.933c0-.68.25-.933.934-.933h14.63c.687 0 .94.251.94.926v11.747-.002zM8.255 5.368H5.183c-.523 0-.827.265-.834.71-.007.445.315.753.843.754 2.022.004 4.045.004 6.067 0 .51 0 .877-.321.875-.742-.003-.42-.354-.721-.883-.722H8.255zM6.282 3.89c.401 0 .802.006 1.204 0 .414-.008.7-.259.747-.645.044-.357-.21-.751-.607-.77a29.785 29.785 0 00-2.675-.004c-.407.018-.628.37-.6.768.03.398.299.643.726.651.401.008.802.001 1.205.001z" />
        <Path d="M13.8 15.32l-.194-1.312c.458 0 .871-.012 1.283.011.077.005.197.138.218.233.078.35.125.707.186 1.075.55.064 1.245-.108 1.3.748.03.477-.234.665-1.043.783.102.622.205 1.237.303 1.856.033.178.054.359.062.54a.71.71 0 01-.644.74.725.725 0 01-.778-.604c-.136-.75-.274-1.5-.38-2.256-.033-.244-.1-.328-.35-.327-2.413.008-4.825.008-7.235 0-.247 0-.33.072-.365.32a59.074 59.074 0 01-.38 2.256.716.716 0 01-.772.614.689.689 0 01-.641-.778c.07-.64.19-1.276.289-1.914l.066-.45c-.823-.127-1.063-.31-1.053-.793.011-.514.251-.654 1.305-.737.057-.328.133-.666.17-1.011.023-.223.09-.324.33-.311.389.016.778 0 1.198 0l-.2 1.312 7.325.005z" />
      </G>
    </Svg>
  )
}


export function WorkshopIconLight(props:any) {
  return (
    <Svg
      width={21}
      height={20}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#fff">
        <Path d="M18.387 12.671c.4 0 .76-.005 1.12 0 .457.007.752.285.759.7.006.415-.273.706-.733.728H.935c-.615 0-.915-.224-.933-.685a.695.695 0 01.676-.745c.194-.01.389 0 .583-.003h.622V.933c0-.68.25-.933.934-.933h14.63c.687 0 .94.251.94.926v11.747-.002zM8.255 5.368H5.183c-.523 0-.827.265-.834.71-.007.445.315.753.843.754 2.022.004 4.045.004 6.067 0 .51 0 .877-.321.875-.742-.003-.42-.354-.721-.883-.722H8.255zM6.282 3.89c.401 0 .802.006 1.204 0 .414-.008.7-.259.747-.645.044-.357-.21-.751-.607-.77a29.785 29.785 0 00-2.675-.004c-.407.018-.628.37-.6.768.03.398.299.643.726.651.401.008.802.001 1.205.001z" />
        <Path d="M13.8 15.32l-.194-1.312c.458 0 .871-.012 1.283.011.077.005.197.138.218.233.078.35.125.707.186 1.075.55.064 1.245-.108 1.3.748.03.477-.234.665-1.043.783.102.622.205 1.237.303 1.856.033.178.054.359.062.54a.71.71 0 01-.644.74.725.725 0 01-.778-.604c-.136-.75-.274-1.5-.38-2.256-.033-.244-.1-.328-.35-.327-2.413.008-4.825.008-7.235 0-.247 0-.33.072-.365.32a59.074 59.074 0 01-.38 2.256.716.716 0 01-.772.614.689.689 0 01-.641-.778c.07-.64.19-1.276.289-1.914l.066-.45c-.823-.127-1.063-.31-1.053-.793.011-.514.251-.654 1.305-.737.057-.328.133-.666.17-1.011.023-.223.09-.324.33-.311.389.016.778 0 1.198 0l-.2 1.312 7.325.005z" />
      </G>
    </Svg>
  )
}