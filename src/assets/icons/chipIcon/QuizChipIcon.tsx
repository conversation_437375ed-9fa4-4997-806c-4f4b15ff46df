import * as React from "react"
import Svg, { Path } from "react-native-svg"

export function QuizChipIcon(props: any) {
  return (
    <Svg
      width={25}
      height={25}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M39.202 60.575v5.723c0 .301-.016.603 0 .903.08 1.067.833 1.82 1.773 1.786.976-.035 1.626-.766 1.626-1.893V50.577c0-1.625.922-2.972 2.302-3.416 2.326-.748 4.486.86 4.514 3.42.036 2.947.01 5.895.01 8.848v1.083h4.469c3.55 0 5.612 2.113 5.684 5.641.127 6.073-.924 11.879-3.611 17.357-.297.602-.614.867-1.344.86-4.603-.044-9.21-.015-13.81-.038-.341 0-.842-.114-.997-.352-1.77-2.709-3.611-5.389-5.177-8.224-1.566-2.835-2.156-6.022-2.15-9.287.005-3.725 2.85-6.276 6.711-5.894zM79.983 40.284c-.019 7.378-6.295 13.559-13.712 13.501-7.416-.058-13.543-6.222-13.523-13.599.02-7.5 6.183-13.648 13.666-13.635C73.85 26.565 80 32.79 79.983 40.284zm-19.449-3.368l3.612 3.328-3.527 3.26 2.384 2.408 3.368-3.494 3.25 3.496 2.476-2.367-3.547-3.406 3.512-3.25-2.432-2.482-3.286 3.585-3.287-3.51-2.523 2.432z"
        fill="#000"
      />
      <Path
        d="M48.726 44.15c-1.692-.66-3.303-.758-4.93-.18-2.742.972-4.528 3.485-4.591 6.483-.015.722-.035 1.444 0 2.167.032.541-.192.742-.685.845-7.367 1.537-14.807-3.442-16.138-10.813-1.324-7.324 3.691-14.567 11.015-15.902 7.752-1.413 15.098 4.174 15.927 12.058.188 1.8-.054 3.512-.598 5.343zm-7.82-9.709l-6.747 6.839-3.167-3.32-2.409 2.197 5.556 5.608 8.959-8.938-2.192-2.386z"
        fill="#000"
      />
    </Svg>
  )
}


export function QuizChipIconLight(props: any) {
  return (
    <Svg
      width={25}
      height={25}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M39.202 60.575v5.723c0 .301-.016.603 0 .903.08 1.067.833 1.82 1.773 1.786.976-.035 1.626-.766 1.626-1.893V50.577c0-1.625.922-2.972 2.302-3.416 2.326-.748 4.486.86 4.514 3.42.036 2.947.01 5.895.01 8.848v1.083h4.469c3.55 0 5.612 2.113 5.684 5.641.127 6.073-.924 11.879-3.611 17.357-.297.602-.614.867-1.344.86-4.603-.044-9.21-.015-13.81-.038-.341 0-.842-.114-.997-.352-1.77-2.709-3.611-5.389-5.177-8.224-1.566-2.835-2.156-6.022-2.15-9.287.005-3.725 2.85-6.276 6.711-5.894zM79.983 40.284c-.019 7.378-6.295 13.559-13.712 13.501-7.416-.058-13.543-6.222-13.523-13.599.02-7.5 6.183-13.648 13.666-13.635C73.85 26.565 80 32.79 79.983 40.284zm-19.449-3.368l3.612 3.328-3.527 3.26 2.384 2.408 3.368-3.494 3.25 3.496 2.476-2.367-3.547-3.406 3.512-3.25-2.432-2.482-3.286 3.585-3.287-3.51-2.523 2.432z"
        fill="#fff"
      />
      <Path
        d="M48.726 44.15c-1.692-.66-3.303-.758-4.93-.18-2.742.972-4.528 3.485-4.591 6.483-.015.722-.035 1.444 0 2.167.032.541-.192.742-.685.845-7.367 1.537-14.807-3.442-16.138-10.813-1.324-7.324 3.691-14.567 11.015-15.902 7.752-1.413 15.098 4.174 15.927 12.058.188 1.8-.054 3.512-.598 5.343zm-7.82-9.709l-6.747 6.839-3.167-3.32-2.409 2.197 5.556 5.608 8.959-8.938-2.192-2.386z"
        fill="#fff"
      />
    </Svg>
  )
}
