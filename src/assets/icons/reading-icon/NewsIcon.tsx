import * as React from "react"
import Svg, { Path } from "react-native-svg"

export function NewsIcon(props:any) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 30 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M21.229 8.704a.796.796 0 00-.231-.562.787.787 0 00-.559-.232H.797c-.21 0-.41.083-.558.232a.797.797 0 00-.231.562v11.435a3.64 3.64 0 001.049 2.571 3.598 3.598 0 002.55 1.066h19.09a5.241 5.241 0 01-1.468-3.621V8.704zm-19.2 3.176a.787.787 0 01.79-.794h7.452a.797.797 0 01.79.794v6.353c0 .21-.084.413-.232.562a.787.787 0 01-.558.232H2.818a.787.787 0 01-.558-.232.796.796 0 01-.231-.562V11.88zm16.137 10.308H2.818a.787.787 0 01-.558-.233.796.796 0 01.558-1.355h15.348c.21 0 .41.083.558.232a.797.797 0 01-.558 1.356zm0-3.177h-4.737a.787.787 0 01-.558-.232.797.797 0 01.558-1.356h4.737a.797.797 0 010 1.588zm0-3.176h-4.737a.787.787 0 01-.558-.233.797.797 0 01.558-1.355h4.737c.21 0 .41.083.558.232a.797.797 0 01-.558 1.356zm0-3.177h-4.737a.787.787 0 01-.558-.232.797.797 0 01.558-1.356h4.737c.21 0 .41.084.558.233a.797.797 0 010 1.123.787.787 0 01-.558.232zm-8.684 4.765H3.608v-4.765h5.874v4.765zM30.008.794L29.88 20.14a3.642 3.642 0 01-.963 2.714A3.6 3.6 0 0126.289 24a3.581 3.581 0 01-2.628-1.147 3.627 3.627 0 01-.964-2.714l.11-13.071a.797.797 0 00-.23-.562.787.787 0 00-.559-.232H8.787V.794c0-.21.083-.412.231-.561A.787.787 0 019.576 0h19.642a.797.797 0 01.79.794z"
        fill="#2d2828"
      />
    </Svg>
  )
}


export function NewsIconLight(props:any) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 30 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M21.229 8.704a.796.796 0 00-.231-.562.787.787 0 00-.559-.232H.797c-.21 0-.41.083-.558.232a.797.797 0 00-.231.562v11.435a3.64 3.64 0 001.049 2.571 3.598 3.598 0 002.55 1.066h19.09a5.241 5.241 0 01-1.468-3.621V8.704zm-19.2 3.176a.787.787 0 01.79-.794h7.452a.797.797 0 01.79.794v6.353c0 .21-.084.413-.232.562a.787.787 0 01-.558.232H2.818a.787.787 0 01-.558-.232.796.796 0 01-.231-.562V11.88zm16.137 10.308H2.818a.787.787 0 01-.558-.233.796.796 0 01.558-1.355h15.348c.21 0 .41.083.558.232a.797.797 0 01-.558 1.356zm0-3.177h-4.737a.787.787 0 01-.558-.232.797.797 0 01.558-1.356h4.737a.797.797 0 010 1.588zm0-3.176h-4.737a.787.787 0 01-.558-.233.797.797 0 01.558-1.355h4.737c.21 0 .41.083.558.232a.797.797 0 01-.558 1.356zm0-3.177h-4.737a.787.787 0 01-.558-.232.797.797 0 01.558-1.356h4.737c.21 0 .41.084.558.233a.797.797 0 010 1.123.787.787 0 01-.558.232zm-8.684 4.765H3.608v-4.765h5.874v4.765zM30.008.794L29.88 20.14a3.642 3.642 0 01-.963 2.714A3.6 3.6 0 0126.289 24a3.581 3.581 0 01-2.628-1.147 3.627 3.627 0 01-.964-2.714l.11-13.071a.797.797 0 00-.23-.562.787.787 0 00-.559-.232H8.787V.794c0-.21.083-.412.231-.561A.787.787 0 019.576 0h19.642a.797.797 0 01.79.794z"
        fill="#fff"
      />
    </Svg>
  )
}
