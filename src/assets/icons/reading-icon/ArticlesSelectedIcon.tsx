import * as React from "react"
import Svg, { Path } from "react-native-svg"

function ArticlesSelectedIcon(props:any) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 29 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M28.244.401a1.94 1.94 0 00-1.65-.343L16.02 2.713V24l11.552-2.894a1.913 1.913 0 001.039-.668c.26-.33.4-.735.397-1.152V1.863a1.83 1.83 0 00-.209-.817A1.877 1.877 0 0028.244.4zm-15.28 2.312L2.362.058A1.986 1.986 0 00.74.401a1.874 1.874 0 00-.545.662 1.828 1.828 0 00-.188.83v17.393c0 .414.142.816.402 1.143.26.327.624.56 1.034.662L12.964 24V2.713z"
        fill="#FDD066"
      />
    </Svg>
  )
}

export default ArticlesSelectedIcon;
