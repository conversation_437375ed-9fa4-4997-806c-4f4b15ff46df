import * as React from "react"
import Svg, { Path } from "react-native-svg"

export function BlogIcon(props:any) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 23 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 6.72v10.56C0 20.99 3.006 24 6.708 24h9.584C19.995 24 23 20.99 23 17.28v-5.76c0-1.06-.858-1.92-1.917-1.92h-2.875V6.72C18.208 3.011 15.203 0 11.5 0H6.708C3.006 0 0 3.011 0 6.72zM18.208 16.8a1.44 1.44 0 00-1.437-1.44H6.229a1.437 1.437 0 00-1.4 1.44 1.442 1.442 0 001.4 1.44h10.542c.794 0 1.437-.645 1.437-1.44zm-4.791-9.6c0-.795-.644-1.44-1.438-1.44H6.23a1.437 1.437 0 00-1.4 1.44 1.442 1.442 0 001.4 1.44h5.75c.794 0 1.438-.645 1.438-1.44z"
        fill="#2d2828"
      />
    </Svg>
  )
}


export function BlogIconLight(props:any) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 23 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 6.72v10.56C0 20.99 3.006 24 6.708 24h9.584C19.995 24 23 20.99 23 17.28v-5.76c0-1.06-.858-1.92-1.917-1.92h-2.875V6.72C18.208 3.011 15.203 0 11.5 0H6.708C3.006 0 0 3.011 0 6.72zM18.208 16.8a1.44 1.44 0 00-1.437-1.44H6.229a1.437 1.437 0 00-1.4 1.44 1.442 1.442 0 001.4 1.44h10.542c.794 0 1.437-.645 1.437-1.44zm-4.791-9.6c0-.795-.644-1.44-1.438-1.44H6.23a1.437 1.437 0 00-1.4 1.44 1.442 1.442 0 001.4 1.44h5.75c.794 0 1.438-.645 1.438-1.44z"
        fill="#fff"
      />
    </Svg>
  )
}