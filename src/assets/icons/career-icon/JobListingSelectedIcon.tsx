import * as React from "react"
import Svg, { <PERSON>, <PERSON> } from "react-native-svg"

function JobListingSelectedIcon(props:any) {
  return (
    <Svg
      width={23}
      height={20}
      viewBox="0 0 23 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#FDD066">
        <Path d="M22.776 3.656l-1.428 4.247c-.308.915-.603 1.836-.928 2.743-.318.878-1 1.34-1.928 1.347-1.095.005-2.19 0-3.284 0h-.428c0 .213.004.396 0 .581-.014.475-.28.762-.75.764-1.738.008-3.475.008-5.212 0-.47 0-.737-.288-.75-.762-.005-.177 0-.355 0-.583H4.463c-1.087 0-1.762-.48-2.108-1.506C1.63 8.332.907 6.177.188 4.021A1.061 1.061 0 000 3.659V17.78c0 1.371.837 2.217 2.213 2.218 6.14.003 12.282.003 18.425 0 1.377 0 2.214-.847 2.214-2.218V3.663l-.076-.007z" />
        <Path d="M1.145 2.698l1.136 3.39c.446 1.327.892 2.655 1.34 3.982.153.454.328.584.805.585h3.32c.092 0 .184-.01.312-.016 0-.204-.006-.39 0-.576.02-.446.275-.705.714-.709 1.761-.004 3.522-.004 5.283 0 .44 0 .696.26.714.71.009.185 0 .372 0 .592h2.136c.524 0 1.047-.009 1.571 0 .364.008.6-.17.706-.486.823-2.442 1.64-4.887 2.451-7.335.01-.043.018-.088.023-.132h-5.513c-.018-.292-.033-.558-.052-.823-.072-.978-.818-1.848-2.087-1.864-1.725-.02-3.45-.023-5.175 0-1.26.019-2.07.857-2.113 2.109-.006.175 0 .35 0 .567H1.147l-.002.006zm6.913-.019c0-.203-.005-.389 0-.575.015-.47.286-.751.758-.753 1.734-.007 3.468-.007 5.202 0 .457 0 .731.277.751.724.01.196 0 .394 0 .604H8.06zM13.39 10.67H9.44v1.295h3.95V10.67z" />
      </G>
    </Svg>
  )
}

export default JobListingSelectedIcon;
