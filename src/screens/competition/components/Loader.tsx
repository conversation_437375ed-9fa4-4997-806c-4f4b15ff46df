import { View, Dimensions } from 'react-native'
import React, { useContext } from 'react'

import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'
import { LinearGradient } from 'expo-linear-gradient';
import { ClientContext } from '../../../context/ClientContext';
import { CompetitionContext } from '../../../context/CompetitionContext'
import { Skeleton } from '@rneui/themed'
import { widthPercentageToDP } from 'react-native-responsive-screen';

const { width } = Dimensions.get('window');

const Loader = () => {

    const { colorScheme, isLarge } = useContext(ClientContext);
    const { selectedChip } = useContext(CompetitionContext);

    return (

        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='space-y-3'>

                <View style={{ height: isLarge? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave'
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                </View>

                <View className='flex-row items-start justify-between'>

                    <View className='w-[55%] space-y-2'>
                        <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                            height: 15,
                            borderRadius: 2,
                            width: '85%'
                        }} />
                        <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                            height: 15,
                            borderRadius: 2,
                            width: '60%'
                        }} />
                    </View>

                    <View className='w-[42%] space-y-3'>

                        {(selectedChip === 'Essay Writing' || selectedChip === 'Article Writing') ? null : <View className='flex-row items-center space-x-2'>
                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                height: 15,
                                borderRadius: 2,
                                width: '70%'
                            }} />
                        </View>}

                        <View className='flex-row items-center space-x-2'>
                            {selectedChip === 'Essay Writing' || selectedChip === 'Article Writing' ?
                                <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} /> :
                                <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />}
                            <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                height: 15,
                                borderRadius: 2,
                                width: '70%'
                            }} />
                        </View>


                        {selectedChip === 'Moot Court' || selectedChip === 'Debate' ?
                            (<View className='flex-row items-center space-x-2'>
                                <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                    height: 15,
                                    borderRadius: 2,
                                    width: '70%'
                                }} />
                            </View>
                            ) : selectedChip === 'Essay Writing' || selectedChip === 'Article Writing' ? (
                                <View className='flex-row items-center space-x-2'>
                                    <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                        height: 15,
                                        borderRadius: 2,
                                        width: '70%'
                                    }} />
                                </View>
                            ) : null
                        }

                    </View>

                </View>

            </View>

            <View className='items-center w-full mb-2'>
                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                    height: 20,
                    borderRadius: 25,
                    width: '60%'
                }} />
            </View>

        </View>
    )
}

export default Loader;