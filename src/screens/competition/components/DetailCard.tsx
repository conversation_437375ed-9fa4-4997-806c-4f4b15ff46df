import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext} from 'react'
import { Image } from 'expo-image';
import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import Button from '../../../components/Button'
import { CompetitionContext } from '../../../context/CompetitionContext'
import { getDate, getQuizDate, getRegistrationEndTime, isExpired, isOngoing, isQuizExpired, isQuizOngoing, isWritingOngoing, separateTimeFromDateTime } from '../../../helpers/dateTimeFormat'
import Badge from '../../../components/Badge'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { capitalizeMode } from '../../../helpers/getCapitalize'

interface DetailCardProps {
    showConfirmation?: boolean;
    confirmationData?: any;
    data?: any;
    paymentCancelled?: boolean;
}

const DetailCard: React.FC<DetailCardProps> = ({ showConfirmation, confirmationData, data, paymentCancelled }) => {

    const { colorScheme, navigation, currentCompChip, registeredComps,isLarge } = useContext(ClientContext);
    const { selectedChip } = useContext(CompetitionContext);
    const { isCardSaved, saveCard, handleView, registeredItems } = useContext(LikeSaveContext);

    const item = confirmationData ? confirmationData : data;

    const handleQuiz = () => {
        if (selectedChip === 'Quiz') navigation.navigate('CompetitionDetails', { competitionId: item?._id });
        else navigation.navigate('CompetitionDetails', { currentCompetition: item });
        handleView(item?._id, currentCompChip);
    }

    const exploreMoreCompetition = () => {
        navigation.navigate('Competition');
    }

    const viewBookedCompetition = () => {
        navigation.navigate('Profile', {
            screen: 'ProfileHome',
            params: {
                from: 'Competition',
                to: currentCompChip
            }
        });
    }

    const isPaymentCompleted = (id: string) => {
        const isRegisteredNow = registeredItems?.has(id);
        const isRegisteredPreviously = registeredComps[0][selectedChip]?.includes(id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={showConfirmation ? undefined : handleQuiz}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                        blurRadius={selectedChip === 'Quiz' ?
                            isPaymentCompleted(item?._id)
                                ? isQuizExpired(item?.date_time || item?.submission_date, item?.end_time) ? 2 : 0
                                : isQuizExpired(item?.date_time || item?.submission_date, item?.end_time) ? 2 : 0
                            : isPaymentCompleted(item?._id)
                                ? isExpired(item?.date_time || item?.submission_date) ? 2 : 0
                                : isExpired(item?.date_time || item?.submission_date) ? 2 : 0
                        }
                    />
                    {!paymentCancelled && (
                        selectedChip !== 'Quiz' ?
                            <View className='absolute bottom-1 left-1'>
                                {isPaymentCompleted(item?._id) ?
                                    isExpired(item?.date_time || item?.submission_date)
                                        ? <Badge name='Completed' backgroundColor='#00000066' borderColor='#fff' color='#fff' />
                                        : (selectedChip === 'Debate' || selectedChip === 'Moot Court')
                                            ? isOngoing(item?.date_time)
                                                ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                                : <Badge name='Enrolled' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                                            : isWritingOngoing(item?.submission_date, item?.registration_end_date)
                                                ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                                : <Badge name='Enrolled' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                                    : isExpired(item?.date_time || item?.submission_date)
                                        ? <Badge name='Ended' backgroundColor='#FF2020' borderColor='#fff' color='#fff' />
                                        : (selectedChip === 'Debate' || selectedChip === 'Moot Court')
                                            ? isOngoing(item?.date_time)
                                                ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                                : null
                                            : isWritingOngoing(item?.submission_date, item?.registration_end_date)
                                                ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                                : null
                                }
                            </View> :
                            <View className='absolute bottom-1 left-1'>
                                {isPaymentCompleted(item?._id)
                                    ? isQuizExpired(item?.date_time, item?.end_time)
                                        ? <Badge name='Completed' backgroundColor='#00000066' borderColor='#fff' color='#fff' />
                                        : isQuizOngoing(item?.date_time, item?.start_time, item?.end_time)
                                            ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                            : <Badge name='Enrolled' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                                    : isQuizExpired(item?.date_time, item?.end_time)
                                        ? <Badge name='Ended' backgroundColor='#FF2020' borderColor='#fff' color='#fff' />
                                        : isQuizOngoing(item?.date_time, item?.start_time, item?.end_time)
                                            ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                            : null
                                }
                            </View>
                    )}
                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[52%]'><Badge height={30} name={currentCompChip} /></View>

                    <View className='w-[45%] h-full items-center'>
                        <Text
                            className='w-full leading-6 text-secondary dark:text-white text-wrap'
                            style={Global.text_medium}
                        >
                            {getRegistrationEndTime(item?.registration_end_date) === 'na'
                                ? 'Registeration closed'
                                : `Reg. ends on : ${getRegistrationEndTime(item?.registration_end_date)}`
                            }
                        </Text>
                    </View>

                </View>

                <View className='flex-row items-start justify-between'>

                    <View className='w-[52%] space-y-2'>
                        <Text
                            className='text-lg text-secondary dark:text-white'
                            style={Global.text_bold}
                            numberOfLines={2}
                            ellipsizeMode='tail'
                        >
                            {item?.title}
                        </Text>
                        <Text
                            className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                            style={Global.text_medium}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            By {item?.posted_by}
                        </Text>
                    </View>

                    <View className='w-[45%] space-y-3'>

                        {(selectedChip === 'Essay Writing' || selectedChip === 'Article Writing') ? null : <View className='flex-row items-center space-x-2'>
                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>{selectedChip === 'Quiz' ? getQuizDate(item?.date_time) : getDate(item?.date_time)}</Text>
                        </View>}

                        <View className='flex-row items-center space-x-2'>
                            {selectedChip === 'Essay Writing' || selectedChip === 'Article Writing' ?
                                <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} /> :
                                <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            }
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>
                                {selectedChip === 'Essay Writing' || selectedChip === 'Article Writing' ? getDate(item?.submission_date) : (item?.start_time || separateTimeFromDateTime(item?.date_time))}
                            </Text>
                        </View>

                        {selectedChip !== 'Quiz' &&
                            item?.mode?.toLowerCase() === 'offline' ? (
                            <View className='flex-row items-center space-x-2'>
                                <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>{capitalizeMode(item?.location)}</Text>
                            </View>
                        ) : selectedChip === 'Essay Writing' || selectedChip === 'Article Writing' ? (
                            <View className='flex-row items-center w-full space-x-2'>
                                <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>Online Submission</Text>
                            </View>
                        ) : selectedChip !== 'Quiz' && <View className='flex-row items-center w-full space-x-2'>
                            <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>Online</Text>
                        </View>}

                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full h-10'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, currentCompChip)}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

            {showConfirmation && <View>
                <Button
                    title='Explore more upcoming competitions'
                    onPress={exploreMoreCompetition}
                    borderColor='gray'
                    color={colorScheme === 'dark' ? "white" : 'black'}
                    bgColor='transparent'
                    height={57}
                />
            </View>}

            {paymentCancelled && <View>
                <Button
                    title='Back to competitions page'
                    onPress={exploreMoreCompetition}
                    height={57}
                />
            </View>}

            {showConfirmation && <View className='mb-2'>
                <Button
                    title='View booked competition details'
                    onPress={viewBookedCompetition}
                    height={57}
                />
            </View>}

        </View>
    )
}

export default DetailCard;