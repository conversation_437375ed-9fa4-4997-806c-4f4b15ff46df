import React, { useEffect, useRef } from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { TextInput } from 'react-native';

interface QuizInputProps {
    control: any;
    questionName: string;
}

const QuizInput = React.forwardRef<any, QuizInputProps>((props, ref) => {

    const { control, questionName } = props;
    const { getValues, setValue } = useFormContext();
    const inputRef = useRef(null);

    useEffect(() => {
        const currentValue = getValues(questionName);
        if (currentValue) {
            setValue(questionName, currentValue);
        } else {
            setValue(questionName, '');
        }
    }, [questionName, getValues, setValue]);

    return (
        <Controller
            control={control}
            name={questionName}
            render={({ field: { onChange, value } }) => (
                <TextInput
                    ref={inputRef}
                    value={value}
                    className='w-[90%] h-10 self-end border-b-[1.2px] text-secondary dark:text-white border-[#a8a8a8] text-text17 tracking-wide'
                    placeholder='Enter your answer'
                    placeholderTextColor='#a8a8a8'
                    onChangeText={(text) => onChange(text.toLowerCase())}
                    style={{
                        fontFamily: 'DMSans-Regular'
                    }}
                />
            )}
        />
    );
});

export default QuizInput;
