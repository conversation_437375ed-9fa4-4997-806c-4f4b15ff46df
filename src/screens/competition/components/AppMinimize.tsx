import { View, Text, TouchableOpacity, Modal } from 'react-native'
import React from 'react'
import Global from '../../../../globalStyle'
import Button from '../../../components/Button';

interface AppMinimizeProps {
    showAppConfirmation: boolean;
    handleAppClose: () => void;
}

const AppMinimize: React.FC<AppMinimizeProps> = ({ showAppConfirmation, handleAppClose }) => {
    return (
        <Modal statusBarTranslucent animationType='fade' transparent={true} visible={showAppConfirmation} onRequestClose={handleAppClose}>

            <View className='items-center justify-center w-full h-full bg-dark/70 dark:bg-dark/80'>

                <View className='w-[85%] px-5 py-6 bg-cardLight dark:bg-secondary rounded-md space-y-5'>

                    <Text
                        className='tracking-wide text-center text-secondary dark:text-white text-text16'
                        style={Global.text_bold}
                    >
                        Please stop minimizing the application. Further minimizing or closing the application may result in the submission of your quiz.
                    </Text>

                    <View className='flex-row items-center justify-center w-full'>

                        <Button
                            title='OK'
                            onPress={handleAppClose}
                            height={40}
                            paddingX={15}
                        />

                    </View>

                </View>

            </View>

        </Modal>
    )
}

export default AppMinimize