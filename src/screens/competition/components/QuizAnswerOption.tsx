import { Text, View } from "react-native";
import Global from "../../../../globalStyle";

const AnswerOption = ({ option, isFillups, isCorrectAnswer, isUserAnswer, noAnswer, isUserCorrect, userAnswer }: any) => {

    return (
        <View>
            {isFillups ? (
                <View className="px-2 w-full">
                    <View className='space-y-2 w-full'>
                        <Text className={`text-text16 tracking-wide ${noAnswer ? 'text-greycolor' : ''} ${isUserCorrect ? 'text-green-600' : ''} ${!noAnswer && !isUserCorrect ? 'text-red-500' : ''}`}
                            style={Global.text_regular}
                        >
                            {userAnswer || 'Not answered'}
                        </Text>
                        <View className={`h-[0.7px] w-full ${noAnswer ? 'bg-greycolor' : ''} ${isUserCorrect ? 'bg-green-600' : ''} ${!noAnswer && !isUserCorrect ? 'bg-red-500' : ''}`} />
                    </View>
                </View>
            ) : (
                <View className="px-2 w-full">
                    <View
                        className={`rounded-full items-center justify-center w-full min-h-[65px] py-2 px-4 border-[#a8a8a8] border-[0.7px] 
                            ${isCorrectAnswer ? 'bg-green-600' : ''}
                            ${isUserAnswer && !isUserCorrect ? 'bg-red-500' : ''}
                            `}
                    >
                        <Text className={`text-text16 tracking-wide text-center 
                            ${isCorrectAnswer ? 'text-white' : 'text-secondary dark:text-white'}
                            ${isUserAnswer && !isUserCorrect ? 'text-white' : ''}
                            `}
                            style={Global.text_regular}
                        >
                            {option?.text}
                        </Text>
                    </View>
                </View>
            )}
        </View>
    )
}

export default AnswerOption;