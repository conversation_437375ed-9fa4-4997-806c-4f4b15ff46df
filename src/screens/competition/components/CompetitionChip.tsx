import { View, Text, FlatList, Dimensions, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { CompetitionContext } from '../../../context/CompetitionContext';
import { QuizChipIcon, QuizChipIconLight } from '../../../assets/icons/chipIcon/QuizChipIcon';
import QuizSelectedIcon from '../../../assets/icons/chipIcon/QuizSelectedIcon';
import MootCourtSelected from '../../../assets/icons/chipIcon/MootCourtSelected';
import { MootCourtIcon, MootCourtIconLight } from '../../../assets/icons/chipIcon/MootCourtIcon';
import EssaySelectedIcon from '../../../assets/icons/chipIcon/EssaySelectedIcon';
import ArticleSelectedIcon from '../../../assets/icons/chipIcon/ArticleSelectedIcon';
import DebateSelectedIcon from '../../../assets/icons/chipIcon/DebateSelectedIcon';
import { EssayIcon, EssayIconLight } from '../../../assets/icons/chipIcon/EssayIcon';
import { ArticleIcon, ArticleIconLight } from '../../../assets/icons/chipIcon/ArticleIcon';
import { DebateIcon, DebateIconLight } from '../../../assets/icons/chipIcon/DebateIcon';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface CompetitionChipProps {
    handlePaginationChange: () => void;
}

const CompetitionChip: React.FC<CompetitionChipProps> = ({ handlePaginationChange }) => {

    const { setCurrentCompChip, colorScheme } = useContext(ClientContext);
    const { selectedChip, setSelectedChip } = useContext(CompetitionContext);

    const flatListRef = useRef<FlatList>(null);

    const defaultData = [
        { id: 1, title: 'Quiz', icon: selectedChip === 'Quiz' ? colorScheme === 'dark' ? <QuizChipIcon /> : <QuizSelectedIcon /> : colorScheme === 'dark' ? <QuizChipIconLight /> : <QuizChipIcon /> },
        { id: 2, title: 'Moot Court', icon: selectedChip === 'Moot Court' ? colorScheme === 'dark' ? <MootCourtIcon /> : <MootCourtSelected /> : colorScheme === 'dark' ? <MootCourtIconLight /> : <MootCourtIcon /> },
        { id: 3, title: 'Essay Writing', icon: selectedChip === 'Essay Writing' ? colorScheme === 'dark' ? <EssayIcon /> : <EssaySelectedIcon /> : colorScheme === 'dark' ? <EssayIconLight /> : <EssayIcon /> },
        { id: 4, title: 'Article Writing', icon: selectedChip === 'Article Writing' ? colorScheme === 'dark' ? <ArticleIcon /> : <ArticleSelectedIcon /> : colorScheme === 'dark' ? <ArticleIconLight /> : <ArticleIcon /> },
        { id: 5, title: 'Debate', icon: selectedChip === 'Debate' ? colorScheme === 'dark' ? <DebateIcon /> : <DebateSelectedIcon /> : colorScheme === 'dark' ? <DebateIconLight /> : <DebateIcon /> }
    ];

    const [data, setData] = useState(defaultData);

    useEffect(() => {
        if (selectedChip) {
            const selectedData = defaultData.find(item => item.title === selectedChip);
            const newData = defaultData.filter(item => item.title !== selectedChip);
            if (selectedData) {
                setData([selectedData, ...newData]);
            }
        } else {
            setData(defaultData);
        }
    }, [selectedChip]);

    const handleChipSelection = (chipId: number) => {
        setSelectedChip(defaultData[chipId - 1]?.title);
        setCurrentCompChip(defaultData[chipId - 1]?.title);
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
        handlePaginationChange();
    }

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => handleChipSelection(item.id)}
                    style={{
                        borderRadius: 25,
                        backgroundColor: selectedChip === item.title ? colorScheme === 'dark' ? '#fdd066' : '#2d2828' : 'transparent',
                        borderWidth: 0.8,
                        borderColor: selectedChip === item.title ? "transparent" : colorScheme === 'dark' ? "#fff" : "#2d2828"
                    }}
                    className='h-10 flex-row items-center justify-center space-x-2 px-[10px]'
                >
                    <View>
                        {item.icon}
                    </View>
                    <Text
                        style={Global.text_bold}
                        className={`${selectedChip === item.title ? 'text-primary dark:text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14`}
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        )
    };

    return (
        <View className='w-full'>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
}

export default CompetitionChip;
