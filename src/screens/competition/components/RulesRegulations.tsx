import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import HTMLView from 'react-native-htmlview'
import { styles } from '../../style/styles'
import { ClientContext } from '../../../context/ClientContext'

const RulesRegulations = ({ currentCompetition }: any) => {

    const { colorScheme } = useContext(ClientContext);

    return (
        <View className='w-full'>

            <View className='bg-primary py-5 rounded-t-md'>
                <Text className='text-secondary text-xl text-center' style={Global.text_bold}>Quiz Rules and Regulations</Text>
            </View>

            {!currentCompetition.guidelines ? (
                <View className='bg-cardLight dark:bg-secondary rounded-b-md space-y-5 py-10 px-primary'>

                    <View className='flex-row items-start space-x-1 mr-8'>
                        <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>1. </Text>
                        <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                            If a team buzzes in and cannot answer or answers incorrectly, the question is then posed to the other team.
                        </Text>
                    </View>

                    <View className='flex-row items-start space-x-1 mr-8'>
                        <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>2. </Text>
                        <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                            If neither team gives a correct answer, the host moves on to another question.
                        </Text>
                    </View>

                    <View className='flex-row items-start space-x-1 mr-8'>
                        <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>3. </Text>
                        <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                            This continues until a correct answer is given and one team is declared the winner.
                        </Text>
                    </View>

                    <View className='flex-row items-start space-x-1 mr-8'>
                        <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>4. </Text>
                        <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                            There are no point deductions for wrong answers.
                        </Text>
                    </View>

                </View>
            ) : (
                <HTMLView
                    value={currentCompetition?.guidelines}
                    addLineBreaks={true}
                    style={{
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#f8f6f3',
                        borderBottomLeftRadius: 5,
                        borderBottomRightRadius: 5,
                        paddingHorizontal: 10,
                        paddingVertical: 15
                    }}
                    stylesheet={styles(colorScheme)}
                />
            )}

        </View>
    )
}

export default RulesRegulations