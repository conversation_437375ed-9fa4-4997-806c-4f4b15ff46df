import { View, Text } from "react-native";
import React, { useContext, useEffect, useState } from "react";
import Global from "../../../../globalStyle";
import Button from "../../../components/Button";
import { ClientContext } from "../../../context/ClientContext";
import Entypo from "@expo/vector-icons/FontAwesome6";
import {
	getDate,
	getDateDay,
	getQuizDate,
	getTimeQuiz,
	isExpired,
	isQuizExpired,
	isRegistrationExpired,
	separateTimeFromDateTime,
} from "../../../helpers/dateTimeFormat";
import { LikeSaveContext } from "../../../context/LikeSaveContext";
import { ClientAxiosInstance } from "../../../lib/axiosInstance";
import { handleCertificateDownload } from "../../../helpers/downloadCertificate";
import { Feather } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";

interface RegisterPriceProps {
	currentCompetition: any;
	selectedChip: string;
	setFeedbackLoading: (value: boolean) => void;
	setIsFeedbackModalVisible: (value: boolean) => void;
	setFeedback: (data: any[]) => void;
	getFeedbackTypeId: (val: string) => string;
}

const RegisterPrice: React.FC<RegisterPriceProps> = ({ currentCompetition, selectedChip, getFeedbackTypeId, setFeedbackLoading, setIsFeedbackModalVisible, setFeedback }) => {

	const { navigation, registeredComps, userData, clientId, colorScheme } = useContext(ClientContext);
	const { registeredItems } = useContext(LikeSaveContext);

	const [quizEnded, setQuizEnded] = useState(false);
	const [quizAttended, setQuizAttended] = useState(false);
	const [serverTimeValid, setServerTimeValid] = useState<boolean | null>(null);
	const [currentQuiz, setCurrentQuiz] = useState<any>();

	const isQuizAttented = () => {
		const currentQuiz = userData?.attended_quizzes?.find(quiz => quiz?.quiz_id === currentCompetition?._id);
		if (currentQuiz) {
			setCurrentQuiz(currentQuiz);
			setQuizAttended(true);
		}
	}

	const handleQuizAnswers = async () => {
		navigation.navigate('QuizAnswers', { currentQuiz: currentQuiz, currentCompetition: currentCompetition, quizAttended: quizAttended });
	}

	const handleRegisteration = () => {
		navigation.navigate("CompetitionCheckout", { currentCompetition: currentCompetition });
	};

	const handleStartQuiz = async () => {
		navigation.navigate("QuizRules", { currentCompetition: currentCompetition });
	};

	const isPaymentCompleted = () => {
		const isRegisteredNow = registeredItems?.has(currentCompetition?._id);
		const isRegisteredPreviously = registeredComps[0][selectedChip]?.includes(currentCompetition?._id);
		return isRegisteredNow || isRegisteredPreviously;
	};

	const feedbackSubmited = async () => {

		const feedbackTypeId = getFeedbackTypeId(selectedChip);
		if (!feedbackTypeId) {
			console.error(`Invalid feedback type: ${selectedChip}`);
			return;
		}

		try {
			const response = await ClientAxiosInstance.get(`${feedbackTypeId}/${clientId}/${currentCompetition?._id}`);
			setIsFeedbackModalVisible(!response.data.data.is_feedback);

		} catch (error: any) {
			console.log("Checking feedback is submitted? : ", error.response.data);
			if (error.response.data.message.indexOf('Feedback not found for this user and') >= 0) setIsFeedbackModalVisible(true);
		}
	}



	const isQuizEnded = () => {
		const currentDate = new Date();

		if (
			!currentCompetition ||
			!currentCompetition.date_time ||
			!currentCompetition.start_time ||
			!currentCompetition.end_time
		) {
			return false;
		}

		const competitionDate = new Date(currentCompetition.date_time);

		const time1 = getTimeQuiz(currentCompetition.start_time);
		const time2 = getTimeQuiz(currentCompetition.end_time);
		const time3 = (currentDate.getHours() % 12) * 60 + currentDate.getMinutes();

		return (
			competitionDate.getFullYear() >= currentDate.getFullYear() &&
			competitionDate.getMonth() >= currentDate.getMonth() &&
			time1 <= time3 &&
			time2 >= time3
		);
	};

	useEffect(() => {
		const checkQuizStatus = () => {
			setQuizEnded(isQuizEnded());
		};

		checkQuizStatus();

		const interval = setInterval(() => {
			checkQuizStatus();
			if (isQuizEnded()) {
				clearInterval(interval);
			}
		}, 5000);

		return () => clearInterval(interval);
	}, [currentCompetition]);

	const [quizCertificateUrl, setQuizCertificateUrl] = useState('');
	const [quizScore, setQuizScore] = useState(0);

	const getQuizCertificate = async () => {

		const url = userData?.completed_quizes?.find((quiz: any) => quiz?.quiz_id === currentCompetition?._id)?.url;
		const isAttended = userData?.attended_quizzes?.find((quiz: any) => quiz?.quiz_id === currentCompetition?._id);
		setQuizScore(isAttended?.percentage_score);

		if (isAttended && !url) {
			try {
				const response = await ClientAxiosInstance.post(`/courseprogress/generate-quiz-certificate/${clientId}/${currentCompetition?._id}`, {});
				const responseData = response.data;
				setQuizCertificateUrl(responseData.url);

			} catch (error: any) {
				console.log('Get quiz certificate error : ', error.response.data);
			}
		} else {
			setQuizCertificateUrl(url || '');
		}
	}

	const getServerTime = async () => {
		try {
			const response = await ClientAxiosInstance(`/server-time`);

			const istOffset = 5.5 * 60 * 60 * 1000;

			const utcDate = new Date(response.data.serverTime);
			const istServerDate = new Date(utcDate.getTime() + istOffset);

			const currentUtcDate = new Date();
			const currentIstDate = new Date(currentUtcDate.getTime() + istOffset);

			const timeDifference = Math.abs(currentIstDate.getTime() - istServerDate.getTime());

			setServerTimeValid(timeDifference <= 2 * 60 * 1000);

		} catch (error) {
			console.log('Getting server time error: ', error);
			setServerTimeValid(null);
		}
	};

	useEffect(() => {
		if (selectedChip === "Quiz" && quizEnded && !quizAttended && isPaymentCompleted()) {
			getServerTime();
		}
	}, [selectedChip, quizEnded, quizAttended, isPaymentCompleted]);

	useEffect(() => {
		getQuizCertificate();
	}, [])

	useFocusEffect(
		React.useCallback(() => {
			isQuizAttented();
		}, [])
	);

	useEffect(() => {
		if (currentCompetition?._id) {
			if (selectedChip !== 'Quiz' &&
				isPaymentCompleted() &&
				isExpired(currentCompetition?.date_time || currentCompetition?.submission_date) &&
				isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time)
			) feedbackSubmited();
		}
	}, [currentCompetition?._id])

	const isCompetitionExpired = !isExpired(currentCompetition?.date_time || currentCompetition?.submission_date) ||
		!isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time);

	return (
		<View className="space-y-5">

			{selectedChip === 'Quiz' && isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time) &&
				isPaymentCompleted() ?
				<View className='flex-col justify-center w-full px-8 py-6 space-y-5 rounded-md bg-cardLight dark:bg-secondary'>

					{!quizAttended && <View className="h-10 w-10 items-center justify-center bg-[#FF2020] rounded-full self-center">
						<Feather name="x" size={28} color='#fff' />
					</View>}

					<Text style={Global.text_medium} className='text-lg tracking-wide text-center text-secondary dark:text-white'>{quizAttended ? 'Quiz has ended' : 'Quiz Ended, Not Attended'}</Text>

					{quizAttended && <View className="flex-row items-center justify-center space-x-1">
						<Text style={Global.text_regular} className='text-lg text-center text-secondary dark:text-white'>You have scored</Text>
						<Text style={Global.text_medium} className='text-lg text-center text-secondary dark:text-white'>
							({Number(quizScore) % 1 === 0 ? Number(quizScore) : quizScore?.toFixed(2)}/100)
						</Text>
					</View>}

					{quizAttended && <View>
						<Button
							title='Download Certificate'
							onPress={() => handleCertificateDownload(currentCompetition?.title, quizCertificateUrl)}
							height={57}
							paddingX={15}
							bgColor="transparent"
							borderColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
							color={colorScheme === 'dark' ? '#fff' : '#2d2828'}
						/>
					</View>}

					{quizAttended && <View>
						<Button
							title="View Answers"
							height={57}
							onPress={handleQuizAnswers}
						/>
					</View>}

					{!quizAttended && <View>
						<Button
							title="View Reults"
							height={57}
							onPress={handleQuizAnswers}
						/>
					</View>}

				</View>
				: selectedChip === 'Quiz' && isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time) &&
				<View className='flex-col justify-center w-full px-8 py-6 space-y-5 rounded-md bg-cardLight dark:bg-secondary'>

					<Text style={Global.text_medium} className='text-lg tracking-wide text-center text-secondary dark:text-white'>Quiz has ended</Text>

					<View>
						<Button
							title='Registration Closed'
							height={57}
							paddingX={15}
							bgColor="transparent"
							borderColor='#a8a8a8'
							color='#a8a8a8'
							disabled
						/>
					</View>

					<View>
						<Button
							title="View Reults"
							height={57}
							onPress={handleQuizAnswers}
						/>
					</View>

				</View>
			}

			{(isCompetitionExpired && !isPaymentCompleted()) &&
				<View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-secondary'>
					<Text style={Global.text_bold} className='text-xl tracking-wide text-secondary dark:text-white'>
						Price : &nbsp;
					</Text>
					<Text style={Global.text_bold} className='text-xl tracking-wide line-through text-secondary dark:text-white'>
						&#8377;{currentCompetition?.price}
					</Text>
					{currentCompetition?.discount_price ? <Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;&#8377;{currentCompetition?.discount_price}</Text> :
						<Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;Free</Text>
					}
					<Text style={Global.text_bold} className='tracking-wide text-secondary dark:text-white text-text17'>&nbsp;+ GST/-</Text>
				</View>
			}

			{selectedChip !== 'Quiz' && isExpired(currentCompetition?.date_time || currentCompetition?.submission_date) && !quizAttended &&
				<View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-darkcard'>
					<Text style={Global.text_medium} className='tracking-wide text-secondary dark:text-white text-text16'>
						The session has ended
					</Text>
				</View>
			}

			{!isPaymentCompleted() &&
				!isRegistrationExpired(currentCompetition?.registration_end_date) && (
					<View className="w-full">
						<Button
							title={currentCompetition?.discount_price > 0 ? 'Register Now' : 'Free Registration'}
							height={57}
							onPress={handleRegisteration}
						/>
					</View>
				)
			}

			{isPaymentCompleted() && !(isExpired(currentCompetition?.date_time) || isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time)) &&
				<View className="items-center py-6 space-y-4 rounded-md bg-cardLight dark:bg-secondary">

					<View className="h-12 w-12 bg-[#2baa16] rounded-full items-center justify-center">
						<Entypo name="check" size={30} color="white" />
					</View>

					<Text
						className="tracking-wide text-center text-text16 text-secondary dark:text-white"
						style={Global.text_regular}
					>
						Thank you for joining in this competition
					</Text>

					<View className="items-center">
						{selectedChip !== "Essay Writing" &&
							selectedChip !== "Article Writing" && (
								<Text
									className="tracking-wide text-text16 text-secondary dark:text-white"
									style={Global.text_medium}
								>
									{selectedChip} will start at&nbsp;
									{selectedChip === "Quiz"
										? getQuizDate(currentCompetition?.date_time)
										: getDate(currentCompetition?.date_time)}
								</Text>
							)
						}

						{selectedChip !== "Essay Writing" &&
							selectedChip !== "Article Writing" && (
								<Text
									className="tracking-wide text-text16 text-secondary dark:text-white"
									style={Global.text_medium}
								>
									{`Time : ${currentCompetition?.start_time ||
										separateTimeFromDateTime(currentCompetition?.date_time)
										}`}
								</Text>
							)
						}

						{(selectedChip === "Essay Writing" ||
							selectedChip === "Article Writing") && (
								<View className="flex-row items-center space-x-2">
									<Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
									<Text
										className="tracking-wide text-text16 text-secondary dark:text-white"
										style={Global.text_medium}
									>
										{getDateDay(currentCompetition?.submission_date)}
									</Text>
								</View>
							)
						}
					</View>

				</View>
			}

			{selectedChip === "Quiz" &&
				quizEnded &&
				!quizAttended &&
				isPaymentCompleted() &&
				(currentCompetition?.status === "active" || currentCompetition?.status === "Active") && (
					<View>
						{serverTimeValid === true ? (
							<Button title="Start Quiz" onPress={handleStartQuiz} height={57} />
						) : (
							<View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-secondary px-primary'>
								<Text style={Global.text_regular} className='tracking-wide text-center text-text16 text-secondary dark:text-white'>
									Your local time is not matching. Check your local time and try again.
								</Text>
							</View>
						)}
					</View>
				)
			}

		</View>
	);
};

export default RegisterPrice;
