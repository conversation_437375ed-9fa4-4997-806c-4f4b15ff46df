import { Dimensions, RefreshControl, ScrollView, Text, View } from 'react-native'
import React, { useContext, useRef } from 'react'
import CustomStatusBar from '../../components/CustomStatusBar'

import Header from '../../components/Header'
import Global from '../../../globalStyle'
import DetailCard from './components/DetailCard'
import Filters from '../../components/Filters'
import { CompetitionContext } from '../../context/CompetitionContext'

import Pagination from './components/Pagination';
import CompetitionChip from './components/CompetitionChip'
import { ClientContext } from '../../context/ClientContext'
import Loader from './components/Loader'
import { FilterContext } from '../../context/FilterContext'

const Competition = () => {

    const { selectedChip, showPagination, selectedChipData, loading, onRefresh, refreshing } = useContext(CompetitionContext);
    const { currentCompChip, isAndroid, isLarge, isMedium } = useContext(ClientContext);
    const { compLocation } = useContext(FilterContext);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Competition' index={1} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className='flex-1'
                ref={scrollViewRef}
                refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
                stickyHeaderIndices={[0]}
            >

                <View className='pt-3 pb-4 bg-cardLight dark:bg-secondary'>
                    <CompetitionChip handlePaginationChange={handlePaginationChange} />
                </View>

                <View className='bg-ececec'>
                    <Filters
                        categoryFilter
                        chipFrom={currentCompChip}
                        dateFilter
                        priceFilter
                        modeFilter={selectedChip === 'Quiz' ? false : true}
                        locationFilter={selectedChip === 'Quiz' ? false : compLocation.length > 0}
                        postedByFilter={selectedChip === 'Quiz' ? false : true}
                        competitionSort
                    />
                </View>

                <View className={`space-y-3 ${isMedium ? 'mb-28' : isAndroid ? 'mb-16' : 'mb-24'}`}>

                    <View className='w-full pt-4 pb-2 space-y-4 px-primary'>

                        <Text className='text-text20 text-secondary dark:text-white' style={Global.text_bold}>List Of {selectedChip}</Text>

                        <View className='space-y-[3%]'>
                            {loading ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {[1, 2, 3, 4, 5, 6].map((index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <Loader />
                                            </View>
                                        ))}
                                    </View>
                                ) : (
                                    [1, 2, 3,].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))
                                )
                            ) : selectedChipData?.length > 0 ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {selectedChipData?.map((item, index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <DetailCard data={item} />
                                            </View>
                                        ))}
                                    </View>
                                ) :
                                    selectedChipData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <DetailCard data={item} />
                                        </View>
                                    ))
                            ) : (
                                <View className='flex-row items-center justify-center w-full h-12'>
                                    <Text className='text-text17 text-secondary dark:text-white' style={Global.text_medium}>
                                        No {selectedChip} is available
                                    </Text>
                                </View>
                            )}
                        </View>

                    </View>

                    {showPagination && <View className='px-1'>
                        <Pagination onPageChange={handlePaginationChange} />
                    </View>}

                </View>

            </ScrollView>

        </View>
    )
}

export default Competition;