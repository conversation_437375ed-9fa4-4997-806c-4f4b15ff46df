import { View, Text, TouchableOpacity, ScrollView } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { Image } from 'expo-image';
import Ionicons from '@expo/vector-icons/Ionicons'

import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import Header from '../../../components/Header';
import CustomStatusBar from '../../../components/CustomStatusBar';
import DescriptionFaqs from '../components/DescriptionFaqs';
import RegisterPrice from '../components/RegisterPrice';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { getDateDay } from '../../../helpers/dateTimeFormat';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { Skeleton } from '@rneui/themed';
import { LinearGradient } from 'expo-linear-gradient';
import { GlobalContext } from '../../../context/GlobalProvider';
import Feedbacks from '../../../components/Feedbacks';
import GetFeedback from '../../../components/GetFeedback';

const CompetitonDetails = ({ route }: any) => {

    const { colorScheme, currentCompChip } = useContext(ClientContext);
    const { saveCard, isCardSaved } = useContext(LikeSaveContext);
    const { setLoading, loading } = useContext(GlobalContext);

    const [currentCompetition, setCurrentCompetetion] = useState<any>();
    const competitionId = route?.params?.competitionId || '';
    const competition = route?.params?.currentCompetition || {};

    const [feedback, setFeedback] = useState<any[]>([]);
    const [isFeedbackModalVisible, setIsFeedbackModalVisible] = useState(false);
    const [feedbackLoading, setFeedbackLoading] = useState(true);

    const fetchFeedback = async () => {

		const feedbackTypeId = getFeedbackTypeId(currentCompChip);
		if (!feedbackTypeId) {
			console.error(`Invalid feedback type: ${currentCompChip}`);
			return;
		}

		try {
			const response = await ClientAxiosInstance.get(
				`${feedbackTypeId}/searchApprovedFeedback?feedback_approval_status=Approved`
			);
			setFeedback(response.data.data);

		} catch (error) {
			console.error('Error fetching feedback:', error);
		} finally {
			setFeedbackLoading(false);
		}
	};

    const getFeedbackTypeId = (chip: string) => {
        const feedbackMapping: { [key: string]: string } = {
            'Quiz': 'quizfeedback',
            'Moot Court': 'mootcourtfeedback',
            'Essay Writing': 'essaywritingfeedback',
            'Article Writing': 'articlewritingfeedback',
            'Debate': 'debatefeedback',
        };
        return feedbackMapping[chip] || '';
    };

    const getCurrentCompetition = async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/quiz/${competitionId}?live=true`);
            setCurrentCompetetion(response.data.data);

        } catch (error: any) {
            console.log('Competition details error : ', error.response.data);
            setCurrentCompetetion({});

        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        if (competitionId) getCurrentCompetition();
        else setCurrentCompetetion(competition);
        fetchFeedback();
    }, [])

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name={currentCompChip} index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='w-full h-full py-3 mb-5 space-y-5'>

                    <View style={{ height: widthPercentageToDP(50) }} className='px-primary'>
                        {loading ?
                            <Skeleton
                                LinearGradientComponent={LinearGradient}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 5
                                }}
                            /> :
                            <Image
                                source={currentCompetition?.thumbnail_image_path ?
                                    { uri: currentCompetition?.thumbnail_image_path } :
                                    require('../../../assets/images/placeholder-thumbnail.png')
                                }
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 5
                                }}
                                contentFit='cover'
                            />}
                    </View>

                    <View className='px-primary'>

                        <Text style={Global.text_bold} className='text-text19 text-secondary dark:text-white'>{currentCompChip} Details</Text>

                        <View className='h-[60px] flex-row items-center justify-betweens'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Date</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>
                                    {loading ? '-' : getDateDay(currentCompetition?.submission_date || currentCompetition?.date_time)}
                                </Text>
                            </View>
                        </View>

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        {currentCompChip === 'Quiz' ? (
                            <View className='min-h-[60px] flex-row items-center justify-between py-2'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Quiz Title</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text
                                        style={Global.text_regular}
                                        className='tracking-wider text-text17 text-secondary dark:text-white'
                                    >
                                        {loading ? '-' : currentCompetition?.title}
                                    </Text>
                                </View>
                            </View>
                        ) : (
                            <View className='h-[60px] flex-row items-center justify-between'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Language</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>English</Text>
                                </View>
                            </View>
                        )}

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        {currentCompChip === 'Quiz' ? (
                            <View className='h-[60px] flex-row items-center justify-between'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Mode</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>Online</Text>
                                </View>
                            </View>
                        ) : (
                            <View className={`h-[60px] flex-row items-center justify-between`}>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Mode</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>{capitalizeMode(currentCompetition?.mode)}</Text>
                                </View>
                            </View>
                        )}

                        {(currentCompetition?.mode?.toLowerCase() === 'offline' || currentCompChip === 'Quiz') && <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />}

                        {currentCompChip === 'Quiz' ? (
                            <View className='h-[60px] flex-row items-center justify-between'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Start Time</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>{loading ? '-' : currentCompetition?.start_time}</Text>
                                </View>
                            </View>
                        ) : currentCompetition?.mode?.toLowerCase() === 'offline' ? (
                            <View className='h-[60px] flex-row items-center justify-between'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Location</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text style={Global.text_regular} className='tracking-wider text-text17 text-secondary dark:text-white'>{currentCompetition?.location}</Text>
                                </View>
                            </View>
                        ) : null}

                    </View>

                    {!loading && <View className='px-primary'>
                        <RegisterPrice
                            currentCompetition={currentCompetition}
                            selectedChip={currentCompChip}
                            setFeedback={setFeedback}
                            setIsFeedbackModalVisible={setIsFeedbackModalVisible}
                            getFeedbackTypeId={getFeedbackTypeId}
                            setFeedbackLoading={setFeedbackLoading}
                        />
                    </View>}

                    <View className='items-center w-full px-primary'>
                        <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(currentCompetition._id, currentCompChip)}>
                            <Ionicons
                                name={isCardSaved(currentCompetition?._id) ? 'heart' : 'heart-outline'}
                                size={25}
                                color={isCardSaved(currentCompetition?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                            />
                            <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(currentCompetition?._id) ? 'Saved' : 'Save for later'}</Text>
                        </TouchableOpacity>
                    </View>

                    <View className='px-primary'>
                        <DescriptionFaqs selectedChip={currentCompChip} currentCompetition={currentCompetition} />
                    </View>

                    <View className='w-full'>
                        <Feedbacks
                            data={feedback.reverse()}
                            selectedChip={currentCompChip}
                            loading={feedbackLoading}
                        />
                    </View>

                </View>

            </ScrollView>

            <GetFeedback
                visible={isFeedbackModalVisible}
                onClose={() => setIsFeedbackModalVisible(false)}
                chip_id={currentCompetition?._id}
                feedback_for={getFeedbackTypeId(currentCompChip)}
            />

        </View>
    )
}

export default CompetitonDetails