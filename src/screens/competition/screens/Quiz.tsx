import { View, Text, ScrollView, TouchableOpacity, BackHandler, AppState } from 'react-native'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import { ClientContext } from '../../../context/ClientContext';
import QuestionCard from '../components/QuestionCard';
import { Image } from 'expo-image';
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import Global from '../../../../globalStyle';
import { FormProvider, useForm } from 'react-hook-form';
import Button from '../../../components/Button';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import QuizWarning from '../components/QuizWarning';
import { formatDuration, getTimeQuiz } from '../../../helpers/dateTimeFormat';
import { Toast } from '../../../components/Toast';
import AppMinimize from '../components/AppMinimize';
import { GlobalContext } from '../../../context/GlobalProvider';
import { usePreventScreenCapture } from 'expo-screen-capture';
import { useFocusEffect } from '@react-navigation/native';

const Quiz = ({ route }: any) => {

    usePreventScreenCapture();
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const currentCompetition = route?.params?.currentCompetition || {};

    const { navigation, getClientData } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const [questionIndex, setQuestionIndex] = useState(0);
    const [totalDurationTaken, setTotalDurationTaken] = useState(0);

    const [totalDuration, setTotalDuration] = useState(0);
    const [showConfirmation, setShowConfirmation] = useState(false);

    const appState = useRef(AppState.currentState);
    const [showAppConfirmation, setShowAppConfirmation] = useState(false);
    const [appStateVisible, setAppStateVisible] = useState(0);

    const methods = useForm();

    const getTotalDuration = () => {
        const start = getTimeQuiz(currentCompetition?.start_time);
        const end = getTimeQuiz(currentCompetition?.end_time);
        const current = new Date();
        const currentTime = (current.getHours() % 12) * 60 + current.getMinutes();

        const totalDurationMinutes = end - start;
        const elapsedDurationMinutes = currentTime - start;
        const totalDurationSeconds = (totalDurationMinutes - elapsedDurationMinutes) * 60;

        setTotalDuration(totalDurationSeconds);
    }

    const startTimer = () => {
        if (totalDuration > 0) {
            timerRef.current = setInterval(() => {
                setTotalDuration(prevDuration => {
                    if (prevDuration <= 1) {
                        clearInterval(timerRef.current!);
                        onSubmit(methods.getValues());
                        return 0;
                    }
                    return prevDuration - 1;
                });
                setTotalDurationTaken(prevDuration => prevDuration + 1);
            }, 1000);
        }
    };

    const stopTimer = () => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };


    const handleNextQuestion = () => {
        if (currentCompetition?.question_id?.length > questionIndex + 1 && questionIndex < currentCompetition?.question_id?.length) {
            setQuestionIndex(questionIndex + 1);
        }
    }

    const handlePreviousQuestion = () => {
        if (currentCompetition?.question_id?.length >= questionIndex + 1 && questionIndex > 0) {
            setQuestionIndex(questionIndex - 1);
        }
    }

    const onSubmit = async (data: any) => {
        stopTimer();
        try {
            setLoading(true);

            const formattedData = Object.keys(data).map(questionId => ({
                questionId: questionId,
                selectedAnswer: data[questionId]?.trim()
            }));

            const response = await ClientAxiosInstance.post(`/auth/attend/${currentCompetition?._id}`, {
                answers: formattedData,
                quiz_attend_time_duration: formatDuration(totalDurationTaken)
            });

            await getClientData();
            setLoading(false);
            await navigation.navigate('QuizResult', { currentCompetition: currentCompetition, appStateVisible: appStateVisible });

            Toast.show({
                type: 'success',
                message: 'Quiz submitted successfully',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/images/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

        }
        catch (error: any) {
            console.log("Error while submitting quiz : ", error.response.data.message);
            if (error.response.data.message === 'The user already attended the quiz') {
                navigation.navigate('CompetitionDetails', { currentCompetition: currentCompetition });
                Toast.show({
                    type: 'error',
                    message: 'You have already attended the quiz',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/images/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }
            setLoading(false);
        }
    };

    useFocusEffect(
        useCallback(() => {
            startTimer();
            return () => stopTimer();
        }, [totalDuration, methods])
    );

    useEffect(() => {
        const subscription = AppState.addEventListener('change', nextAppState => {
            if (
                appState.current.match(/inactive|background/) &&
                nextAppState === 'active'
            ) {
                console.log('App is in foreground');
            }
            else if (nextAppState.match(/inactive|background/)) {
                setAppStateVisible((prev) => {
                    const updated = prev + 1;
                    if (updated === 1) setShowAppConfirmation(true);
                    else if (updated === 2) onSubmit(methods.getValues());
                    return updated;
                });
            }
            appState.current = nextAppState;
        });
        return () => {
            subscription.remove();
        };
    }, []);

    useEffect(() => {
        const backAction = () => {
            if (questionIndex > 0 && questionIndex < currentCompetition?.question_id?.length) {
                setQuestionIndex(questionIndex - 1);
                return true;
            }
            else if (questionIndex === 0) {
                setShowConfirmation(true);
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [questionIndex]);

    const handleClose = () => {
        setShowConfirmation(false);
    }

    useEffect(() => {
        getTotalDuration();
    }, [])

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header name='Quiz' index={-1} setShowConfirmation={setShowConfirmation} menu={false} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <FormProvider {...methods}>

                    <View className='w-full h-full py-3 px-primary'>

                        <View>
                            <QuestionCard
                                key={currentCompetition?.question_id[questionIndex]?._id}
                                questionIndex={questionIndex}
                                currentCompetition={currentCompetition}
                                totalDuration={totalDuration}
                                currentQuestion={currentCompetition?.question_id[questionIndex]}
                            />
                        </View>

                        <View className='flex-row items-center justify-center w-full py-8 space-x-4 bg-cardLight dark:bg-secondary rounded-b-md'>

                            <TouchableOpacity
                                onPress={handlePreviousQuestion}
                                activeOpacity={0.8}
                                className={`h-[42px] w-[42px] rounded-full pl-2 items-center justify-center ${questionIndex > 0 ? 'bg-primary' : 'border border-[#aaa]'}`}
                            >
                                <MaterialIcons name='arrow-back-ios' size={20} color={questionIndex > 0 ? "#000" : '#aaa'} />
                            </TouchableOpacity>

                            <Text className={`text-xl text-secondary dark:text-white text-center tracking-widest`} style={Global.text_medium}>
                                {questionIndex + 1 < 10 ? `0${questionIndex + 1}` : questionIndex + 1} / {currentCompetition?.question_id?.length < 10 ? `0${currentCompetition?.question_id?.length}` : currentCompetition?.question_id?.length}
                            </Text>

                            <TouchableOpacity
                                onPress={handleNextQuestion}
                                activeOpacity={0.8}
                                className={`h-[42px] w-[42px] rounded-full items-center justify-center ${currentCompetition?.question_id?.length > questionIndex + 1 ? 'bg-primary' : 'border border-[#aaa]'}`}
                            >
                                <MaterialIcons name='arrow-forward-ios' size={20} color={currentCompetition?.question_id?.length > questionIndex + 1 ? "#000" : '#aaa'} />
                            </TouchableOpacity>

                        </View>

                        {currentCompetition?.question_id?.length === questionIndex + 1 && <View className="flex-row justify-center w-full py-4">
                            <Button title="Submit" height={53} onPress={methods.handleSubmit(onSubmit)} />
                        </View>}

                    </View>

                </FormProvider>

            </ScrollView>

            <QuizWarning
                handleClose={handleClose}
                showConfirmation={showConfirmation}
                onSubmit={onSubmit}
                handleSubmit={methods.handleSubmit}
            />

            <AppMinimize
                handleAppClose={() => setShowAppConfirmation(false)}
                showAppConfirmation={showAppConfirmation}
            />

        </View>
    )
}

export default Quiz