import { <PERSON><PERSON><PERSON><PERSON>, Text, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import { ClientContext } from '../../../context/ClientContext';
import Button from '../../../components/Button';
import Global from '../../../../globalStyle';
import { getTimeQuiz } from '../../../helpers/dateTimeFormat';
import { Image } from 'expo-image';

const QuizResult = ({ route }: any) => {

    const { navigation, userData, getClientData } = useContext(ClientContext);
    const currentCompetition = route?.params?.currentCompetition || {};
    const appStateVisible = route?.params?.appStateVisible || 0;

    const [score, setScore] = useState(0);
    const [currentQuiz, setCurrentQuiz] = useState<any>();

    const [totalDuration, setTotalDuration] = useState(0);
    const [quizEnded, setQuizEnded] = useState(false);

    const handleQuizAnswers = async () => {
        navigation.navigate('QuizAnswers', { currentQuiz: currentQuiz, currentCompetition: currentCompetition });
    }

    const getAnswers = async () => {
        const currentQuiz = userData?.attended_quizzes?.find(quiz => quiz?.quiz_id === currentCompetition?._id);
        if (currentQuiz) {
            setCurrentQuiz(currentQuiz);
            setScore(currentQuiz.percentage_score);
        }
    }

    const isQuizEnded = () => {

        const currentDate = new Date();

        if (!currentCompetition || !currentCompetition.date_time || !currentCompetition.start_time || !currentCompetition.end_time) {
            return false;
        }

        const competitionDate = new Date(currentCompetition.date_time);

        const time1 = getTimeQuiz(currentCompetition.start_time);
        const time2 = getTimeQuiz(currentCompetition.end_time);
        const time3 = (currentDate.getHours() % 12) * 60 + currentDate.getMinutes() + currentDate.getSeconds() / 60;

        return (
            competitionDate.getFullYear() >= currentDate.getFullYear() &&
            competitionDate.getMonth() >= currentDate.getMonth() &&
            time1 <= time3 &&
            time2 >= time3
        );
    };

    const getTotalDuration = () => {
        const start = getTimeQuiz(currentCompetition?.start_time);
        const end = getTimeQuiz(currentCompetition?.end_time);
        const current = new Date();
        const currentTime = (current.getHours() % 12) * 60 + current.getMinutes();

        const totalDurationMinutes = end - start;
        const elapsedDurationMinutes = currentTime - start;
        const totalDurationSeconds = (totalDurationMinutes - elapsedDurationMinutes) * 60;

        if (currentTime < start || currentTime > end) {
            setQuizEnded(isQuizEnded());
            return 0;
        } else {
            return totalDurationSeconds;
        }
    }

    const handleDurationFormat = () => {
        const minutes = Math.floor(totalDuration / 60);
        const seconds = totalDuration % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    useEffect(() => {
        const remainingDuration = getTotalDuration();
        setTotalDuration(remainingDuration);

        const interval = setInterval(() => {
            setTotalDuration((prevDuration) => {
                if (prevDuration <= 1) {
                    clearInterval(interval);
                    setQuizEnded(true);
                    return 0;
                }
                return prevDuration - 1;
            });
        }, 1000);

        return () => clearInterval(interval);
    }, [currentQuiz]);

    useEffect(() => {
        getClientData();
        getAnswers();
    }, [])

    const backAction = () => {
        navigation.navigate('CompetitionDetails', { currentCompetition: currentCompetition });
    };

    useEffect(() => {
        const backAction = () => {
            navigation.navigate('CompetitionDetails', { currentCompetition: currentCompetition });
            return true;
        };
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
        return () => backHandler.remove();
    }, []);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Quiz' index={-1} handleNavigation={backAction} />

            <View className='h-3/4 w-full px-primary justify-center'>

                <View className='w-full flex-col items-center bg-cardLight dark:bg-secondary pb-5 rounded-md'>

                    <View className='w-full bg-primary rounded-t-md'>
                        <Text
                            style={Global.text_bold}
                            className='w-full py-6 text-secondary tracking-wide text-lg text-center'
                        >
                            {appStateVisible === 0 ? 'Quiz has ended' : 'Your quiz has been submitted, you moved out the app several times'}
                        </Text>
                    </View>

                    <Image
                        source={require('../../../assets/images/crown.png')}
                        style={{ height: 180, width: '100%' }}
                        contentFit='cover'
                    />

                    {quizEnded ? (
                        <Text
                            style={Global.text_medium}
                            className='w-3/4 self-center py-3 text-secondary dark:text-white tracking-wide text-center text-lg'
                        >
                            You have scored <Text style={Global.text_bold}>
                                ({Number(score) % 1 === 0
                                    ? Number(score)
                                    : score?.toFixed(2)}/100)
                            </Text>
                        </Text>
                    ) : (
                        <Text
                            style={Global.text_medium}
                            className='w-3/4 self-center py-5 text-secondary dark:text-white tracking-wide text-center text-lg'
                        >
                            Please wait <Text style={Global.text_bold}>({handleDurationFormat()})</Text> before revealing the answers
                        </Text>
                    )}

                    <View className='items-center'>
                        {quizEnded ? (
                            <Button
                                title='View answers'
                                onPress={handleQuizAnswers}
                            />
                        ) : (
                            <View className='h-[57] px-5 rounded-full bg-slate-300 items-center justify-center'>
                                <Text
                                    className='text-text16 tracking-wide text-secondary'
                                    style={Global.text_bold}
                                >
                                    View answers
                                </Text>
                            </View>
                        )}
                    </View>

                </View>

            </View>

        </View>
    )
}

export default QuizResult