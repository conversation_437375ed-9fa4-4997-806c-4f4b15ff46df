import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ActivityIndicator } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import Global from '../../../../globalStyle';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { Entypo, Feather } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { ClientContext } from '../../../context/ClientContext';
import { getDateDay, isQuizExpired } from '../../../helpers/dateTimeFormat';
import Animated, { FadeInUp } from 'react-native-reanimated';
import AnswerOption from '../components/QuizAnswerOption';
import GetFeedback from '../../../components/GetFeedback';
import { LikeSaveContext } from '../../../context/LikeSaveContext';

const QuizAnswers = ({ route }: any) => {

    const currentQuiz = route?.params?.currentQuiz || {};
    const currentCompetition = route?.params?.currentCompetition || {};
    const quizAttended = route?.params?.quizAttended || false;

    const [isFeedbackModalVisible, setIsFeedbackModalVisible] = useState(false);

    const { colorScheme, navigation, clientId, registeredComps } = useContext(ClientContext);
    const { registeredItems } = useContext(LikeSaveContext);

    const [leaderboard, setLeaderboard] = useState([]);
    const [loading, setLoading] = useState(true);

    const getLeaderboard = async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/auth/leaderboard/${currentCompetition?._id}`);
            const responseData = response.data?.data;
            setLeaderboard(responseData);

        } catch (error) {
            console.log("Fetching Leaderboard Data : ", error);
        } finally {
            setLoading(false);
        }
    }

    const feedbackSubmited = async () => {
        try {
            const response = await ClientAxiosInstance.get(`/quizfeedback/${clientId}/${currentCompetition?._id}`);
            setIsFeedbackModalVisible(!response.data.data.is_feedback);

        } catch (error: any) {
            console.log("Checking feedback is submitted? : ", error.response.data);
            if (error.response.data.message === 'Feedback not found for this user and quiz') setIsFeedbackModalVisible(true);
        }
    }

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(currentCompetition?._id);
        const isRegisteredPreviously = registeredComps[0]['Quiz']?.includes(currentCompetition?._id);
        return isRegisteredNow || isRegisteredPreviously;
    };

    useEffect(() => {
        if (currentQuiz) {
            getLeaderboard();
        }
        if (isPaymentCompleted() && isQuizExpired(currentCompetition?.date_time, currentCompetition?.end_time)) feedbackSubmited();
    }, []);

    const backAction = () => {
        navigation.navigate('CompetitionDetails', { currentCompetition: currentCompetition });
    };

    useEffect(() => {
        const backAction = () => {
            navigation.navigate('CompetitionDetails', { currentCompetition: currentCompetition });
            return true;
        };
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
        return () => backHandler.remove();
    }, []);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Quiz Answers' index={-1} handleNavigation={backAction} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1 space-y-3'>

                <View className='w-full px-primary space-y-3 mt-3 mb-12'>

                    <View className='bg-primary py-4 items-center rounded-md space-y-4'>

                        <Text className='text-secondary text-xl' style={Global.text_bold}>Quiz Details</Text>
                        <Text className='text-secondary text-text15' style={Global.text_medium}>{getDateDay(currentCompetition?.date_time)}</Text>
                        {quizAttended && <Text className='text-secondary text-lg' style={Global.text_bold}>Your total score in this quiz</Text>}
                        {quizAttended && <Text className='text-secondary text-xl' style={Global.text_bold}>
                            ({Number(currentQuiz?.percentage_score) % 1 === 0
                                ? Number(currentQuiz?.percentage_score)
                                : currentQuiz?.percentage_score?.toFixed(2)}/100)
                        </Text>}
                    </View>

                    {currentQuiz.user_answers?.map((item: any, index: number) => {

                        const { question, description, question_image_path, all_answers } = item;
                        const noAnswer = !item.userAnswer
                        const isUserCorrect = item.userAnswer === item.correctAnswer;

                        return (
                            <Animated.View
                                entering={FadeInUp.delay(100 * index).duration(1000).springify()}
                                className="w-full bg-cardLight dark:bg-secondary px-3 py-3 space-y-3 rounded-md"
                                key={index}
                            >
                                <View className="px-2 py-3 space-y-4">
                                    <View className="w-full flex-row items-start justify-between">
                                        <Text style={Global.text_medium} className="w-[10%] text-text16 text-secondary dark:text-white tracking-wide">
                                            {index + 1}.
                                        </Text>
                                        <View className="space-y-2 w-[90%]">
                                            <Text style={Global.text_regular} className="text-text16 text-secondary dark:text-white tracking-wide">
                                                {question}
                                            </Text>
                                            {description && (
                                                <Text style={Global.text_medium} className="text-text16 text-secondary dark:text-white tracking-wide">
                                                    {description}
                                                </Text>
                                            )}
                                        </View>
                                    </View>

                                    {question_image_path && (
                                        <View className="h-[180px] w-full">
                                            <Image
                                                source={{ uri: question_image_path }}
                                                style={{ width: '100%', height: '100%', borderRadius: 5 }}
                                                contentFit="cover"
                                            />
                                        </View>
                                    )}
                                </View>

                                {all_answers.map((option: any, optionIndex: number) => {
                                    const isCorrectAnswer = option.correct;
                                    const isUserAnswer = item.userAnswer === option.text;
                                    return (
                                        <View key={optionIndex}>
                                            <AnswerOption
                                                isFillups={all_answers.length === 1}
                                                option={option}
                                                isCorrectAnswer={isCorrectAnswer}
                                                isUserAnswer={isUserAnswer}
                                                noAnswer={noAnswer}
                                                isUserCorrect={isUserCorrect}
                                                userAnswer={item.userAnswer}
                                            />
                                        </View>
                                    )
                                })}

                                <View className='w-full flex-row items-center space-x-2'>

                                    <View className={`h-6 w-6 rounded-full items-center justify-center ${isUserCorrect ? 'bg-green-600' : 'bg-red-500'}`}>
                                        <Entypo name={isUserCorrect ? 'check' : 'cross'} size={18} color='white' />
                                    </View>

                                    <Text style={Global.text_medium} className="text-text15 text-secondary dark:text-white tracking-wide w-[90%]">
                                        Answer is {isUserCorrect ? 'correct' : 'wrong'} ({item.correctAnswer})
                                    </Text>

                                </View>

                            </Animated.View>
                        )
                    })}

                    <View className='bg-cardLight dark:bg-secondary rounded-md'>

                        <View className='bg-primary py-4 items-center rounded-t-md space-y-4'>
                            <Text className='text-secondary text-xl' style={Global.text_bold}>Leaderboard</Text>
                            <Text className='text-secondary text-text15' style={Global.text_medium}>Quiz</Text>
                        </View>

                        {loading ?
                            <View className='py-2'>
                                <ActivityIndicator size={25} color={colorScheme === 'dark' ? '#fff' : '#fdd066'} />
                            </View> :
                            <View className='py-2 px-1 space-y-4'>

                                <View className='w-full flex-row items-center justify-between mt-2'>
                                    <View className='w-[20%] items-center'>
                                        <Text style={Global.text_bold} className='text-secondary dark:text-white text-text16'>Rank</Text>
                                    </View>
                                    <View className='w-[50%] pl-1 items-start'>
                                        <Text style={Global.text_bold} className='text-secondary dark:text-white text-text16'>Username</Text>
                                    </View>
                                    <View className='w-[30%] items-center'>
                                        <Text style={Global.text_bold} className='text-secondary dark:text-white text-text16'>Total Points</Text>
                                    </View>
                                </View>

                                <View className='h-[1.5px] w-full bg-secondary dark:bg-white' />

                                {leaderboard?.map((leader: any, index: number) => (

                                    <View className='w-full flex-row items-center justify-between' key={index}>

                                        <View className='w-[20%] items-center'>
                                            <Text style={Global.text_medium} className='text-secondary dark:text-white text-text16'>
                                                {leader?.rank < 10 ? `0${leader?.rank}` : `${leader?.rank}`}
                                            </Text>
                                        </View>

                                        <View className='w-[50%] flex-row items-center justify-center space-x-1'>

                                            <View className='h-8 w-8 bg-[#a8a8a8] rounded-full overflow-hidden items-center justify-center'>
                                                {leader?.user_profile ? (
                                                    <Image
                                                        source={{ uri: leader?.user_profile }}
                                                        style={{ height: '100%', width: '100%' }}
                                                        contentFit='cover'
                                                    />
                                                ) : (
                                                    <View className='h-full w-full items-center justify-center pb-[2]'>
                                                        <Feather name="user" size={20} color={colorScheme === 'dark' ? '#000' : '#fff'} />
                                                    </View>
                                                )}
                                            </View>

                                            <View className='w-3/4'>
                                                <Text
                                                    style={Global.text_medium}
                                                    className='text-secondary dark:text-white text-text16'
                                                    numberOfLines={1}
                                                    ellipsizeMode='tail'
                                                >
                                                    {leader?.firstName} {leader?.lastName}
                                                </Text>
                                            </View>

                                        </View>

                                        <View className='w-[30%] items-center'>
                                            <Text style={Global.text_medium} className='text-secondary dark:text-white text-text16'>
                                                {leader?.percentageScore?.toFixed(2)} / 100
                                            </Text>
                                        </View>

                                    </View>
                                ))}

                            </View>
                        }

                    </View>

                </View>

            </ScrollView>

            <GetFeedback
                visible={isFeedbackModalVisible}
                onClose={() => setIsFeedbackModalVisible(false)}
                chip_id={currentCompetition?._id}
                feedback_for="quizfeedback"
            />

        </View>
    )
}

export default QuizAnswers;