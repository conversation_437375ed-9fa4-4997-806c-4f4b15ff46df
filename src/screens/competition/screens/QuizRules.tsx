import { View, Text, ScrollView } from 'react-native'
import React, { useContext, useState } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import Global from '../../../../globalStyle';
import Button from '../../../components/Button';
import RulesRegulations from '../components/RulesRegulations';
import { ClientContext } from '../../../context/ClientContext';


const QuizRules = ({ route }: any) => {

    const currentCompetition = route?.params?.currentCompetition || {};

    const { navigation } = useContext(ClientContext);

    const startEvaluationTest = () => {
        navigation.navigate('Quiz', { currentCompetition: currentCompetition });
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Quiz' index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='h-full w-full justify-center'>

                    <View className='h-full w-fullx items-center justify-end space-y-7 px-primary py-primary'>

                        <View className='w-full'>
                            <RulesRegulations currentCompetition={currentCompetition} />
                            </View>

                        <View>
                            <Button title='Start Evaluation Test' onPress={startEvaluationTest} />
                        </View>

                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default QuizRules