import { View, Text, Linking, Alert } from 'react-native'
import React from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar'

import Header from '../../../components/Header'
import Button from '../../../components/Button'
import Global from '../../../../globalStyle'

const PostToUs = () => {

    const handleWebsite = async () => {
        const url = 'https://www.lawcube.org/promote-to-us/competition'
        Linking.openURL(url).catch(err => console.log("Couldn't load page", err));
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Post to us' index={-1} />

            <View className='h-full w-full px-primary'>

                <View className='h-3/4 w-full space-y-5 flex-col items-center px-primary justify-center'>

                    <Text
                        style={Global.text_bold}
                        className='text-secondary dark:text-white tracking-wide text-2xl text-center'
                    >
                        Visit Lawcube website
                    </Text>

                    <Text
                        style={Global.text_medium}
                        className='text-secondary dark:text-white tracking-wide text-center text-lg'
                    >
                        To post your content in our website, you have to visit the website
                    </Text>

                    <View className='items-center'>
                        <Button title='Visit Website' onPress={handleWebsite} />
                    </View>

                </View>

            </View>

        </View>
    )
}

export default PostToUs