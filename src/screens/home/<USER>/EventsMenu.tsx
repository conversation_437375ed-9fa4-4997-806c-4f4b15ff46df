import { View, Text, FlatList, Dimensions, TouchableOpacity } from 'react-native';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import Global from '../../../../globalStyle';
import Carousel from 'react-native-snap-carousel';
import EventSlider from './EventSlider';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { ClientContext } from '../../../context/ClientContext';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { Skeleton } from '@rneui/themed';
import { AntDesign, Feather, SimpleLineIcons } from '@expo/vector-icons';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;
const Width = carouselWidth - (carouselWidth * 0.04) - (carouselWidth * 0.04);

interface EventsMenuProps {
    refreshing: boolean;
    setRefreshing: (val: boolean) => void;
}

const EventsMenu: React.FC<EventsMenuProps> = ({ refreshing, setRefreshing }) => {

    const [selectedChip, setSelectedChip] = useState('College Events');
    const [loading, setLoading] = useState(true);

    const { isLarge, colorScheme } = useContext(ClientContext);

    const [selectedChipData, setSelectedChipData] = useState<any>();

    const fetchData = async (selectedChip: string, chip: string, searchChip: string) => {
        try {
            setLoading(true);
            let url = `/${chip}/${searchChip}?page=1&limit=2&sort=latestdatetime`;

            const response = await ClientAxiosInstance.get(url);
            setSelectedChipData((prev: any) => ({
                ...prev,
                [selectedChip]: response.data.data
            }));

        } catch (error) {
            console.log("Fetching Event Data: ", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }

    const carouselRef = useRef(null);
    const [activeSlide, setActiveSlide] = useState(0);

    const data = useMemo(() => [
        {
            id: 1,
            title: 'College Events',
            show: selectedChipData && selectedChipData['College Events'] ? 'flex' : 'none',
        },
        {
            id: 2,
            title: 'Seminars',
            show: selectedChipData && selectedChipData['Seminars'] ? 'flex' : 'none',
        },
        {
            id: 3,
            title: 'Workshop',
            show: selectedChipData && selectedChipData['Workshop'] ? 'flex' : 'none',
        },
    ], [selectedChipData]);

    const handleChipPress = (item: any) => {
        setSelectedChip(data[item.id - 1]?.title);
    };

    useEffect(() => {
        (async () => {
            await fetchData('College Events', 'collegeevent', 'searchCollegeevent')
            await fetchData('Workshop', 'workshop', 'searchWorkshop')
            await fetchData('Seminars', 'seminar', 'searchSeminar')
        })()
    }, [refreshing])

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View
                style={{
                    display: item.show,
                    paddingLeft: index === 0 ? leftMargin : 10,
                    paddingRight: index === data.length - 1 ? leftMargin : 0
                }}
            >
                <TouchableOpacity
                    activeOpacity={0.8} onPress={() => handleChipPress(item)}
                    className={`h-10 justify-center items-center border-[0.8px] border-primary px-[10px] ${selectedChip === item?.title ? 'bg-primary border-transparent' : 'bg-transparent'}`}
                    style={{
                        borderRadius: 25,
                        paddingVertical: 2,
                    }}
                >
                    <Text
                        style={Global.text_bold}
                        className={`tracking-wide text-text14 ${selectedChip === item?.title ? 'text-secondary' : 'text-secondary dark:text-white'}`}
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        );
    };

    const renderCarouselItem = ({ item }: any) => {
        return <EventSlider item={item} selectedChip={selectedChip} />;
    };

    return (
        selectedChipData && Object.keys(selectedChipData).some(key => selectedChipData[key]?.length > 0) ? (

        <View className="w-full space-y-4 mt-5">

            <Text style={Global.lora_semibold} className="text-xl tracking-wide px-primary text-secondary dark:text-white">
                Events
            </Text>

            <View>
                <FlatList
                    horizontal
                    data={data}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id.toString()}
                    showsHorizontalScrollIndicator={false}
                />
            </View>

            <View>
                {loading ? (
                    <View className='px-primary'>
                        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

                            <View className='space-y-3'>

                                <View style={{ height: widthPercentageToDP(isLarge ? 30 : 50) }}>
                                    <Skeleton animation='wave'
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            borderRadius: 5
                                        }}
                                    />
                                </View>

                                <View className='flex-row items-start justify-between'>

                                    <View className='w-[55%] space-y-2'>
                                        <Skeleton animation='wave' style={{
                                            height: 15,
                                            borderRadius: 2,
                                            width: '85%'
                                        }} />
                                        <Skeleton animation='wave' style={{
                                            height: 15,
                                            borderRadius: 2,
                                            width: '60%'
                                        }} />
                                    </View>

                                    <View className='w-[42%] space-y-3'>

                                        <View className='flex-row items-center space-x-2'>
                                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                            <Skeleton animation='wave' style={{
                                                height: 15,
                                                borderRadius: 2,
                                                width: '70%'
                                            }} />
                                        </View>

                                        <View className='flex-row items-center space-x-2'>
                                            <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                            <Skeleton animation='wave' style={{
                                                height: 15,
                                                borderRadius: 2,
                                                width: '70%'
                                            }} />
                                        </View>

                                        <View className='flex-row items-center space-x-2'>
                                            <Feather name='users' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                            <Skeleton animation='wave' style={{
                                                height: 15,
                                                borderRadius: 2,
                                                width: '70%'
                                            }} />
                                        </View>

                                        <View className='flex-row items-center space-x-2'>
                                            <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                            <Skeleton animation='wave' style={{
                                                height: 15,
                                                borderRadius: 2,
                                                width: '70%'
                                            }} />
                                        </View>
                                    </View>

                                </View>

                            </View>

                            <View className='items-center w-full mb-2'>
                                <Skeleton animation='wave' style={{
                                    height: 20,
                                    borderRadius: 25,
                                    width: '60%'
                                }} />
                            </View>

                        </View>
                    </View>
                ) : (
                    selectedChipData[selectedChip] && selectedChipData[selectedChip].length > 0 && (
                        <Carousel
                            layout={'default'}
                            ref={carouselRef}
                            data={selectedChipData?.[selectedChip]}
                            firstItem={activeSlide}
                            sliderWidth={carouselWidth}
                            onSnapToItem={(index) => setActiveSlide(index)}
                            itemWidth={isLarge ? Width / 1.5 : Width}
                            renderItem={renderCarouselItem}
                        />
                    )
                )}
            </View>

        </View>

    ) : null
);

};

export default EventsMenu;
