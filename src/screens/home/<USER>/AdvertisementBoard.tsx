import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import Button from '../../../components/Button'
import { ClientContext } from '../../../context/ClientContext'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { Image } from 'expo-image';

const AdvertisementBoard = () => {

    const { navigation } = useContext(ClientContext);

    const handleAdvertisement = () => {
        navigation.navigate('PostToUs');
    }

    return (
        <View className='w-full px-primary'>

            <View className='w-full bg-[#F7F0DB] dark:bg-darkcard rounded-md px-2 pt-2 pb-3 space-y-3'>

                <View style={{ height: widthPercentageToDP(50) }}>
                    <Image
                        source={require('../../../assets/images/advertisement.png')}
                        style={{ height: '100%', width: '100%', borderRadius: 6 }}
                        contentFit='cover'
                    />
                </View>

                <Text
                    style={Global.text_bold}
                    className='text-lg tracking-wider text-center uppercase text-secondary dark:text-white'
                >
                    Advertisement
                </Text>

                <View className='space-y-3'>

                    <Text
                        style={Global.lora_bold}
                        className='text-xl tracking-wide text-center text-secondary dark:text-white'
                    >
                        Advertise With Us
                    </Text>

                    <Text
                        style={Global.text_medium}
                        className='tracking-wide text-center text-secondary dark:text-white text-text15'
                    >
                        We provide a platform to promote and advertise your law-related events. Whether it's a quiz, competition, or workshop, you can connect with individuals passionate about law.
                    </Text>

                </View>

                <View className='items-center'>
                    <Button title='Contact Us' height={45} paddingX={12} onPress={handleAdvertisement} />
                </View>

            </View>

        </View>
    )
}

export default AdvertisementBoard