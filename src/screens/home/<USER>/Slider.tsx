import { View, Text, Dimensions, TouchableOpacity } from "react-native";
import React, { useContext, useEffect, useRef, useState } from "react";
import Carousel, { Pagination } from "react-native-snap-carousel";
import Global from "../../../../globalStyle";
import { ClientAxiosInstance } from "../../../lib/axiosInstance";
import { Skeleton } from "@rneui/themed";
import { bannerUrl } from "../../../helpers/bannerUrl";
import { ClientContext } from "../../../context/ClientContext";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { Toast } from "../../../components/Toast";
import { LikeSaveContext } from "../../../context/LikeSaveContext";
import { LinearGradient } from "expo-linear-gradient";
import { Image } from "expo-image";

const carouselWidth = Dimensions.get("window").width;
const Width = carouselWidth - carouselWidth * 0.08;

interface CarouselData {
    title: string;
    image: string;
    chip: string[];
}

interface SliderProps {
    refreshing: boolean;
    setRefreshing: (val: boolean) => void;
}

const Slider: React.FC<SliderProps> = ({ refreshing, setRefreshing }) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { navigation, setCurrentCareerChip, setCurrentCompChip, setCurrentEventChip, setCurrentReadingsChip, setCurrentCourseChip, registeredCourses } = useContext(ClientContext);
    const { registeredItems } = useContext(LikeSaveContext);

    const carouselRef = useRef<Carousel<CarouselData>>(null);
    const [data, setData] = useState<CarouselData[]>([]);
    const [activeSlide, setActiveSlide] = useState(0);
    const [loading, setLoading] = useState(true);

    const fetchBanners = async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(
                `${api_link}/banner/banners`
            );
            const formattedData = response.data.data
                .filter((item: any) => item?.status?.toLowerCase() === "active")
                .map((item: any) => ({
                    title: item.ad_title,
                    image: item.thumbnail_image_path,
                    chip: item?.link?.split("/").filter((part: string) => part),
                }));
            setData(formattedData);

        } catch (error) {
            console.log("Fetching Banner Data : ", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchBanners();
    }, [refreshing]);

    const isPaymentCompleted = (id: string) => {
        const isRegisteredNow = registeredItems?.has(id);
        const isRegisteredPreviously = registeredCourses?.includes(id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    const handleBanner = async (chip: string[]) => {

        const bannerChip = chip && chip[0] === "readings" ? chip[1] : chip[0];
        const bannerChipId = chip && chip[0] === "readings" ? chip[2] : chip[0] === 'course' ? chip[2] : chip[1];

        const { url, path, selected_chip } = bannerUrl(bannerChip, bannerChipId);

        try {

            const response = await ClientAxiosInstance.get(url);
            const responseData = response.data.data;

            if (path === "CompetitionDetails") {
                setCurrentCompChip(selected_chip);
                navigation.navigate(path, { currentCompetition: responseData });
            }
            else if (path === "EventDetails") {
                setCurrentEventChip(selected_chip);
                navigation.navigate(path, { currentEvent: responseData });
            }
            else if (path === "CourseDetails") {
                const course_chip = chip[1] === 'beginner' ? 'Beginner' : chip[1] === 'intermediate' ? 'Intermediate' : 'Advanced'
                setCurrentCourseChip(course_chip);
                if (!isPaymentCompleted(responseData._id))
                    navigation.navigate(path, { currentCourse: responseData });
                else navigation.navigate('BookedCourse', { currentCourse: responseData });
            }
            else if (path === "CareerDetails") {
                setCurrentCareerChip(selected_chip);
                navigation.navigate(path, { selectedCareer: responseData });
            }
            else if (path === "LawyerDetails") {
                navigation.navigate(path, { data: responseData });
            }
            else if (path === "ReadingDetail") {
                setCurrentReadingsChip(selected_chip);
                navigation.navigate(path, {
                    item: responseData,
                    selectedChipData: [responseData],
                });
            }
        } catch (error) {
            console.log("Banner Navigation : ", error);
            Toast.show({
                type: 'error',
                message: `${selected_chip} not found`,
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        }
    };

    const renderItem = ({ item }: { item: CarouselData }) => {
        return (
            <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => handleBanner(item.chip)}
                className="space-y-3"
            >
                <View style={{ width: "100%", height: wp(50) }}>
                    <Image
                        source={
                            item?.image
                                ? { uri: item?.image }
                                : require("../../../assets/images/placeholder-thumbnail.png")
                        }
                        style={{ width: "100%", height: "100%", borderRadius: 5 }}
                    />
                </View>
                <Text
                    className="text-lg tracking-wide text-secondary dark:text-white"
                    style={Global.text_bold}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                >
                    {item.title}
                </Text>
            </TouchableOpacity>
        );
    };

    const pagination_one = (activeSlide: number) => {
        return (
            <Pagination
                dotsLength={data.length}
                activeDotIndex={activeSlide}
                dotStyle={{
                    width: 10,
                    height: 10,
                    borderRadius: 1,
                    backgroundColor: "#fdd066",
                    marginHorizontal: 0,
                    paddingHorizontal: 0,
                }}
                inactiveDotStyle={{
                    backgroundColor: "#ffffff",
                    width: 10,
                    height: 10,
                    borderRadius: 1,
                    marginHorizontal: 0,
                    paddingHorizontal: 0,
                    borderWidth: 0.8,
                    borderColor: "#000",
                }}
                inactiveDotScale={0.8}
                containerStyle={{
                    flexDirection: "row",
                    justifyContent: "center",
                    paddingHorizontal: 0,
                    paddingVertical: 10,
                }}
            />
        );
    };

    // useEffect(() => {
    //     const interval = setInterval(() => {
    //         const nextIndex = (activeSlide + 1) % data.length;
    //         carouselRef.current?.snapToItem(nextIndex);
    //         setActiveSlide(nextIndex);
    //     }, 3000);

    //     return () => clearInterval(interval);
    // }, [activeSlide, data.length]);

    return loading ? (
        <View className="self-center mt-6 space-y-3" style={{ width: Width }}>
            <View className="items-center w-full">
                <Skeleton
                    animation="wave"
                    LinearGradientComponent={LinearGradient}
                    style={{ width: Width, height: wp(55), borderRadius: 5 }}
                />
            </View>
            <View className="w-full pl-[2px]">
                <Skeleton
                    animation="wave"
                    LinearGradientComponent={LinearGradient}
                    style={{
                        height: 12,
                        borderRadius: 2,
                        width: "90%",
                    }}
                />
            </View>
        </View>
    ) : (

        <View className="space-y-3">

            <View className="flex-1 mt-6">
                <Carousel
                    layout={"default"}
                    ref={carouselRef}
                    data={data}
                    sliderWidth={carouselWidth}
                    onSnapToItem={(index) => setActiveSlide(index)}
                    itemWidth={Width}
                    renderItem={renderItem}
                    loop
                        autoplay
                        autoplayInterval={3000}
                />
            </View>

            <View>{pagination_one(activeSlide)}</View>

        </View>
    );
};

export default Slider;