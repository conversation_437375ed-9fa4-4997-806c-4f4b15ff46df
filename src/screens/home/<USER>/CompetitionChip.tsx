import { View, Text, FlatList, Dimensions, TouchableOpacity } from 'react-native'
import React, { useContext, useRef } from 'react'
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { QuizChipIcon, QuizChipIconLight } from '../../../assets/icons/chipIcon/QuizChipIcon';
import { MootCourtIcon, MootCourtIconLight } from '../../../assets/icons/chipIcon/MootCourtIcon';
import { EssayIcon, EssayIconLight } from '../../../assets/icons/chipIcon/EssayIcon';
import { ArticleIcon, ArticleIconLight } from '../../../assets/icons/chipIcon/ArticleIcon';
import { DebateIcon, DebateIconLight } from '../../../assets/icons/chipIcon/DebateIcon';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

const CompetitionChip = () => {

    const { navigation, colorScheme, setCurrentCompChip } = useContext(ClientContext);

    const flatListRef = useRef<FlatList>(null);

    const data = [
        { id: 1, title: 'Quiz', icon: colorScheme === 'dark' ? <QuizChipIconLight /> : <QuizChipIcon /> },
        { id: 2, title: 'Moot Court', icon: colorScheme === 'dark' ? <MootCourtIconLight /> : <MootCourtIcon /> },
        { id: 3, title: 'Essay Writing', icon: colorScheme === 'dark' ? <EssayIconLight /> : <EssayIcon /> },
        { id: 4, title: 'Article Writing', icon: colorScheme === 'dark' ? <ArticleIconLight /> : <ArticleIcon /> },
        { id: 5, title: 'Debate', icon: colorScheme === 'dark' ? <DebateIconLight /> : <DebateIcon /> }
    ];

    const handleChipSelection = (title:string) => {
        setCurrentCompChip(title)
        navigation.navigate('Competition');
    }

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={()=>handleChipSelection(item.title)}
                    style={{ borderRadius: 25 }}
                    className='h-10 flex-row items-center justify-center space-x-2 border-[0.8px] border-secondary dark:border-white px-[10px] bg-transparent'
                >
                    <View>
                        {item.icon}
                    </View>
                    <Text
                        style={Global.text_bold}
                        className='text-secondary dark:text-white tracking-wide text-text14'
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        )
    };

    return (
        <View className='w-full'>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
}

export default CompetitionChip;
