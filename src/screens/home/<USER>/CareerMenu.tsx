import { View, Text } from 'react-native'
import React, { useEffect, useState } from 'react'
import Global from '../../../../globalStyle'
import CareerCard from './CareerCard'
import { ClientAxiosInstance } from '../../../lib/axiosInstance'

interface CareerMenuProps {
    refreshing: boolean;
    setRefreshing: (val: boolean) => void;
}

const CareerMenu: React.FC<CareerMenuProps> = ({ refreshing, setRefreshing }) => {

    const [careerData, setCareerData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    const fetchCareerData = async () => {
        try {
            setLoading(true);
            const jobResponse = await ClientAxiosInstance.get(`/job/searchJob`);
            const internResponse = await ClientAxiosInstance.get(`/internship/searchInternship`);

            const jobResponseData = jobResponse.data.data;
            const internResponseData = internResponse.data.data;


            const fullTimeData = jobResponseData.find((job: any) => (job?.job_type?.toLowerCase() === 'full-time'));
            const partTimeData = jobResponseData.find((job: any) => (job?.job_type?.toLowerCase() === 'part-time'));

            const newCareerData: any[] = [];
            if (fullTimeData) newCareerData.push(fullTimeData);
            if (partTimeData) newCareerData.push(partTimeData);
            if (internResponseData[0]) newCareerData.push(internResponseData[0]);

            setCareerData(newCareerData);

        } catch (error) {
            console.log("Fetching Career Menu Data : ", error);
        }
        finally {
            setLoading(false);
            setRefreshing(false);
        }
    }

    useEffect(() => {
        fetchCareerData();
    }, [refreshing])

    return (
        <View className='w-full px-primary space-y-4'>
            <Text
                style={Global.lora_bold}
                className='text-xl text-secondary dark:text-white tracking-wide'
            >
                Career
            </Text>
            <View>
                <CareerCard careerData={careerData} loading={loading} />
            </View>
        </View>
    )
}

export default CareerMenu