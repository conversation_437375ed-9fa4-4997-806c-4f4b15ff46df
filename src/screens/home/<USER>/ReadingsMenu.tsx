import React, { useContext } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { Image } from 'expo-image';
import { Images } from '../../../helpers/images';

const ReadingsMenu = () => {

    const { navigation, setCurrentReadingsChip, isLarge } = useContext(ClientContext);

    const readingData = [
        {
            title: 'Blogs',
            img: Images.blogs
        },
        {
            title: 'News',
            img: Images.news
        },
        {
            title: 'Articles',
            img: Images.articles
        },
        {
            title: 'Journals',
            img: Images.journals
        }
    ]

    const handleNavigation = (title: string) => {
        setCurrentReadingsChip(title);
        navigation.navigate('Readings');
    };

    return (
        <View className='space-y-3 px-primary'>

            <Text style={Global.lora_semibold} className='text-xl tracking-wide text-secondary dark:text-white'>
                Readings
            </Text>

            <View className='flex-row flex-wrap items-center justify-between'>
                {readingData.map((reading, index) => (
                    <View key={index} className='space-y-1' style={{ marginBottom: index < 2 ? 35 : 0 }}>
                        <TouchableOpacity
                            activeOpacity={0.8}
                            style={{ width: widthPercentageToDP(45), height: isLarge ? widthPercentageToDP(25) : widthPercentageToDP(30) }}
                            onPress={() => handleNavigation(reading.title)}
                        >
                            <Image
                                source={reading.img}
                                style={{ width: '100%', height: '100%', borderRadius: 6 }}
                                contentFit='cover'
                            />
                        </TouchableOpacity>
                        <Text style={Global.text_bold} className='tracking-wider text-secondary dark:text-white text-text15'>
                            {reading.title}
                        </Text>
                    </View>
                ))}
            </View>

        </View>
    );
};

export default ReadingsMenu;
