import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import { CareerContext } from '../../../context/CareerContext'
import { Skeleton } from '@rneui/themed'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { Image } from 'expo-image';

interface CareerCardProps {
    careerData: any[];
    loading: boolean;
}

const CareerCard: React.FC<CareerCardProps> = ({ careerData, loading }) => {

    const { navigation, setCurrentCareerChip, setSelectedJobTypeHome, isLarge } = useContext(ClientContext);
    const { setSelectedChip } = useContext(CareerContext);

    const handleCareer = (type: string) => {

        if (type === 'Full-time') {
            setSelectedJobTypeHome('Full-time');
            setCurrentCareerChip('Job Listing');
            setSelectedChip('Job Listing');
        }
        else if (type === 'Part-time') {
            setSelectedJobTypeHome('Part-time');
            setCurrentCareerChip('Job Listing');
            setSelectedChip('Job Listing');
        }
        else {
            setCurrentCareerChip('Internship');
            setSelectedChip('Internship');
        }

        navigation.navigate('Career');
    }

    const careerDescriptionsMobile = [
        `We're opening our doors to talented advocates!`,
        `Join our team and grab the career opportunities as a part-time advocate!`,
        `Kickstart your legal career with the internship program!`
    ];

    const careerDescriptionsIsLarge = [
        `We're opening our doors to talented advocates! If you're passionate about the law and have a strong legal background, apply for the full-time advocate position today.`,
        `Join our team and grab the career opportunities as a part-time advocate! If you have a strong legal background and are looking for a flexible, supportive role, we'd love to hear from you`,
        `Kickstart your legal career with the internship program! Gain invaluable exposure to the legal field while learning and growing in a dynamic, supportive environment.`
    ]

    return (
        <View className='space-y-3'>

            {careerData?.map((career, index) => (
                <TouchableOpacity
                    onPress={() => handleCareer(career?.job_type || 'Internship')}
                    key={index}
                    activeOpacity={0.8}
                    className='w-full flex-row overflow-hidden items-center p-[6px] bg-cardLight dark:bg-darkcard rounded-md'
                    style={{ height: isLarge ? widthPercentageToDP(25) : widthPercentageToDP(27) }}
                >

                    <View className='w-[45%] h-full'>
                        {loading ? (
                            [1].map((index) => (
                                <Skeleton animation='wave' key={index}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        borderRadius: 8
                                    }}
                                />
                            ))
                        ) : (
                            <Image
                                source={career?.thumbnail_image_path ?
                                    { uri: career?.thumbnail_image_path } :
                                    require('../../../assets/images/placeholder-thumbnail.png')
                                }
                                style={{ height: '100%', width: '100%', borderRadius: 6 }}
                                contentFit='cover'
                            />
                        )}
                    </View>

                    <View className='w-[55%] h-full pl-2 flex-col justify-between'>
                        {loading ?
                            <Skeleton animation='wave' style={{
                                height: 15,
                                borderRadius: 10,
                                width: '85%'
                            }} /> :
                            <Text style={Global.text_bold} className='text-secondary dark:text-white text-text17' numberOfLines={1} ellipsizeMode='tail'>{career?.job_type || 'Internship'}</Text>
                        }
                        {loading ?
                            <View className='space-y-3'>
                                <Skeleton animation='wave' style={{ height: 10, borderRadius: 6, width: '100%' }} />
                                <Skeleton animation='wave' style={{ height: 10, borderRadius: 6, width: '90%' }} />
                                <Skeleton animation='wave' style={{ height: 10, borderRadius: 6, width: '80%' }} />
                            </View> :
                            <View className='w-full'>
                                <Text
                                    style={Global.text_regular}
                                    className={`w-full text-secondary dark:text-white ${isLarge ? 'text-text17' : 'text-text16'}`}
                                    numberOfLines={isLarge ? 4 : 3}
                                    ellipsizeMode='tail'
                                >
                                    {isLarge ? careerDescriptionsIsLarge[index] : careerDescriptionsMobile[index]}
                                </Text>
                            </View>
                        }
                    </View>

                </TouchableOpacity>
            ))}

        </View>
    )
}

export default CareerCard