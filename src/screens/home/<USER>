import React, { useCallback, useContext, useState } from 'react';
import { ScrollView, Text, View, RefreshControl } from 'react-native';
import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import Slider from './components/Slider';
import EventsMenu from './components/EventsMenu';
import ReadingsMenu from './components/ReadingsMenu';
import CareerMenu from './components/CareerMenu';
import AdvertisementBoard from './components/AdvertisementBoard';
import Global from '../../../globalStyle';
import CompetitionChip from './components/CompetitionChip';
import { ClientContext } from '../../context/ClientContext';

const MainHome = () => {

	const [refreshing, setRefreshing] = useState(false);
	const onRefresh = useCallback(() => {
		setRefreshing(true);
	}, []);

	return (
		<View className='flex-1 bg-white dark:bg-dark'>

			<CustomStatusBar />

			<View><Header search index={0} /></View>

			<ScrollView
				showsVerticalScrollIndicator={false}
				className='flex-1'
				refreshControl={
					<RefreshControl
						tintColor='#fdd066'
						onRefresh={onRefresh}
						refreshing={refreshing}
					/>
				}
			>
				<View className='space-y-6 mb-20'>

					<Slider refreshing={refreshing} setRefreshing={setRefreshing} />

					<View className='w-full pt-6 pb-8 space-y-7 bg-cardLight dark:bg-darkcard'>
						<Text
							style={Global.text_bold}
							className='text-lg tracking-wide text-center text-secondary dark:text-white'
						>
							Competition
						</Text>
						<View>
							<CompetitionChip />
						</View>
					</View>

					<EventsMenu refreshing={refreshing} setRefreshing={setRefreshing} />

					<View><ReadingsMenu /></View>

					<View><CareerMenu refreshing={refreshing} setRefreshing={setRefreshing} /></View>

					<View className='mb-8'><AdvertisementBoard /></View>

				</View>

			</ScrollView>

		</View>
	);
}

export default MainHome;
