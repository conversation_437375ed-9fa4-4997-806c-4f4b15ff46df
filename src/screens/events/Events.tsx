import React, { useContext, useRef } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import EventChip from './components/EventChip';
import Filters from '../../components/Filters';
import Global from '../../../globalStyle';
import DetailCard from './components/DetailCard';
import { EventContext } from '../../context/EventContext';
import Pagination from './components/Pagination';
import { ClientContext } from '../../context/ClientContext';
import Loader from './components/Loader';
import { FilterContext } from '../../context/FilterContext';

const Events = () => {

    const { showPagination, selectedChip, selectedChipData, loading, onRefresh, refreshing } = useContext(EventContext);
    const { currentEventChip, isAndroid, isMedium, isLarge } = useContext(ClientContext);
    const { eventLocation } = useContext(FilterContext);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Events' index={1} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className='flex-1'
                ref={scrollViewRef}
                refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
                stickyHeaderIndices={[0]}
            >

                <View className='pt-3 pb-4 bg-cardLight dark:bg-secondary'>
                    <EventChip handlePaginationChange={handlePaginationChange} />
                </View>

                <View className='bg-ececec'>
                    <Filters
                        chipFrom={currentEventChip}
                        dateFilter
                        priceFilter
                        modeFilter
                        locationFilter={eventLocation.length > 0}
                        postedByFilter
                        categoryFilter
                        eventSort
                    />
                </View>

                <View className={`space-y-3 ${isMedium ? 'mb-28' : isAndroid ? 'mb-20' : 'mb-24'}`}>

                    <View className='w-full pt-4 pb-2 space-y-4 px-primary'>

                        <Text className='text-text20 text-secondary dark:text-white' style={Global.text_bold}>List Of {selectedChip}</Text>

                        <View className='space-y-[3%]'>
                            {loading ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {[1, 2, 3, 4, 5, 6].map((index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <Loader />
                                            </View>
                                        ))}
                                    </View>
                                ) : (
                                    [1, 2, 3,].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))
                                )
                            ) : selectedChipData?.length > 0 ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {selectedChipData?.map((item, index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <DetailCard data={item} />
                                            </View>
                                        ))}
                                    </View>
                                ) :
                                    selectedChipData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <DetailCard data={item} />
                                        </View>
                                    ))
                            ) : (
                                <View className='flex-row items-center justify-center w-full h-12'>
                                    <Text className='text-text17 text-secondary dark:text-white' style={Global.text_medium}>
                                        No {selectedChip} is available.
                                    </Text>
                                </View>
                            )}
                        </View>

                    </View>

                    {showPagination && <View className='px-1'>
                        <Pagination onPageChange={handlePaginationChange} />
                    </View>}

                </View>

            </ScrollView>

        </View>
    );
};

export default Events;
