import { View, Text, StatusBar, <PERSON><PERSON><PERSON><PERSON>, ScrollView } from 'react-native'
import React, { useContext, useEffect } from 'react'
import { FontAwesome6 } from '@expo/vector-icons'
import Global from '../../../../globalStyle'
import DetailCard from '../components/DetailCard'
import { ClientContext } from '../../../context/ClientContext'

const EventPaymentCancelled = ({ route }: any) => {

    const currentEvent = route.params?.currentEvent || {};
    const { colorScheme, navigation, isAndroid } = useContext(ClientContext);

    useEffect(() => {
        const backAction = () => {
            navigation.navigate("Events");
            return true;
        };

        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            backAction
        );

        return () => backHandler.remove();
    }, []);

    const STATUSBAR_HEIGHT = isAndroid ? StatusBar.currentHeight : 0;

    return (
        <View className='flex-1 bg-white dark:bg-dark' style={{ paddingTop: STATUSBAR_HEIGHT }}>

            <StatusBar
                barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'}
                translucent={true}
                animated={true}
                backgroundColor='transparent'
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexGrow: 1,
                    justifyContent: 'center'
                }}
            >

                <View className='px-primary space-y-2 items-center justify-center py-5'>

                    <View className='w-full flex-col items-center space-y-2'>

                        <View className='h-10 w-10 bg-red-500 rounded-full items-center justify-center'>
                            <FontAwesome6 name="x" size={20} color="white" />
                        </View>

                        <Text
                            className='text-lg text-secondary dark:text-white text-center'
                            style={Global.text_bold}
                        >
                            Payment Failed
                        </Text>

                        <Text className='text-text16 text-secondary dark:text-white text-center' style={Global.text_regular}>Your payment failed, please try again later</Text>

                    </View>

                    <View className='w-full'>
                        <DetailCard confirmationData={currentEvent} paymentCancelled />
                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default EventPaymentCancelled