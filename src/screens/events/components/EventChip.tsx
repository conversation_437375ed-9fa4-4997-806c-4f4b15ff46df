import { View, Text, FlatList, Dimensions } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { Chip } from 'react-native-paper';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import CollegeEventSelectedIcon from '../../../assets/icons/event-icon/CollegeEventSelectedIcon';
import SeminarSelectedIcon from '../../../assets/icons/event-icon/SeminarSelectedIcon';
import WorkshopSelectedIcon from '../../../assets/icons/event-icon/WorkshopSelectedIcon';
import { CollegeEventIcon, CollegeEventIconLight } from '../../../assets/icons/event-icon/CollegeEventIcon';
import { SeminarIcon, SeminarIconLight } from '../../../assets/icons/event-icon/SeminarIcon';
import { WorkshopIcon, WorkshopIconLight } from '../../../assets/icons/event-icon/WorkshopIcon';
import { TouchableOpacity } from 'react-native';
import { EventContext } from '../../../context/EventContext';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface EventChipProps {
    handlePaginationChange: () => void;
}

const EventChip: React.FC<EventChipProps> = ({ handlePaginationChange }) => {

    const { setCurrentEventChip, colorScheme } = useContext(ClientContext);
    const { selectedChip, setSelectedChip } = useContext(EventContext);

    const defaultData = [
        {
            id: 1,
            title: 'College Events',
            icon: selectedChip === 'College Events' ? (colorScheme === 'dark' ? <CollegeEventIcon /> : <CollegeEventSelectedIcon />) : colorScheme === 'dark' ? <CollegeEventIconLight /> : <CollegeEventIcon />,
        },
        {
            id: 2,
            title: 'Seminars',
            icon: selectedChip === 'Seminars' ? (colorScheme === 'dark' ? <SeminarIcon /> : <SeminarSelectedIcon />) : colorScheme === 'dark' ? <SeminarIconLight /> : <SeminarIcon />,
        },
        {
            id: 3,
            title: 'Workshop',
            icon: selectedChip === 'Workshop' ? (colorScheme === 'dark' ? <WorkshopIcon /> : <WorkshopSelectedIcon />) : colorScheme === 'dark' ? <WorkshopIconLight /> : <WorkshopIcon />,
        },
    ]

    const [data, setData] = useState(defaultData);
    const flatListRef = useRef<FlatList>(null);

    useEffect(() => {
        if (selectedChip) {
            const selectedData = defaultData.find(item => item.title === selectedChip);
            const newData = defaultData.filter(item => item.title !== selectedChip);
            if (selectedData) {
                setData([selectedData, ...newData]);
            }
        } else {
            setData(defaultData);
        }
    }, [selectedChip]);

    const handleChipSelection = (chipId: number) => {
        const selectedTitle = defaultData[chipId - 1]?.title;
        setSelectedChip(selectedTitle);
        setCurrentEventChip(selectedTitle);
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
        handlePaginationChange();
    };

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => handleChipSelection(item.id)}
                    style={{
                        borderRadius: 25,
                        backgroundColor: selectedChip === item.title ? colorScheme === 'dark' ? '#fdd066' : '#2d2828' : 'transparent',
                        borderWidth: 0.8,
                        borderColor: selectedChip === item.title ? "transparent" : colorScheme === 'dark' ? "#fff" : "#2d2828"
                    }}
                    className={`h-10 flex-row items-center justify-center space-x-2 border-[0.8px] border-secondary px-[10px] ${selectedChip === item.title ? '#2d2828 bg-secondary dark:bg-primary' : 'bg-transparent dark:border-white'}`}
                >
                    <View>
                        {item.icon}
                    </View>
                    <Text
                        style={Global.text_bold}
                        className={`${selectedChip === item.title ? 'text-primary dark:text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14`}
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        )
    };

    return (
        <View style={{ width: '100%' }}>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
}

export default EventChip;
