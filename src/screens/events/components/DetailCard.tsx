import { View, Text, TouchableOpacity} from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'

import { ClientContext } from '../../../context/ClientContext'
import Badge from '../../../components/Badge'
import Global from '../../../../globalStyle'
import { getDate, getRegistrationEndTime, isExpired, isOngoing, separateTimeFromDateTime } from '../../../helpers/dateTimeFormat'
import Button from '../../../components/Button'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'

interface DetailCardProps {
    confirmationData?: any;
    showConfirmation?: boolean;
    data?: any;
    paymentCancelled?:boolean;
}

const DetailCard: React.FC<DetailCardProps> = ({ data, confirmationData, showConfirmation,paymentCancelled }) => {

    const { colorScheme, navigation, currentEventChip, registeredEvents, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, handleView, registeredItems } = useContext(LikeSaveContext);

    const item = confirmationData ? confirmationData : data;

    const handleEvent = () => {
        navigation.navigate('EventDetails', { currentEvent: item });
        handleView(item?._id, currentEventChip);
    }

    const exploreMoreEvent = () => {
        navigation.navigate('Events');
    }

    const viewBookedEvent = () => {
        navigation.navigate('Profile', {
            screen: 'ProfileHome',
            params: {
                from: 'Enrolled',
                to: 'Events'
            }
        });
    }

    const isPaymentCompleted = (id: string) => {
        const isRegisteredNow = registeredItems?.has(id);
        const isRegisteredPreviously = registeredEvents?.includes(id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={showConfirmation ? undefined : handleEvent}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                        blurRadius={registeredEvents.includes(item._id) ?
                            isExpired(item?.date_time || item?.submission_date)
                                ? 2 : 0
                            : isExpired(item?.date_time || item?.submission_date)
                                ? 2 : 0
                        }
                    />
                    {!paymentCancelled && <View className='absolute bottom-1 left-1'>
                        {isPaymentCompleted(item._id)
                            ? isExpired(item?.date_time)
                                ? <Badge name='Completed' backgroundColor='#00000066' borderColor='#fff' color='#fff' />
                                : isOngoing(item?.date_time)
                                    ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                    : <Badge name='Enrolled' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                            : isExpired(item?.date_time)
                                ? <Badge name='Ended' backgroundColor='#FF2020' borderColor='#fff' color='#fff' />
                                : isOngoing(item?.date_time)
                                    ? <Badge name='Ongoing' backgroundColor='#F19E19' borderColor='#fff' color='#fff' />
                                    : null
                        }
                    </View>}

                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[52%]'><Badge height={30} name={currentEventChip} /></View>

                    <View className='w-[45%] h-full items-center'>
                        <Text
                            className='w-full leading-6 text-secondary dark:text-white text-wrap'
                            style={Global.text_medium}
                        >
                            {getRegistrationEndTime(item?.registration_end_date) === 'na'
                                ? 'Registeration closed'
                                : `Reg. ends on : ${getRegistrationEndTime(item?.registration_end_date)}`
                            }
                        </Text>
                    </View>

                </View>

                <View className='flex-row items-start justify-between'>

                    <View className='w-[52%] space-y-2'>

                        <Text
                            className='text-lg text-secondary dark:text-white'
                            style={Global.text_bold}
                            numberOfLines={3}
                            ellipsizeMode='tail'
                        >
                            {item?.title}
                        </Text><Text
                            className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                            style={Global.text_medium}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            By {item?.posted_by}
                        </Text>

                    </View>

                    <View className='w-[45%] space-y-3'>

                        <View className='flex-row items-center space-x-2'>
                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>{getDate(item?.date_time)}</Text>
                        </View>

                        <View className='flex-row items-center space-x-2'>
                            <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>
                                {separateTimeFromDateTime(item?.date_time)}
                            </Text>
                        </View>

                        <View className='flex-row items-center space-x-2'>
                            <Feather name='users' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>No. of Seats : {item?.number_of_seats}</Text>
                        </View>

                        {item?.location ?
                            <View className='flex-row items-center space-x-2'>
                                <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>{item?.location}</Text>
                            </View>
                            : <View className='flex-row items-center space-x-2'>
                                <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>Online</Text>
                            </View>
                        }

                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full h-10'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, currentEventChip)}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

            {showConfirmation && <View>
                <Button
                    title='Explore more upcoming Events'
                    onPress={exploreMoreEvent}
                    borderColor='gray'
                    color={colorScheme === 'dark' ? "white" : 'black'}
                    bgColor='transparent'
                    height={57}
                />
            </View>}

            {paymentCancelled && <View className='mb-2'>
                <Button
                    title='Back to events page'
                    onPress={exploreMoreEvent}
                    height={57}
                />
            </View>}

            {showConfirmation && <View className='mb-2'>
                <Button
                    title='View booked Event details'
                    onPress={viewBookedEvent}
                    height={57}
                />
            </View>}

        </View>
    )
}

export default DetailCard;