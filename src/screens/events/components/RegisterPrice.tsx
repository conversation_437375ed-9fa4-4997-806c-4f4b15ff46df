import { View, Text } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import Global from '../../../../globalStyle'
import Button from '../../../components/Button'
import { ClientContext } from '../../../context/ClientContext'

import Entypo from '@expo/vector-icons/FontAwesome6'
import Feather from '@expo/vector-icons/Feather'
import { getDate, getDateDay, isExpired, isRegistrationExpired } from '../../../helpers/dateTimeFormat'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { ClientAxiosInstance } from '../../../lib/axiosInstance'

interface RegisterPriceProps {
    currentEvent: any;
    selectedChip: string;
    setFeedbackLoading: (value: boolean) => void;
    setIsFeedbackModalVisible: (value: boolean) => void;
    setFeedback: (data: any[]) => void;
    getFeedbackTypeId: (val: string) => string;
}

const RegisterPrice: React.FC<RegisterPriceProps> = ({ currentEvent, selectedChip, getFeedbackTypeId, setFeedbackLoading, setIsFeedbackModalVisible, setFeedback }) => {

    const { navigation, colorScheme, registeredEvents, clientId } = useContext(ClientContext);
    const { registeredItems } = useContext(LikeSaveContext);

    const handleRegisteration = () => {
        navigation.navigate('EventCheckout', { selectedChip: selectedChip, currentEvent: currentEvent });
    }

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(currentEvent?._id);
        const isRegisteredPreviously = registeredEvents?.includes(currentEvent?._id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    const getRemainingSeats = () => {
        return currentEvent?.number_of_seats - currentEvent?.enrolled_count;
    }

    const feedbackSubmited = async () => {

        const feedbackTypeId = getFeedbackTypeId(selectedChip);
        if (!feedbackTypeId) {
            console.error(`Invalid feedback type: ${selectedChip}`);
            return;
        }

        try {
            const response = await ClientAxiosInstance.get(`${feedbackTypeId}/${clientId}/${currentEvent?._id}`);
            setIsFeedbackModalVisible(!response.data.data.is_feedback);

        } catch (error: any) {
            console.log("Checking feedback is submitted? : ", error.response.data);
            if (error.response.data.message.indexOf('Feedback not found for this user and') >= 0) setIsFeedbackModalVisible(true);
        }
    }

    const fetchFeedback = async () => {

        const feedbackTypeId = getFeedbackTypeId(selectedChip);
        if (!feedbackTypeId) {
            console.error(`Invalid feedback type: ${selectedChip}`);
            return;
        }

        try {
            const response = await ClientAxiosInstance.get(
                `${feedbackTypeId}/searchApprovedFeedback?feedback_approval_status=Approved`
            );
            setFeedback(response.data.data);

        } catch (error) {
            console.error('Error fetching feedback:', error);
        } finally {
            setFeedbackLoading(false);
        }
    };

    useEffect(() => {
        if (currentEvent?._id) {
            fetchFeedback();
            if (isPaymentCompleted() && isExpired(currentEvent?.date_time)) feedbackSubmited();
        }
    }, [currentEvent?._id])

    return (
        <View className='space-y-5'>

            {!isExpired(currentEvent?.date_time) ? (<View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-secondary'>
                <Text style={Global.text_bold} className='text-xl tracking-wide text-secondary dark:text-white'>
                    Price : &nbsp;
                </Text>
                <Text style={Global.text_bold} className='text-xl tracking-wide line-through text-secondary dark:text-white'>
                    &#8377;{currentEvent?.price}
                </Text>
                {currentEvent?.discount_price ? <Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;&#8377;{currentEvent?.discount_price}</Text> :
                    <Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;Free</Text>
                }
                <Text style={Global.text_bold} className='text-secondary dark:text-white text-text17 tracking-wide'>&nbsp;+ GST/-</Text>
            </View>
            ) : (
                <View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-darkcard'>
                    <Text style={Global.text_medium} className='tracking-wide text-secondary dark:text-white text-text16'>
                        The session has ended
                    </Text>
                </View>
            )}

            {!isPaymentCompleted() && !isRegistrationExpired(currentEvent?.registration_end_date) && getRemainingSeats() &&
                <View className='w-full'>
                    <Button title='Register Now' height={57} onPress={handleRegisteration} />
                </View>
            }

            {isPaymentCompleted() && !isExpired(currentEvent?.date_time) &&

                <View className='items-center py-6 space-y-4 rounded-md bg-cardLight dark:bg-secondary'>

                    <View className='h-12 w-12 bg-[#2baa16] rounded-full items-center justify-center'>
                        <Entypo name='check' size={30} color='white' />
                    </View>

                    <Text
                        className='tracking-wide text-center text-text16 text-secondary dark:text-white'
                        style={Global.text_regular}
                    >
                        Thank you for booking this event.
                    </Text>

                    <View className='flex-row items-center space-x-2'>
                        <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                        <Text
                            className='tracking-wide text-center text-text16 text-secondary dark:text-white'
                            style={Global.text_medium}
                        >
                            {getDateDay(currentEvent?.date_time)}
                        </Text>
                    </View>

                </View>}

        </View>
    )
}

export default RegisterPrice