import { View, Text, Dimensions, StyleSheet, Image } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import Entypo from '@expo/vector-icons/Entypo';
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons';
import Accordion from 'react-native-accordion-wrapper';
import { ClientContext } from '../../../context/ClientContext';

import HTMLView from 'react-native-htmlview';
import Global from '../../../../globalStyle';
import { styles } from '../../style/styles';
import FaqImage from '../../../components/FaqImage';

interface DescriptionFaqsProps {
    selectedChip: string;
    currentEvent: any;
}

interface CompetitionFaqs {
    title: string;
    child: JSX.Element;
}

const DescriptionFaqs: React.FC<DescriptionFaqsProps> = ({ selectedChip, currentEvent }) => {

    const { colorScheme } = useContext(ClientContext);

    const competitionDescription = [
        {
            title: `${selectedChip} Description`,
            child: (
                <View className='bottom-1 space-y-4 w-full bg-cardLight dark:bg-darkcard rounded-b-md px-[10px]'>
                    <Text
                        className='tracking-wide text-center text-secondary dark:text-white text-text16'
                        style={Global.text_bold}
                    >
                        Helping Lawyers in building skills and knowledge in Legal studies.
                    </Text>
                    <View className='h-[100px] w-full self-center items-center'>
                        <FaqImage/>
                    </View>
                    <View className='mb-3'>
                        <HTMLView
                            value={currentEvent?.description}
                            style={{
                                backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#f8f6f3',
                                borderBottomLeftRadius: 5,
                                borderBottomRightRadius: 5
                            }}
                            stylesheet={styles(colorScheme)}
                        />
                    </View>
                </View>
            )
        }
    ];

    const [competitionFaqs, setCompetitionFaqs] = useState<CompetitionFaqs[]>([]);

    const fetchCompetitionFaqs = () => {
        const faqs: CompetitionFaqs[] = currentEvent?.faqs?.map((faq: any, index: number) => ({
            title: faq?.question,
            child: (
                <View key={index}>
                    <Text className={`bg-cardLight dark:bg-secondary py-2 px-3 text-secondary dark:text-white text-text15 tracking-wider ${index + 1 === currentEvent?.faqs?.length ? 'rounded-b-md bottom-1 pb-5' : ''}`}>
                        {faq?.answer}
                    </Text>
                </View>
            )
        })) || [];
        setCompetitionFaqs(faqs);
    };

    useEffect(() => {
        if (currentEvent) {
            fetchCompetitionFaqs();
        }
    }, [currentEvent]);

    const faqs = [
        {
            title: 'FAQs',
            child: (
                <View className='bottom-1'>
                    {competitionFaqs.length > 0 && <Accordion
                        dataSource={competitionFaqs}
                        rightChevronIcon={<Entypo name='plus' size={21} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                        headerItemsStyle={{
                            backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#F8F6F3',
                            height: 70,
                            paddingHorizontal: 10,
                            borderBottomWidth: 0,
                        }}
                        headerTitleLabelStyle={{
                            color: colorScheme === 'dark' ? "#fff" : '#111',
                            fontSize: 16,
                            fontFamily: 'DMSans-Bold',
                            letterSpacing: 0.5
                        }}
                    />}
                </View>
            )
        }
    ];

    return (
        <View className='space-y-3'>
            {competitionDescription.length > 0 && <View>
                <Accordion
                    dataSource={competitionDescription}
                    rightChevronIcon={<SimpleLineIcons name='arrow-up' size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                    headerItemsStyle={{
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#F8F6F3',
                        height: 70,
                        paddingHorizontal: 10,
                        borderRadius: 5,
                        borderBottomWidth: 0,
                    }}
                    headerTitleLabelStyle={{
                        color: colorScheme === 'dark' ? "#fff" : '#111',
                        fontSize: 16,
                        fontFamily: 'DMSans-Bold',
                        letterSpacing: 0.5
                    }}
                />
            </View>}
            {currentEvent?.faqs?.length > 0 && <View>
                <Accordion
                    dataSource={faqs}
                    rightChevronIcon={<SimpleLineIcons name='arrow-up' size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                    headerItemsStyle={{
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#F8F6F3',
                        height: 70,
                        paddingHorizontal: 10,
                        borderRadius: 5,
                        borderBottomWidth: 0,
                    }}
                    headerTitleLabelStyle={{
                        color: colorScheme === 'dark' ? "#fff" : '#111',
                        fontSize: 16,
                        fontFamily: 'DMSans-Bold',
                        letterSpacing: 0.5
                    }}
                />
            </View>}
        </View>
    );
};

export default DescriptionFaqs;
