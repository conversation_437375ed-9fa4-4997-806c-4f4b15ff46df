import { View, Text, TouchableOpacity } from 'react-native';
import React, { useContext } from 'react';

import Feather from '@expo/vector-icons/Feather';
import { EventContext } from '../../../context/EventContext';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';

const Pagination = ({ onPageChange }: any) => {

    const { colorScheme } = useContext(ClientContext);
    const { setCurrentPage, totalPages, currentPage } = useContext(EventContext);

    const handleNextPage = () => {
        if (totalPages > currentPage) {
            setCurrentPage(currentPage + 1);
            onPageChange();
        }
    }

    const handlePreviousPage = () => {
        if (currentPage !== 1) {
            setCurrentPage(currentPage - 1);
            onPageChange();
        }
    }

    const handlePageClick = (pageNumber: number) => {
        setCurrentPage(pageNumber);
        onPageChange();
    }

    const renderPageNumbers = () => {
        const pageNumbers = [];

        if (totalPages === 3) {
            for (let i = 1; i <= 3; i++) {
                pageNumbers.push(
                    <TouchableOpacity
                        key={i}
                        onPress={() => handlePageClick(i)}
                        activeOpacity={0.8}
                        className={`h-12 w-12 rounded-full items-center justify-center border ${currentPage === i ? 'bg-primary border-none' : 'bg-transparent border-secondary dark:border-white'}`}
                    >
                        <Text className={`text-xl ${currentPage === i ? 'text-secondary' : 'text-secondary dark:text-white'}`} style={Global.text_medium}>
                            {i}
                        </Text>
                    </TouchableOpacity>
                );
            }

        } else if (totalPages >= 3 && (currentPage === totalPages - 1 || currentPage === totalPages)) {
            pageNumbers.push(
                <TouchableOpacity
                    key="dots"
                    activeOpacity={0.8}
                    className='h-12 w-12 rounded-full items-center justify-center pb-3 border bg-transparent border-secondary dark:border-white'
                >
                    <Text className='text-xl text-secondary dark:text-white tracking-widest' style={Global.text_medium}>...</Text>
                </TouchableOpacity>
            );

            for (let i = totalPages - 1; i <= totalPages; i++) {
                pageNumbers.push(
                    <TouchableOpacity
                        key={i}
                        onPress={() => handlePageClick(i)}
                        activeOpacity={0.8}
                        className={`h-12 w-12 rounded-full items-center justify-center border ${currentPage === i ? 'bg-primary border-none' : 'bg-transparent border-secondary dark:border-white'}`}
                    >
                        <Text className={`text-xl ${currentPage === i ? 'text-secondary' : 'text-secondary dark:text-white'}`} style={Global.text_medium}>
                            {i}
                        </Text>
                    </TouchableOpacity>
                );
            }

        } else if (totalPages >= 3) {
            pageNumbers.push(
                <TouchableOpacity
                    key={currentPage}
                    onPress={() => handlePageClick(currentPage)}
                    activeOpacity={0.8}
                    className='h-12 w-12 rounded-full items-center justify-center border bg-primary'
                >
                    <Text className='text-xl text-secondary' style={Global.text_medium}>{currentPage}</Text>
                </TouchableOpacity>
            );

            pageNumbers.push(
                <TouchableOpacity
                    key="dots"
                    activeOpacity={0.8}
                    className='h-12 w-12 rounded-full items-center justify-center pb-3 border bg-transparent border-secondary dark:border-white'
                >
                    <Text className='text-xl text-secondary dark:text-white tracking-widest' style={Global.text_medium}>...</Text>
                </TouchableOpacity>
            );

            pageNumbers.push(
                <TouchableOpacity
                    key={totalPages}
                    onPress={() => handlePageClick(totalPages)}
                    activeOpacity={0.8}
                    className='h-12 w-12 rounded-full items-center justify-center border bg-transparent border-secondary dark:border-white'
                >
                    <Text className='text-xl text-secondary dark:text-white' style={Global.text_medium}>{totalPages}</Text>
                </TouchableOpacity>
            );

        } else {
            for (let i = 1; i < 3; i++) {
                pageNumbers.push(
                    <TouchableOpacity
                        key={i}
                        onPress={() => handlePageClick(i)}
                        activeOpacity={0.8}
                        className={`h-12 w-12 rounded-full items-center justify-center border ${currentPage === i ? 'bg-primary border-none' : 'bg-transparent border-secondary dark:border-white'}`}
                    >
                        <Text className={`text-xl ${currentPage === i ? 'text-secondary' : 'text-secondary dark:text-white'}`} style={Global.text_medium}>
                            {i}
                        </Text>
                    </TouchableOpacity>
                );
            }
        }

        return pageNumbers;
    };

    return (
        <View className='w-full mb-5 px-primary flex-row items-center justify-between'>

            <TouchableOpacity
                onPress={handlePreviousPage}
                activeOpacity={0.8}
                className='h-12 w-12 rounded-full items-center justify-center pr-1 border bg-transparent border-secondary dark:border-white'
            >
                <Feather name='chevron-left' size={28} color={colorScheme === 'dark' ? '#fff' : '#111'} />
            </TouchableOpacity>

            {renderPageNumbers()}

            <TouchableOpacity
                onPress={handleNextPage}
                activeOpacity={0.8}
                className='h-12 w-12 rounded-full items-center justify-center pl-1 border bg-transparent border-secondary dark:border-white'
            >
                <Feather name='chevron-right' size={28} color={colorScheme === 'dark' ? '#fff' : '#111'} />
            </TouchableOpacity>

        </View>
    )
}

export default Pagination;