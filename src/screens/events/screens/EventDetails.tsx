import { View, Text, TouchableOpacity, ScrollView } from 'react-native'
import React, { useContext, useState } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons'
import { Image } from 'expo-image';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import Header from '../../../components/Header';
import CustomStatusBar from '../../../components/CustomStatusBar';
import RegisterPrice from '../components/RegisterPrice';
import DescriptionFaqs from '../components/DescriptionFaqs';
import { getDateDay } from '../../../helpers/dateTimeFormat';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import Feedbacks from '../../../components/Feedbacks';
import GetFeedback from '../../../components/GetFeedback';

const EventDetails = ({ route }: any) => {

    const { colorScheme, currentEventChip, registeredEvents } = useContext(ClientContext);
    const { isCardSaved, saveCard, registeredItems } = useContext(LikeSaveContext);

    const currentEvent = route?.params?.currentEvent || {};

    const getRemainingSeats = () => {
        const remainingSeats = currentEvent?.number_of_seats - currentEvent?.enrolled_count;
        return remainingSeats < 1 ? "Fully Booked" : remainingSeats;
    }

    const [feedback, setFeedback] = useState<any[]>([]);
    const [isFeedbackModalVisible, setIsFeedbackModalVisible] = useState(false);
    const [feedbackLoading, setFeedbackLoading] = useState(true);

    const getFeedbackTypeId = (chip: string) => {
        const feedbackMapping: { [key: string]: string } = {
            'College Events': 'collegeeventfeedback',
            'Seminars': 'seminarfeedback',
            'Workshop': 'workshopfeedback',
        };
        return feedbackMapping[chip] || '';
    };

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(currentEvent?._id);
        const isRegisteredPreviously = registeredEvents?.includes(currentEvent?._id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name={currentEventChip} index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='w-full h-full py-3 mb-10 space-y-5'>

                    <View style={{ height: widthPercentageToDP(50) }} className='px-primary'>
                        <Image
                            source={currentEvent?.thumbnail_image_path ?
                                { uri: currentEvent?.thumbnail_image_path } :
                                require('../../../assets/images/placeholder-thumbnail.png')
                            }
                            style={{
                                width: '100%',
                                height: '100%',
                                borderRadius: 5
                            }}
                        />
                    </View>

                    <View className='px-primary'>

                        <Text style={Global.text_bold} className='text-text20 text-secondary dark:text-white'>{currentEventChip} Details</Text>

                        <View className='h-[60px] w-full flex-row items-center justify-between'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Date</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={2}
                                    ellipsizeMode='tail'
                                >
                                    {getDateDay(currentEvent?.date_time)}
                                </Text>
                            </View>
                        </View>

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        <View className='min-h-[60px] w-full flex-row items-center justify-between py-2'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Event Name</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={2}
                                    ellipsizeMode='tail'
                                >
                                    {currentEvent?.title}
                                </Text>
                            </View>
                        </View>

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        {!isPaymentCompleted() && <View className='h-[60px] flex-row items-center justify-between'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Seats Available</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={1}
                                    ellipsizeMode='tail'
                                >
                                    {getRemainingSeats()}
                                </Text>
                            </View>
                        </View>}

                        {!isPaymentCompleted() && <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />}

                        <View className='h-[60px] flex-row items-center justify-between'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Mode</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={1}
                                    ellipsizeMode='tail'
                                >
                                    {capitalizeMode(currentEvent?.mode) || '-'}
                                </Text>
                            </View>
                        </View>

                        {currentEvent?.mode?.toLowerCase() === 'offline' && <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />}

                        {currentEvent?.mode?.toLowerCase() === 'offline' ? (
                            <View className='h-[60px] w-full flex-row items-center justify-between'>
                                <View className='w-2/5'>
                                    <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Location</Text>
                                </View>
                                <View className='items-end w-3/5'>
                                    <Text
                                        style={Global.text_regular}
                                        className='tracking-wider text-text17 text-secondary dark:text-white'
                                        numberOfLines={1}
                                        ellipsizeMode='tail'
                                    >
                                        {capitalizeMode(currentEvent?.location) || '-'}
                                    </Text>
                                </View>
                            </View>
                        ) : null}

                    </View>

                    <View className='px-primary'>
                        <RegisterPrice
                            currentEvent={currentEvent}
                            selectedChip={currentEventChip}
                            setFeedback={setFeedback}
                            setIsFeedbackModalVisible={setIsFeedbackModalVisible}
                            getFeedbackTypeId={getFeedbackTypeId}
                            setFeedbackLoading={setFeedbackLoading}
                        />
                    </View>

                    <View className='items-center w-full px-primary'>
                        <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(currentEvent._id, currentEventChip)}>
                            <Ionicons
                                name={isCardSaved(currentEvent?._id) ? 'heart' : 'heart-outline'}
                                size={25}
                                color={isCardSaved(currentEvent?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                            />
                            <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(currentEvent?._id) ? 'Saved' : 'Save for later'}</Text>
                        </TouchableOpacity>
                    </View>

                    <View className='px-primary'>
                        <DescriptionFaqs selectedChip={currentEventChip} currentEvent={currentEvent} />
                    </View>

                    <View className='w-full'>
                        <Feedbacks
                            data={feedback.reverse()}
                            selectedChip={currentEventChip}
                            loading={feedbackLoading}
                        />
                    </View>

                </View>

            </ScrollView>

            <GetFeedback
                visible={isFeedbackModalVisible}
                onClose={() => setIsFeedbackModalVisible(false)}
                chip_id={currentEvent?._id}
                feedback_for={getFeedbackTypeId(currentEventChip)}
            />

        </View>
    )
}

export default EventDetails