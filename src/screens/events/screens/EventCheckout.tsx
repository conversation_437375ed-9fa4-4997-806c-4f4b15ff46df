import { ScrollView, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext, useEffect, useMemo, useState } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar'
import Header from '../../../components/Header'
import Global from '../../../../globalStyle'
import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../../../context/ClientContext'
import Button from '../../../components/Button'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import PaymentButton from '../../../components/PaymentButton'
import { ClientAxiosInstance } from '../../../lib/axiosInstance'
import { Image } from 'expo-image';
import { GlobalContext } from '../../../context/GlobalProvider'
import { Toast } from '../../../components/Toast'

const EventCheckout = ({ route }: any) => {

    const currentEvent = route?.params?.currentEvent || {};

    const { colorScheme, currentEventChip, navigation, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, setRegisteredItems } = useContext(LikeSaveContext);
    const { setLoading } = useContext(GlobalContext);

    const [totalAmount, setTotalAmount] = useState(0);

    let formattedChip = currentEventChip.replace(/\s+/g, '').toLowerCase();

    if (formattedChip === 'collegeevents' || formattedChip === 'seminars') {
        formattedChip = formattedChip.slice(0, -1);
    }

    const chip = useMemo(() => {
        switch (currentEventChip) {
            case 'College Events': return 'collegeevent';
            case 'Workshop': return 'workshop';
            case 'Seminars': return 'seminar';
            default: return null;
        }
    }, [currentEventChip]);

    const handlePaymentCancel = () => {
        navigation.goBack();
    }

    const handleFreePayment = async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.post(`/${chip}/register-${chip}/${currentEvent?._id}`);
            console.log(response.data.message)

            setRegisteredItems(prevItems => {
                const updatedSet = new Set(prevItems);
                updatedSet.add(currentEvent?._id);
                return updatedSet;
            });
            setLoading(false);
            navigation.navigate('EventPaymentConfirmation', { currentEvent: currentEvent });

            Toast.show({
                type: 'success',
                message: 'Registeration successful',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/images/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

        } catch (error: any) {
            console.log("Handle free payment error : ", error.response.data);
            setLoading(false);
            Toast.show({
                type: 'error',
                message: 'You have already registered',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/images/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        }
    }

    const handleTotalAmount = () => {
        const [gst, discount] = [currentEvent?.gst, currentEvent?.discount_price];
        let gst_amount = 0;
        if (gst !== undefined && gst !== null) {
            gst_amount = gst;
            const gstAmount = discount * (gst_amount / 100);
            const totalPrice = discount + gstAmount;
            setTotalAmount(totalPrice);
        }
        else {
            const gstAmount = discount * (0 / 100);
            const totalPrice = discount + gstAmount;
            setTotalAmount(totalPrice);
        }
    }

    const handleGstAmount = (discount: number, gst: number) => {
        let gst_amount = 0;
        if (gst !== undefined && gst !== null) {
            gst_amount = gst;
            const gstAmount = discount * (gst_amount / 100);
            return gstAmount;
        }
        else {
            const gstAmount = discount * (0 / 100);
            return gstAmount;
        }
    }

    useEffect(() => {
        handleTotalAmount();
    }, [])

    return (

        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name={currentEventChip} index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='mb-5 space-y-5'>

                    <View className='py-4 bg-ececec px-primary'>

                        <View className='flex-row items-center w-full px-2 py-2 bg-white rounded-md dark:bg-secondary' style={{ height: isLarge ? 180 : 110 }}>

                            <View className='h-full w-[50%]'>
                                <Image
                                    source={currentEvent?.thumbnail_image_path ?
                                        { uri: currentEvent?.thumbnail_image_path } :
                                        require('../../../assets/images/placeholder-thumbnail.png')
                                    }
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        borderRadius: 5
                                    }}
                                />
                                <Text className='text-secondary absolute top-[2px] left-1' style={Global.text_bold}>{currentEventChip}</Text>
                            </View>

                            <View className='h-full w-[50%] px-2 flex-col justify-between'>
                                <Text
                                    className='text-secondary dark:text-white text-text14'
                                    style={Global.text_bold}
                                    numberOfLines={2}
                                    ellipsizeMode='tail'
                                >
                                    {currentEvent?.title}
                                </Text>
                                <Text
                                    className='text-secondary dark:text-white text-text14'
                                    style={Global.text_bold}
                                >
                                    &#8377; {currentEvent?.price}
                                </Text>
                                <View className='items-start w-full'>
                                    <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(currentEvent._id, currentEventChip)}>
                                        <Ionicons name={isCardSaved(currentEvent?._id) ? 'heart' : 'heart-outline'}
                                            size={23}
                                            color={isCardSaved(currentEvent?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'} />

                                        <Text className='text-text14 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(currentEvent?._id) ? 'Saved' : 'Save for later'}</Text>

                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>

                    </View>

                    <View className='space-y-5 px-primary'>

                        <View>

                            <Text style={Global.text_bold} className='mb-6 text-text20 text-secondary dark:text-white'>Order Summary</Text>

                            <View className='h-[50px] flex-row items-start justify-between'>
                                <Text style={Global.text_bold} className='tracking-wide text-text17 text-secondary dark:text-white'>Price</Text>
                                <Text style={Global.text_medium} className='tracking-wider text-text17 text-secondary dark:text-white'>{currentEvent?.price || 0}</Text>
                            </View>

                            <View className='h-[50px] flex-row items-start justify-between'>
                                <Text style={Global.text_bold} className='tracking-wide text-text17 text-secondary dark:text-white'>Discounted Price</Text>
                                <Text style={Global.text_medium} className='tracking-wider text-text17 text-secondary dark:text-white'>{currentEvent?.discount_price || 0}</Text>
                            </View>

                            <View className='h-[50px] flex-row items-start justify-between'>
                                <Text style={Global.text_bold} className='tracking-wide text-text17 text-secondary dark:text-white'>GST ({currentEvent?.gst || 0}%)</Text>
                                <Text style={Global.text_medium} className='tracking-wider text-text17 text-secondary dark:text-white'>{handleGstAmount(currentEvent?.discount_price, currentEvent?.gst)?.toFixed(2)}</Text>
                            </View>

                            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                            <View className='h-[50px] flex-row items-end justify-between'>
                                <Text style={Global.text_bold} className='tracking-wide text-text17 text-secondary dark:text-white'>Total</Text>
                                <Text style={Global.text_medium} className='tracking-wider text-text17 text-secondary dark:text-white'>{totalAmount.toFixed(2)}</Text>
                            </View>

                        </View>


                        <View className='flex-row items-center justify-center py-6 rounded-md bg-cardLight dark:bg-secondary'>
                            <Text style={Global.text_bold} className='text-xl tracking-wide text-secondary dark:text-white'>
                                Price : &nbsp;

                            </Text>
                            <Text style={Global.text_bold} className='text-xl tracking-wide line-through text-secondary dark:text-white'>
                                &#8377;{currentEvent?.price}
                            </Text>
                            {currentEvent?.discount_price ? <Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;&#8377;{totalAmount.toFixed(2)}</Text> :
                                <Text style={Global.text_bold} className='text-xl tracking-wide text-primary'>&nbsp;Free</Text>
                            }
                        </View>

                        <View className='w-full'>
                            {totalAmount > 0 ?
                                <PaymentButton
                                    loading={false}
                                    total={totalAmount}
                                    category={formattedChip}
                                    navigateToConfirm='EventPaymentConfirmation'
                                    navigateToCancel='EventPaymentCancelled'
                                    data={currentEvent}
                                    type='currentEvent'
                                />
                                : <Button
                                    title={`Free Registration`}
                                    height={57}
                                    onPress={handleFreePayment}
                                    color='#2d2828'
                                    bgColor='#fdd066'
                                />
                            }
                        </View>

                        <View className='w-full'>
                            <Button
                                title='Cancel Payment'
                                height={57}
                                onPress={handlePaymentCancel}
                                color='#E5554E'
                                borderColor='#E5554E'
                                bgColor='transparent'
                            />
                        </View>

                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default EventCheckout