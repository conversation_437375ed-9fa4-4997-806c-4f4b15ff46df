import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Global from '../../../../globalStyle';
import Badge from '../../../components/Badge';
import { ClientContext } from '../../../context/ClientContext';
import { Feather } from '@expo/vector-icons';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import { formatDate } from '../../../helpers/dateTimeFormat';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { ReadingsContext } from '../../../context/ReadingsContext';
import { Image } from 'expo-image';

interface ReadingsCardProps {
    data?: any;
    handleNewReadingDetail?: (item: any) => void;
}

const ReadingsCard: React.FC<ReadingsCardProps> = ({ data, handleNewReadingDetail }) => {

    const { colorScheme, currentReadingsChip, navigation } = useContext(ClientContext);
    const { isCardSaved, saveCard, handleView } = useContext(LikeSaveContext);
    const { selectedChipData } = useContext(ReadingsContext);

    const handleReadingsCard = () => {
        if (handleNewReadingDetail) handleNewReadingDetail(data);
        else {
            handleView(data?._id, currentReadingsChip);
            navigation.navigate('ReadingDetail', { item: data, selectedChipData: selectedChipData });
        }
    };

    function calculateReadTime(content: string): number {
        const plainText = content?.replace(/<\/?[^>]+(>|$)/g, "");
        const wordCount = plainText?.split(/\s+/)?.filter(Boolean)?.length;
        const wordsPerMinute = 150;
        const readTime = Math.ceil(wordCount / wordsPerMinute);

        return readTime;
    };

    return (
        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleReadingsCard}>

                <View className='h-[185] max-h-64'>
                    <Image
                        source={data?.thumbnail_image_path ?
                            { uri: data?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={styles.cardImage}
                        contentFit='cover'
                    />
                </View>

                <View>
                    <Badge height={30} name={currentReadingsChip} />
                </View>

                <Text
                    style={Global.text_bold}
                    className='text-text15 text-secondary dark:text-white'
                    numberOfLines={2}
                    ellipsizeMode='tail'
                >
                    {data?.article_title || data?.news_title || data?.blog_title}
                </Text>

                <View className='flex-row items-center space-x-3'>

                    <View>
                        <Badge height={30} name={`${calculateReadTime(data?.news_content || data?.blog_content || data?.article_content || data?.journal_content)} Min Read`} />
                    </View>

                    <View className='flex-row items-center space-x-2'>
                        <Ionicons name="eye-outline" size={20} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                        <Text style={Global.text_medium} ellipsizeMode='tail' numberOfLines={1} className='text-text14 text-secondary dark:text-white'>{data?.views_count} Views</Text>
                    </View>

                </View>

                <View className='flex-row items-center justify-between w-full'>

                    <View className='w-[50%] flex-row items-center space-x-2 pr-1'>
                        {data?.author_image_path ? (
                            <Image source={{ uri: data?.author_image_path }} style={styles.authorImage} contentFit='cover' />
                        ) :
                            <View className='w-7 h-7 rounded-full items-center justify-center bg-[#a8a8a850]'>
                                <Feather name="user" size={17} color="black" />
                            </View>
                        }
                        <Text style={Global.text_bold} ellipsizeMode='tail' numberOfLines={1} className='text-black dark:text-white'>{capitalizeMode(data?.author_name)}</Text>
                    </View>

                    <View className='w-[50%] items-end pl-1'>
                        <Text style={Global.text_medium} ellipsizeMode='tail' numberOfLines={1} className='text-black dark:text-white'>{formatDate(data?.createdAt)}</Text>
                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(data._id, currentReadingsChip)}>
                    <Ionicons
                        name={isCardSaved(data?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(data?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{ isCardSaved(data?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    cardImage: {
        width: '100%',
        height: '100%',
        borderRadius: 6,
    },
    authorImage: {
        width: 28,
        height: 28,
        borderRadius: 25
    },
});

export default ReadingsCard;