import { View, Text, FlatList, Dimensions, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { BlogIconLight, BlogIcon } from '../../../assets/icons/reading-icon/BlogIcon';
import BlogSelectedIcon from '../../../assets/icons/reading-icon/BlogSelectedIcon';
import NewsSelectedIcon from '../../../assets/icons/reading-icon/NewsSelectedIcon';
import { NewsIcon, NewsIconLight } from '../../../assets/icons/reading-icon/NewsIcon';
import ArticlesSelectedIcon from '../../../assets/icons/reading-icon/ArticlesSelectedIcon';
import { JournalsIconLight, JournalsIcon } from '../../../assets/icons/reading-icon/JournalsIcon';
import JournalsSelectedIcon from '../../../assets/icons/reading-icon/JournalsSelectedIcon';
import { ArticlesIcon, ArticlesIconLight } from '../../../assets/icons/reading-icon/ArticlesIcon';
import { ReadingsContext } from '../../../context/ReadingsContext';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface ReadingsChipProps {
    handlePaginationChange: () => void;
}

const ReadingsChip: React.FC<ReadingsChipProps> = ({ handlePaginationChange }) => {

    const { colorScheme, setCurrentReadingsChip } = useContext(ClientContext);
    const { selectedChip, setSelectedChip } = useContext(ReadingsContext);

    const defaultData = [
        {
            id: 1,
            title: 'Blogs',
            icon: selectedChip === 'Blogs' ? colorScheme === 'dark' ? <BlogIcon /> : <BlogSelectedIcon /> : colorScheme === 'dark' ? <BlogIconLight /> : <BlogIcon />,
        },
        {
            id: 2,
            title: 'News',
            icon: selectedChip === 'News' ? colorScheme === 'dark' ? <NewsIcon /> : <NewsSelectedIcon /> : colorScheme === 'dark' ? <NewsIconLight /> : <NewsIcon />,
        },
        {
            id: 3,
            title: 'Articles',
            icon: selectedChip === 'Articles' ? colorScheme === 'dark' ? <ArticlesIcon /> : <ArticlesSelectedIcon /> : colorScheme === 'dark' ? <ArticlesIconLight /> : <ArticlesIcon />,
        },
        {
            id: 4,
            title: 'Journals',
            icon: selectedChip === 'Journals' ? colorScheme === 'dark' ? <JournalsIcon /> : <JournalsSelectedIcon /> : colorScheme === 'dark' ? <JournalsIconLight /> : <JournalsIcon />,
        },
    ]

    const [data, setData] = useState(defaultData);
    const flatListRef = useRef<FlatList>(null);

    useEffect(() => {
        if (selectedChip) {
            const selectedData = defaultData.find(item => item.title === selectedChip);
            const newData = defaultData.filter(item => item.title !== selectedChip);
            if (selectedData) {
                setData([selectedData, ...newData]);
            }
        } else {
            setData(defaultData);
        }
    }, [selectedChip]);

    const handleChipSelection = (chipId: number) => {
        setSelectedChip(defaultData[chipId - 1]?.title);
        setCurrentReadingsChip(defaultData[chipId - 1]?.title);
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
        handlePaginationChange();
    }

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => handleChipSelection(item.id)}
                    style={{
                        borderRadius: 25,
                        backgroundColor: selectedChip === item.title ? colorScheme === 'dark' ? '#fdd066' : '#2d2828' : 'transparent',
                        borderWidth: 0.8,
                        borderColor: selectedChip === item.title ? "transparent" : colorScheme === 'dark' ? "#fff" : "#2d2828"
                    }}
                    className='h-10 flex-row items-center justify-center space-x-2 px-[10px]'
                >
                    <View>
                        {item.icon}
                    </View>
                    <Text
                        style={Global.text_bold}
                        className={`${selectedChip === item.title ? 'text-primary dark:text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14`}
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        )
    };

    return (
        <View style={{ width: '100%' }}>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
}

export default ReadingsChip;