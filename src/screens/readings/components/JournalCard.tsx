import React, { useContext } from 'react';
import { View, Text,TouchableOpacity } from 'react-native';
import Global from '../../../../globalStyle';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import { ClientContext } from '../../../context/ClientContext';
import { ReadingsContext } from '../../../context/ReadingsContext';
import { getJournalDate } from '../../../helpers/dateTimeFormat';
import { Image } from 'expo-image';

const JournalCard = ({ data }: any) => {

    const { currentReadingsChip, navigation, isLarge } = useContext(ClientContext);
    const { handleView } = useContext(LikeSaveContext);
    const { selectedChipData } = useContext(ReadingsContext);

    const handleReadingsCard = () => {
        handleView(data?._id, currentReadingsChip);
        navigation.navigate('ReadingDetail', { item: data, selectedChipData: selectedChipData });
    };

    return (
        <TouchableOpacity activeOpacity={0.8} className='flex-row w-full' onPress={handleReadingsCard} style={{ height: isLarge ? 210 : 160 }}>

            <View className={`h-full ${isLarge ? 'w-[20%]' : 'w-[32%]'}`}>
                <Image
                    source={data?.thumbnail_image_path ? { uri: data?.thumbnail_image_path } : require('../../../assets/images/placeholder-thumbnail.png')}
                    contentFit='cover'
                    style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: 6
                    }}
                />
            </View>

            <View className='justify-between flex-1 pl-2'>
                <Text style={Global.text_bold} className='text-lg leading-tight sm:text-xl text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>{data?.journal_title}</Text>
                <Text style={Global.text_regular} className='text-text15 sm:text-lg text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>Latest content : Volume {data?.volume},</Text>
                <Text style={Global.text_regular} className='text-text15 sm:text-lg text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>Issue {getJournalDate(data?.issue_date)}</Text>
                <Text style={Global.text_regular} className='text-text15 sm:text-lg text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>Content available from : {data?.contents_available_from ? data?.contents_available_from?.slice(0, 4) : 'N/A'}</Text>
                <Text style={Global.text_regular} className='text-text15 sm:text-lg text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>ISSN : {data?.issn_number}</Text>
                <Text style={Global.text_regular} className='text-text15 sm:text-lg text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail'>Online ISSN : {data?.online_issn_number}</Text>
            </View>

        </TouchableOpacity>
    );
};

export default JournalCard;