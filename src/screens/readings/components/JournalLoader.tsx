import { View, Dimensions } from 'react-native'
import React, { useContext } from 'react'
import { Skeleton } from '@rneui/themed'
import { LinearGradient } from 'expo-linear-gradient'
import { ClientContext } from '../../../context/ClientContext'

const JournalLoader = () => {

    const { isLarge } = useContext(ClientContext);

    return (
        <View className='w-full flex-row' style={{ height: isLarge ? 210 : 160 }}>

            <View className={`h-full ${isLarge ? 'w-[20%]' : 'w-[32%]'}`}>
                <Skeleton
                    LinearGradientComponent={LinearGradient}
                    animation="wave"
                    style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: 6
                    }}
                />
            </View>

            <View className='flex-1 justify-between py-1 pl-2'>
                <Skeleton LinearGradientComponent={LinearGradient} animation="wave" style={{ height: 15, width: '100%', borderRadius: 3 }} />
                <Skeleton LinearGradientComponent={LinearGradient} animation="wave" style={{ height: 12, width: '90%', borderRadius: 2 }} />
                <Skeleton LinearGradientComponent={LinearGradient} animation="wave" style={{ height: 12, width: '80%', borderRadius: 2 }} />
                <Skeleton LinearGradientComponent={LinearGradient} animation="wave" style={{ height: 12, width: '70%', borderRadius: 2 }} />
                <Skeleton LinearGradientComponent={LinearGradient} animation="wave" style={{ height: 12, width: '50%', borderRadius: 2 }} />
            </View>

        </View>
    )
}

export default JournalLoader;
