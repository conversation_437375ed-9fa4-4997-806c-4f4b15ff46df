import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList, Dimensions, LayoutAnimation } from 'react-native';
import Global from '../../../../globalStyle';
import { capitalizeMode } from '../../../helpers/getCapitalize';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

const TableOfContents = ({ items, handleHeadingPress }: any) => {

    const [isOpen, setIsOpen] = useState(false);
    const [activeIndex, setActiveIndex] = useState(null);

    const handleItemClick = (index: any) => {
        setActiveIndex(index);
        if (handleHeadingPress) {
            handleHeadingPress(index);
        }
    };

    useEffect(()=>{
        setActiveIndex(null);
        setIsOpen(false);
    },[items])

    const toggleDropdown = () => {
        LayoutAnimation.configureNext({
            duration: 300,
            create: {
                type: 'linear',
                property: 'opacity',
            },
            update: {
                type: 'linear',
            },
        });
        setIsOpen(!isOpen);
    };

    return (
        <View style={styles.container}>

            <TouchableOpacity activeOpacity={0.8} onPress={toggleDropdown} style={[styles.header, isOpen && styles.openHeader]}>
                <Text style={Global.text_bold} className='text-lg text-secondary'>Table of contents</Text>
                <Text style={[styles.toggleText, Global.text_bold]}>{isOpen ? '-' : '+'}</Text>
            </TouchableOpacity>

            {isOpen && (
                <View className='bg-cardLight dark:bg-secondary' style={styles.dropdown}>
                    <FlatList
                        data={items}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item, index }) => (
                            <TouchableOpacity activeOpacity={0.8} onPress={() => handleItemClick(index)}>
                                <View style={[styles.itemContainer, activeIndex === index && styles.activeItem]}>
                                    <Text style={[styles.itemText, activeIndex === index && styles.activeItemText, Global.text_medium]} className='text-secondary dark:text-white'>{capitalizeMode(item)}</Text>
                                </View>
                            </TouchableOpacity>
                        )}
                    />
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 6,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#FDD066',
        padding: 15,
        borderRadius: 6,
    },
    openHeader: {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
    },
    toggleText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    dropdown: {
        overflow: 'hidden',
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
    },
    itemContainer: {
        paddingVertical: 8,
        paddingLeft: leftMargin,
    },
    itemText: {
        fontSize: 16,
    },
    activeItem: {
        top: 3,
        borderLeftWidth: 4,
        borderColor: '#FDD066',
    },
    activeItemText: {
        color: '#FDD066',
    },
});

export default TableOfContents;