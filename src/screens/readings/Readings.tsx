import { Dimensions, RefreshControl, ScrollView, Text, View } from 'react-native';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { ReadingsContext } from '../../context/ReadingsContext';
import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import ReadingsChip from './components/ReadingsChip';
import Filters from '../../components/Filters';
import Global from '../../../globalStyle';
import JournalCard from './components/JournalCard';
import ReadingsCard from './components/ReadingsCard';
import Pagination from './components/Pagination';
import JournalLoader from './components/JournalLoader';
import ReadingLoader from './components/ReadingLoader';
import { ClientContext } from '../../context/ClientContext';

const Readings = () => {

    const { selectedChip, showPagination, selectedChipData, loading, onRefresh, refreshing } = useContext(ReadingsContext);
    const { isAndroid, isLarge, isMedium } = useContext(ClientContext);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Readings' index={1} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className='flex-1'
                ref={scrollViewRef}
                refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
                stickyHeaderIndices={[0]}
            >

                <View className='pt-3 pb-4 bg-cardLight dark:bg-secondary'>
                    <ReadingsChip handlePaginationChange={handlePaginationChange} />
                </View>

                <View className='bg-ececec'>
                    <Filters
                        chipFrom=''
                        dateFilter
                        readingSort
                        categoryFilter
                    />
                </View>

                <View className={`space-y-3 ${isMedium ? 'mb-28' : isAndroid ? 'mb-20' : 'mb-24'}`}>

                    <View className='w-full pt-4 pb-2 space-y-4 px-primary'>

                        <Text className='text-text20 text-secondary dark:text-white' style={Global.text_bold}>{selectedChip}</Text>

                        <View className='space-y-[3%]'>
                            {loading ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {[1, 2, 3, 4, 5, 6].map((index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                {selectedChip === 'Journals' ? <JournalLoader /> : <ReadingLoader />}
                                            </View>
                                        ))}
                                    </View>
                                ) : (
                                    [1, 2, 3,].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            {selectedChip === 'Journals' ? <JournalLoader /> : <ReadingLoader />}
                                        </View>
                                    ))
                                )
                            ) : selectedChipData?.length > 0 ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {selectedChipData?.map((item, index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                {selectedChip === 'Journals' ? <JournalCard data={item} /> : <ReadingsCard data={item} />}
                                            </View>
                                        ))}
                                    </View>
                                ) :
                                    selectedChipData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            {selectedChip === 'Journals' ? <JournalCard data={item} /> : <ReadingsCard data={item} />}
                                        </View>
                                    ))
                            ) : (
                                <View className='flex-row items-center justify-center w-full h-12'>
                                    <Text className='text-text17 text-secondary dark:text-white' style={Global.text_medium}>
                                        No {selectedChip} is available
                                    </Text>
                                </View>
                            )}
                        </View>

                    </View>

                    {showPagination && <View className='px-1 mb-3'>
                        <Pagination onPageChange={handlePaginationChange} />
                    </View>}

                </View>

            </ScrollView>

        </View>
    );
};

export default Readings;