import React, {
    useCallback,
    useContext,
    useEffect,
    useRef,
    useState,
} from 'react';
import { Dimensions, FlatList, Linking, Pressable, Text, TouchableOpacity, View, Platform } from 'react-native';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import TableOfContents from '../components/TableOfContents';
import { ClientContext } from '../../../context/ClientContext';
import { styles } from '../../style/styles';
import HTMLView from 'react-native-htmlview';
import Carousel from 'react-native-snap-carousel';
import Global from '../../../../globalStyle';
import TTSPlayer from '../../../components/TTSPlayer';
import { AntDesign, Feather, FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { StyleSheet } from 'react-native';
import { formatDate, getJournalDate } from '../../../helpers/dateTimeFormat';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import ReadingsCard from '../components/ReadingsCard';
import { handleShare } from '../../../lib/share';
import { Image } from 'expo-image';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import TTSPlayerAndroid from '../../../components/TTSPlayerAndroid';

const carouselWidth = Dimensions.get('window').width;
const Width = carouselWidth - carouselWidth * 0.04;

const ReadingDetail = ({ route }: any) => {

    const { item, selectedChipData } = route.params;
    const { colorScheme, currentReadingsChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, likeReadings, isCardLiked } = useContext(LikeSaveContext);



    const [currentIndex, setCurrentIndex] = useState(-1);
    const [contentSections, setContentSections] = useState<string[]>([]);
    const [headingPositions, setHeadingPositions] = useState<{ [key: string]: number }>({});

    const flatListRef = useRef<FlatList>(null);
    const currentItem = selectedChipData[currentIndex];
    const tableContent: string[] = currentItem?.table_of_content.map((title: string) => title.trim());

    const getCurrentItem = useCallback(() => {
        const index = selectedChipData.findIndex(
            (data: any) => data?._id === item?._id
        );
        setCurrentIndex(index);
    }, [selectedChipData, item?._id]);

    useEffect(() => {
        getCurrentItem();
    }, [getCurrentItem]);

    const handlePrevious = () => {
        if (currentIndex > 0) {
            setCurrentIndex(currentIndex - 1);
            scrollToTop();
        }
    };

    const handleNext = () => {
        if (currentIndex < selectedChipData.length - 1) {
            setCurrentIndex(currentIndex + 1);
            scrollToTop();
        }
    };

    const handlePress = (item: any) => {
        const newIndex = selectedChipData.findIndex((data: any) => data?._id === item?._id);
        setCurrentIndex(newIndex);

        scrollToTop();
    };

    const content =
        currentItem?.blog_content ||
        currentItem?.article_content ||
        currentItem?.news_content ||
        currentItem?.journal_content;

    const formatContent = () => {
        const sections = content?.match(/<(h[1-6]|p|strong|i|img|ul|ol)[^>]*>.*?<\/\1>|<img[^>]*\/?>/gs) || [];
        setContentSections(sections);
    }

    useEffect(() => {
        formatContent();
    }, [content]);

    const [remainingData, setRemainingData] = useState(selectedChipData);

    useEffect(() => {
        const getRemainingData = () => {
            const filteredData = selectedChipData.filter((data: any) => data?._id !== currentItem?._id);
            setRemainingData(filteredData);
        };

        if (currentItem) {
            getRemainingData();
        }
    }, [currentItem, selectedChipData]);


    const renderItem = ({ item, index }: { item: string; index: number }) => {

        const isHeading = ['<h1>', '<h2>', '<h3>', '<h4>', '<h5>', '<h6>', '<p><strong>'].some(tag => item.indexOf(tag) >= 0);
        const sanitizedHeader = isHeading
            ? item
                .replace(/&nbsp;/g, " ")
                .replace(/&amp;/g, "&")
                .replace(/<\/?h[1-6]>/g, '')
                .replace(/<\/?strong>/g, '')
                .replace(/<\/?p>/g, '')
                .trim()
            : '';

        const isValidHeader = tableContent.find((header: string) => (
            header.split(/\s+/).join('&nbsp;') === sanitizedHeader.split(/\s+/).join('&nbsp;')
        ))

        if (sanitizedHeader && isValidHeader) {
            const head = sanitizedHeader.split(/\s+/).join('&nbsp;');
            return (
                <View
                    key={index}
                    className='my-2 px-primary'
                    onLayout={() => {
                        setHeadingPositions(prev => ({
                            ...prev,
                            [head]: index,
                        }));
                    }}
                >
                    <Text
                        className='text-lg tracking-wider text-secondary dark:text-white'
                        style={Global.text_medium}
                    >
                        {sanitizedHeader}
                    </Text>
                </View>
            );
        }

        return (
            <View className='px-primary' key={index}>
                <HTMLView
                    value={item}
                    key={index}
                    stylesheet={styles(colorScheme)}
                    renderNode={(node) => {
                        if (node?.name === 'img' && node?.attribs?.src) {
                            return (
                                <Image
                                    source={{ uri: node?.attribs?.src }}
                                    key={index}
                                    style={{
                                        width: '100%',
                                        height: widthPercentageToDP(isLarge ? 35 : 50),
                                        borderRadius: 6,
                                        marginVertical: 10,
                                    }}
                                    contentFit='cover'
                                />
                            );
                        }
                    }}
                    onLinkPress={(url) => {
                        Linking.openURL(url).catch(() => console.log('Not a valid url'));
                    }}
                />
            </View>
        );
    };

    const handleHeadingPress = (index: number) => {
        let headingKey = tableContent[index];
        headingKey = headingKey.split(/\s+/).join('&nbsp;').trim();

        if (headingKey) {
            const position = headingPositions[headingKey];
            if (position !== undefined && flatListRef.current) {
                flatListRef.current.scrollToIndex({ index: position, animated: true });
            }
        }
    };

    const scrollToTop = () => {
        if (flatListRef.current) {
            flatListRef.current.scrollToOffset({ offset: 0, animated: true });
        }
    };

    return (
        <View className="flex-1 bg-white dark:bg-dark">

            <CustomStatusBar />

            <Header search name={currentReadingsChip} index={-1} />

            <FlatList
                ref={flatListRef}
                data={contentSections}
                keyExtractor={(item, index) => `section-${index}`}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={

                    <View className='pt-3 space-y-5 px-primary'>

                        <View className='flex-row flex-wrap items-center justify-between'>
                            <View className='flex-row items-center space-x-2'>
                                <View>
                                    {currentItem?.author_image_path ? (
                                        <Image source={{ uri: currentItem?.author_image_path }} className='rounded-full w-7 h-7' contentFit='cover' />
                                    ) :
                                        <View className='w-7 h-7 rounded-full items-center justify-center bg-[#a8a8a850]'>
                                            <Feather name="user" size={17} color="black" />
                                        </View>
                                    }
                                </View>
                                <Text className='text-base text-secondary dark:text-white' style={Global.text_bold}>{currentItem?.author_name}</Text>
                            </View>
                            <Text className='text-base text-secondary dark:text-white' style={Global.text_regular}>{formatDate(item?.createdAt)}</Text>
                        </View>

                        <View>
                            <Text className='text-lg dark:text-white' style={Global.text_bold}>{currentItem?.article_title || currentItem?.news_title || currentItem?.blog_title || currentItem?.journal_title}</Text>
                        </View>

                        <View className='flex-row items-center justify-end space-x-3'>
                            <Pressable onPress={() => handleShare(currentReadingsChip, currentItem?._id, currentItem?.thumbnail_image_path)} className='h-10 w-10 pr-[2px] items-center justify-center rounded-full border-[0.7px] border-secondary dark:border-white'>
                                <Feather name="share-2" size={22} color={colorScheme === 'dark' ? 'white' : 'gray'} />
                            </Pressable>
                            <TouchableOpacity activeOpacity={0.8} onPress={() => likeReadings(currentReadingsChip, currentItem?._id)} className='h-10 w-10 pb-[2px] pl-[1px] items-center justify-center rounded-full border-[0.7px] border-secondary dark:border-white'>
                                <FontAwesome
                                    name={isCardLiked(currentItem?._id) ? 'thumbs-up' : 'thumbs-o-up'}
                                    size={22}
                                    color={isCardLiked(currentItem?._id) ? '#fdd066' : colorScheme === 'dark' ? 'white' : 'gray'}
                                />
                            </TouchableOpacity>
                        </View>

                        <View className='z-50'>
                            <TableOfContents items={currentItem?.table_of_content || []} handleHeadingPress={handleHeadingPress} />
                        </View>

                        <View>
                            <Image style={{ width: '100%', height: 200, borderRadius: 6 }}
                                source={{ uri: currentItem?.thumbnail_image_path }}
                            />
                        </View>

                        <View className='w-full'>
                            {Platform.OS === 'ios' ? <TTSPlayer text={content} /> : <TTSPlayerAndroid text={content} />}
                        </View>

                        <View className='items-center mb-3'>
                            <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(currentItem?._id, currentReadingsChip)}>
                                <Ionicons name={isCardSaved(currentItem?._id) ? 'heart' : 'heart-outline'}
                                    size={25}
                                    color={isCardSaved(currentItem?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                                />
                                <Text className='text-text16 dark:text-white' style={Global.text_medium}>{isCardSaved(currentItem?._id) ? 'Saved' : 'Save for later'}</Text>
                            </TouchableOpacity>
                        </View>

                        {item?.issn_number && <View className='mb-5 space-y-2'>
                            <Text style={Global.text_regular} className='text-base text-secondary dark:text-white'>Latest content : Volume {item?.volume},</Text>
                            <Text style={Global.text_regular} className='text-base text-secondary dark:text-white'>Issue {getJournalDate(item?.issue_date)}</Text>
                            <Text style={Global.text_regular} className='text-base text-secondary dark:text-white'>Content available from : {item?.contents_available_from?.slice(0, 4)}</Text>
                            <Text style={Global.text_regular} className='text-base text-secondary dark:text-white'>ISSN : {item?.issn_number} . Online ISSN : {item?.online_issn_number}</Text>
                        </View>}

                    </View>
                }
                ListFooterComponent={
                    <View className='mb-8'>

                        <View className='my-3 px-primary'>

                            <View className='h-14 bg-primary rounded-lg py-[5px]'>

                                <View className='flex-row items-center h-full px-2 space-x-2 justify-evenly'>

                                    <Pressable
                                        className='h-full rounded-lg flex-row items-center justify-center w-[49%] space-x-2'
                                        onPress={handlePrevious}
                                        disabled={currentIndex === 0}
                                        style={currentIndex === 0 ? styless.disabledButton : styless.button}
                                    >
                                        <View className=''>
                                            <AntDesign name='arrowleft' size={18} color={currentIndex === 0 ? '#aaa' : '#000'} />
                                        </View>
                                        <Text
                                            style={[Global.text_bold, { color: currentIndex === 0 ? '#aaa' : '#000' }]}
                                            className='text-sm uppercase text-secondary'
                                        >
                                            PREVIOUS {currentReadingsChip}
                                        </Text>
                                    </Pressable>

                                    <Pressable
                                        className='h-full rounded-lg flex-row items-center justify-center w-[49%] space-x-2'
                                        onPress={handleNext}
                                        disabled={currentIndex === selectedChipData.length - 1}
                                        style={currentIndex === selectedChipData.length - 1 ? styless.disabledButton : styless.button}
                                    >
                                        <Text
                                            style={[Global.text_bold, { color: currentIndex === selectedChipData.length - 1 ? '#aaa' : '#000' }]}
                                            className='text-sm uppercase text-secondary'
                                        >
                                            NEXT {currentReadingsChip}
                                        </Text>
                                        <View className=''>
                                            <AntDesign name='arrowright' size={18} color={currentIndex === selectedChipData.length - 1 ? '#aaa' : '#000'} />
                                        </View>
                                    </Pressable>

                                </View>

                            </View>

                        </View>

                        {currentReadingsChip !== 'Journals' && remainingData.length > 0 && (
                            <View className='mb-4'>
                                <Text style={Global.text_bold} className='my-3 text-xl text-secondary dark:text-white px-primary'>Latest {currentReadingsChip}</Text>
                                <Carousel
                                    data={remainingData}
                                    renderItem={({ item }) => {
                                        return (
                                            <View className='px-1'>
                                                <ReadingsCard data={item} handleNewReadingDetail={handlePress} />
                                            </View>
                                        )
                                    }}
                                    sliderWidth={carouselWidth}
                                    itemWidth={Width}
                                    inactiveSlideScale={1}
                                    inactiveSlideOpacity={1}
                                />
                            </View>
                        )}

                    </View>
                }
            />

            <TouchableOpacity activeOpacity={0.9} onPress={scrollToTop} className='absolute items-center justify-center w-12 h-12 rounded-full bg-primary bottom-6 right-6'>
                <MaterialIcons name='keyboard-arrow-up' size={40} color='#2d2828' />
            </TouchableOpacity>

        </View>
    );
};

const styless = StyleSheet.create({
    contentSection: {
        marginVertical: 16,
        padding: 16,
        backgroundColor: '#F0F0F0',
        borderRadius: 8,
    },
    sectionContent: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        backgroundColor: 'white',
    },
    disabledButton: {
        backgroundColor: '#f0f0f0',
    }
});

export default ReadingDetail;
