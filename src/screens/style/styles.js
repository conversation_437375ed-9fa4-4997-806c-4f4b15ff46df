import { StyleSheet } from 'react-native';

export const styles = (colorScheme) => StyleSheet.create({
    h1: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 17,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    h2: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 17,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    h3: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 16,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    h4: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 16,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    h5: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 16,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    h6: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontSize: 16,
        fontFamily: 'DMSans-Bold',
        letterSpacing: 0.5
    },
    p: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16,
    },
    strong: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Medium',
        letterSpacing: 0.5,
        fontSize: 16,
    },
    div: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 14,
    },
    span: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16,
    },
    li: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16
    },
    ul: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        marginBottom:10
    },
    ol: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        marginBottom:10
    },
    div: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 15
    },
});