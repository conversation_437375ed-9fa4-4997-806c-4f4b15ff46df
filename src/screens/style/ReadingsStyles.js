import { StyleSheet } from 'react-native';

export const ReadingsStyles = (colorScheme) => StyleSheet.create({
    p: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16,
        marginBottom: 10
    },
    strong: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Medium',
        letterSpacing: 0.5,
        fontSize: 16,
    },
    li: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16
    },
    ul: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16
    },
    ol: {
        color: colorScheme === 'dark' ? '#fff' : '#000',
        fontFamily: 'DMSans-Regular',
        letterSpacing: 0.5,
        fontSize: 16
    },
});
