import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Global from '../../../../globalStyle';
import { Image } from 'expo-image';

const Card = ({ title, image, onPress }: any) => (
	<TouchableOpacity activeOpacity={0.8} onPress={onPress} style={styles.cardContainer}>
		<View style={styles.imageContainer}>
			<Image source={image} style={styles.cardImage} contentFit='fill' />
		</View>
		<Text style={Global.text_regular} className='mt-2 text-white text-text15'>{title}</Text>
	</TouchableOpacity>
);

const styles = StyleSheet.create({
	cardContainer: {
		width: '48%',
		marginVertical: 8,
	},
	imageContainer: {
		width: '100%',
		aspectRatio: 16 / 9,
		borderRadius: 6,
		overflow: 'hidden',
	},
	cardImage: {
		width: '100%',
		height: '100%',
	},
});

export default Card;
