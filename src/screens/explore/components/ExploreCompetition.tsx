import { View, Text } from 'react-native';
import React, { useContext } from 'react';
import Global from '../../../../globalStyle';
import Card from './Card';
import { ClientContext } from '../../../context/ClientContext';
import { CompetitionContext } from '../../../context/CompetitionContext';
import { Images } from '../../../helpers/images';

const CompCard = [
	{
		id: 1,
		title: 'Quiz',
		image: Images.quiz,
	},
	{
		id: 2,
		title: 'Moot Court',
		image: Images.mootcourt,
	},
	{
		id: 3,
		title: 'Essay Writing',
		image: Images.essaywriting,
	},
	{
		id: 4,
		title: 'Article Writing',
		image: Images.articlewriting,
	},
	{
		id: 5,
		title: 'Debate',
		image: Images.debate,
	},
];


const ExploreCompetition = () => {

	const { navigation, setCurrentCompChip } = useContext(ClientContext);
	const { setSelectedChip } = useContext(CompetitionContext);

	const handleCompetition = (index: number) => {
		setSelectedChip(CompCard[index]?.title);
		setCurrentCompChip(CompCard[index]?.title);
		navigation.navigate('Competition', { title: CompCard[index]?.title });
	};

	return (
		<View className='w-full space-y-2 px-primary'>

			<Text style={[Global.lora_medium,]} className='text-xl text-white'>
				Competitions
			</Text>

			<View className='flex-row flex-wrap justify-between'>
				{CompCard.map((comp, index) => (
					<Card
						key={comp?.id}
						title={comp.title}
						image={comp.image}
						onPress={() => handleCompetition(index)}
					/>
				))}
			</View>

		</View>
	);
};


export default ExploreCompetition;
