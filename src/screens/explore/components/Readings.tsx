import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle';
import Card from './Card';
import { ClientContext } from '../../../context/ClientContext';
import { Images } from '../../../helpers/images';

const ReadingCard = [
	{
		id: 1,
		title: 'Blogs',
		image: Images.blogs,
	},
	{
		id: 2,
		title: 'News',
		image: Images.news,
	},
	{
		id: 3,
		title: 'Articles',
		image: Images.articles,
	},
	{
		id: 4,
		title: 'Journals',
		image: Images.journals,
	},
];

const Readings = () => {

	const { navigation, setCurrentReadingsChip } = useContext(ClientContext);

	const navigateToEvents = (id: number) => {
		setCurrentReadingsChip(ReadingCard[id - 1].title);
		navigation.navigate('Readings');
	};

	return (
		<View className='w-full space-y-2 px-primary'>

			<Text style={Global.lora_medium} className='text-xl text-white'>
				Readings
			</Text>

			<View className='flex-row flex-wrap justify-between '>
				{ReadingCard.map((reading) => (
					<Card key={reading.id} title={reading.title} image={reading.image} onPress={() => navigateToEvents(reading.id)} />
				))}
			</View>

		</View>
	);
};

export default Readings;
