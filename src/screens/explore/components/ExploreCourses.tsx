import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle';
import Card from './Card';
import { ClientContext } from '../../../context/ClientContext';
import { CourseContext } from '../../../context/CourseContext';
import { Images } from '../../../helpers/images';

const CourseCard = [
	{
		id: 1,
		title: 'Beginner',
		image: Images.beginner,
	},
	{
		id: 2,
		title: 'Intermediate',
		image: Images.intermediate,
	},
	{
		id: 3,
		title: 'Advanced',
		image: Images.advanced,
	},

];

const ExploreCourses = () => {

	const { navigation, setCurrentCourseChip } = useContext(ClientContext);
	const { setSelectedChip } = useContext(CourseContext);

	const handleCourses = (id: number) => {
		setSelectedChip(CourseCard[id - 1]?.title);
		setCurrentCourseChip(CourseCard[id - 1]?.title);
		navigation.navigate('Courses');
	}

	return (
		<View className='w-full space-y-2 px-primary'>

			<Text style={[Global.lora_medium,]} className='text-xl text-white'>
				Courses
			</Text>

			<View className='flex-row flex-wrap justify-between '>
				{CourseCard.map((course) => (
					<Card key={course?.id} title={course.title} image={course.image} onPress={() => handleCourses(course.id)} />
				))}
			</View>

		</View>
	)
}

export default ExploreCourses