import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle';
import Card from './Card';
import { ClientContext } from '../../../context/ClientContext';
import { EventContext } from '../../../context/EventContext';
import { Images } from '../../../helpers/images';

const EventCard = [
	{
		title: 'College Events',
		image: Images.collegeevent,
		chipId: 1
	},
	{
		title: 'Seminars',
		image: Images.seminar,
		chipId: 2
	},
	{
		title: 'Workshop',
		image: Images.workshop,
		chipId: 3
	},

];

const ExploreEvent = () => {

	const { navigation, setCurrentEventChip } = useContext(ClientContext);
	const { setSelectedChip } = useContext(EventContext);

	const handleChipSelection = (chipId: number) => {
		setSelectedChip(EventCard[chipId - 1]?.title);
		setCurrentEventChip(EventCard[chipId - 1]?.title);
		navigation.navigate('Events');
	}

	return (
		<View className='w-full space-y-2 px-primary'>

			<Text style={[Global.lora_medium]} className='text-xl text-white'>
				Events
			</Text>

			<View className='flex-row flex-wrap justify-between '>
				{EventCard.map((event) => (
					<Card key={event?.chipId} title={event.title} image={event.image} onPress={() => handleChipSelection(event.chipId)} />
				))}
			</View>

		</View>
	)
}

export default ExploreEvent