import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle';
import Card from './Card';
import { ClientContext } from '../../../context/ClientContext';
import { CareerContext } from '../../../context/CareerContext';
import { Images } from '../../../helpers/images';

const CareerCard = [
	{
		id: 1,
		title: 'Job Listing',
		image: Images.job,
	},
	{
		id: 2,
		title: 'Internship',
		image: Images.internship,
	},
];

const ExploreCarrer = () => {

	const { navigation, setCurrentCareerChip } = useContext(ClientContext);
	const { setSelectedChip } = useContext(CareerContext);

	const handleCareer = (id: number) => {
		setSelectedChip(CareerCard[id - 1]?.title);
		setCurrentCareerChip(CareerCard[id - 1]?.title);
		navigation.navigate('Career');
	}

	return (
		<View className='w-full space-y-2 px-primary'>

			<Text style={[Global.lora_medium]} className='text-xl text-white'>
				Careers
			</Text>

			<View className='flex-row flex-wrap justify-between'>
				{CareerCard.map((carrer) => (
					<Card key={carrer.id} title={carrer.title} image={carrer.image} onPress={() => handleCareer(carrer.id)} />
				))}
			</View>

		</View>
	)
}

export default ExploreCarrer