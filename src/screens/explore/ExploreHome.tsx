import { View, Text, TouchableOpacity, ScrollView, Pressable } from 'react-native'
import React, { useContext } from 'react';
import AntDesign from '@expo/vector-icons/AntDesign'
import { ClientContext } from '../../context/ClientContext';
import ExploreCourses from './components/ExploreCourses';
import ExploreCarrer from './components/ExploreCarrer';
import Readings from './components/Readings';
import ExploreEvent from './components/ExploreEvent';
import Global from '../../../globalStyle';
import CustomStatusBar from '../../components/CustomStatusBar';
import ExploreCompetition from './components/ExploreCompetition';


const ExploreHome = () => {

    const { navigation, colorScheme, isAndroid } = useContext(ClientContext);

    return (
        <View className='flex-1 bg-secondary'>

            <CustomStatusBar />

            <View className='w-full flex-row items-center justify-between px-primary pt-3 pb-4'>

                <Pressable onPress={() => navigation.goBack()} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-white'>
                    <AntDesign name='arrowleft' size={22} color='white' />
                </Pressable>

                <Text style={Global.text_medium} className='text-white text-xl'>Explore</Text>

                <View className='h-[30px] w-[30px]'>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        className='hidden h-full w-full rounded-full justify-center items-center border-[1px] border-white'
                    >
                        <AntDesign name='arrowleft' size={22} color='white' />
                    </TouchableOpacity>
                </View>

            </View>

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className={`space-y-4 ${isAndroid ? 'mb-20' : 'mb-28'}`}>

                    <View className='space-y-4'>
                        <ExploreCompetition />
                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                    </View>

                    <View className='space-y-4'>
                        <ExploreEvent />
                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                    </View>

                    <View className='space-y-4'>
                        <Readings />
                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                    </View>

                    <View className='space-y-4'>
                        <ExploreCarrer />
                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                    </View>

                    <View className='space-y-4'>
                        <ExploreCourses />
                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default ExploreHome;