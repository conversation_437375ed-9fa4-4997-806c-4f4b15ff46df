import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import Badge from '../../../components/Badge'
import { getWishListMenu, searchPath } from '../../../helpers/getWishlistMenu'
import { ClientContext } from '../../../context/ClientContext'
import { LikeSaveContext } from '../../../context/LikeSaveContext'

const SearchCard = ({ item }: { item: any }) => {

    const { navigation, setCurrentCareerChip, registeredCourses, setCurrentCompChip, setCurrentEventChip, setCurrentCourseChip, setCurrentReadingsChip } = useContext(ClientContext);
    const { registeredItems } = useContext(LikeSaveContext);

    const isPaymentCompleted = (id: string) => {
        const isRegisteredNow = registeredItems?.has(id);
        const isRegisteredPreviously = registeredCourses?.includes(id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    const handleSearchNavigation = () => {

        const { url, path } = searchPath(item.item_type);
        const dynamicKey = `${url}`;

        if (path === 'CompetitionDetails') {
            setCurrentCompChip(getWishListMenu(item.item_type));
            navigation.navigate(path, { [dynamicKey]: item });
        }
        if (path === 'EventDetails') {
            setCurrentEventChip(getWishListMenu(item.item_type));
            navigation.navigate(path, { [dynamicKey]: item });
        }
        if (path === 'CourseDetails') {
            const course = item.course_level === 'Expert' ? 'Advanced' : item.course_level;
            setCurrentCourseChip(course);
            isPaymentCompleted(item?._id)
                ? navigation.navigate('BookedCourse', { currentCourse: item })
                : navigation.navigate(path, { [dynamicKey]: item });
        }
        if (path === 'ReadingDetail') {
            console.log(item)
            setCurrentReadingsChip(getWishListMenu(item.item_type));
            navigation.navigate(path, { [dynamicKey]: item, selectedChipData: [item] });
        }
        if (path === 'CareerDetails') {
            setCurrentCareerChip(getWishListMenu(item.item_type));
            navigation.navigate(path, { [dynamicKey]: item });
        }
        if (path === 'LawyerDetails') {
            navigation.navigate(path, { [dynamicKey]: item });
        }
    }

    return (
        <View className='mt-3 space-y-3'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleSearchNavigation}>

                <View>
                    <Badge name={item.item_type === 'course' ? 'Course' : getWishListMenu(item.item_type)} />
                </View>

                <Text
                    numberOfLines={2}
                    ellipsizeMode='tail'
                    className='tracking-wide text-white text-text17'
                    style={Global.text_medium}
                >
                    {item.title || item?.article_title || item?.news_title || item?.blog_title || item?.journal_title || item?.job_position || item?.internship_position || item?.freelancer_name}
                </Text>

                {(item?.posted_by || item?.company_name || item?.author_name || item?.profession_name) &&
                    <Text
                        numberOfLines={1}
                        ellipsizeMode='tail'
                        className='tracking-wide text-white text-text14'
                        style={Global.text_regular}
                    >
                        {item?.item_type !== 'freelancer' && "By"} {item?.posted_by || item?.company_name || item?.author_name || item?.profession_name}
                    </Text>
                }

            </TouchableOpacity>

            <View className='h-[0.8px] w-full bg-greycolor' />

        </View>
    )
}

export default SearchCard