import { View, Text, FlatList, Dimensions, Pressable, KeyboardAvoidingView } from 'react-native'
import React, { useContext } from 'react'
import { AntDesign } from '@expo/vector-icons';
import { ActivityIndicator, Searchbar } from 'react-native-paper';
import { ClientContext } from '../../context/ClientContext';
import CustomStatusBar from '../../components/CustomStatusBar';
import Global from '../../../globalStyle';
import { SearchContext } from '../../context/SearchContext';
import SearchCard from './components/SearchCard';
import { Image } from 'expo-image';

const Search = () => {

    const { navigation, isAndroid } = useContext(ClientContext);
    const { setSearchQuery, loading, searchQuery, searchedData } = useContext(SearchContext);

    const renderItem = ({ item }: { item: any }) => (
        <SearchCard item={item} />
    )

    return (
        <View className='flex-1 bg-secondary'>

            <CustomStatusBar />

            <View className='h-full space-y-5 px-primary'>

                <Pressable onPress={() => navigation.goBack()} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-white absolute m-5 top-1 z-20'>
                    <AntDesign name='arrowleft' size={22} color='white' />
                </Pressable>

                <View className='top-1'>
                    <Text style={Global.text_medium} className='self-center text-xl text-white'>Search</Text>
                </View>

                <View className=''>
                    <Searchbar
                        iconColor='#2d2828'
                        cursorColor='#2d2828'
                        placeholder='Search'
                        onChangeText={setSearchQuery}
                        value={searchQuery}
                        style={{
                            height: 50,
                            width: '100%',
                            borderRadius: 8,
                            backgroundColor: '#ececec'
                        }}
                        inputStyle={{
                            minHeight: 0,
                            fontFamily: 'DMSans-Medium',
                            fontSize: 15,
                            color: '#2d2828'
                        }}
                        autoFocus
                    />
                </View>

                <KeyboardAvoidingView
                    behavior={!isAndroid ? 'height' : 'padding'}
                    style={{ flex: 1 }}
                >
                    <View>
                        {loading ?
                            <View className='items-center space-y-2'>
                                <ActivityIndicator size={20} color='#fff' />
                                <Text style={Global.text_medium} className='text-white text-text15'>Searching for "{searchQuery}"</Text>
                            </View>
                            :
                            searchedData.length > 0 ?
                                <View>
                                    <FlatList
                                        data={searchedData ? searchedData : []}
                                        renderItem={renderItem}
                                        showsVerticalScrollIndicator={false}
                                        contentContainerStyle={{
                                            paddingBottom: '25%'
                                        }}
                                        keyboardShouldPersistTaps="handled"
                                    />
                                </View>
                                :
                                <View className='absolute left-0 right-0 items-center justify-between pt-2' style={{ height: Dimensions.get('window').height / 1.8 }}>
                                    <View>{searchQuery && <Text style={Global.text_medium} className='text-white text-text15'>No result found for "{searchQuery}"</Text>}</View>
                                    <Image
                                        source={require('../../assets/images/search-image.png')}
                                        contentFit="contain"
                                        style={{
                                            width: 250,
                                            height: 250,
                                        }}
                                    />
                                </View>
                        }

                    </View>

                </KeyboardAvoidingView>

            </View>

        </View>
    )
}

export default Search;