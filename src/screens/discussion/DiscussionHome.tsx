import React, { useContext, useState, useEffect, useCallback, useRef } from 'react';
import {
	View, FlatList, TouchableOpacity, Text, StyleSheet,
	TextInput, Keyboard, KeyboardAvoidingView, Platform,
} from 'react-native';
import Global from '../../../globalStyle';
import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import DiscussionCard from './components/DiscussionCard';
import { AntDesign, Feather } from '@expo/vector-icons';
import DiscussionModal from './components/NewDiscussionModal';
import DiscussionChip from './components/DiscussionChip';
import { ClientContext } from '../../context/ClientContext';
import { useSocket } from '../../context/SocketContext';
import { Dimensions } from 'react-native';
import { calculateTimeDifference } from '../../helpers/calculateTime';
import { Discussion } from '../../utils/types';
import Pagination from './components/Pagination';
import Loader from './components/Loader';

const { width, height } = Dimensions.get('window');
const small = width <= 375;

const tabBarHeight =
	Platform.OS === 'ios' && height <= 667
		? 70
		: Platform.OS === 'android'
			? 90
			: 100;

const DiscussionHome = () => {

	const { socket, discussions, pinDiscussion, unpinDiscussion, setSelectedChip,
		currentPage, setCurrentPage, totalPages, showPagination,
		searchQuery, setSearchQuery, loading, fetchDiscussions } = useSocket();

	const [modalVisible, setModalVisible] = useState(false);
	const [keyboardVisible, setKeyboardVisible] = useState(false);
	const [newDiscussionTitle, setNewDiscussionTitle] = useState('');

	const { navigation, clientId, userData, isAndroid } = useContext(ClientContext);
	const userName = `${userData?.first_name}`;

	const hasFetchedDiscussions = useRef(false);

	const flatListRef = useRef<FlatList>(null);
	const handlePageChange = () => {
		if (flatListRef.current) {
			flatListRef.current.scrollToIndex({ animated: true, index: 0 })
		}
	}

	useEffect(() => {
		const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
			setKeyboardVisible(true);
		});
		const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
			setKeyboardVisible(false);
		});

		return () => {
			keyboardDidShowListener.remove();
			keyboardDidHideListener.remove();
		};
	}, []);

	const handleNewDiscussion = () => {
		setModalVisible(true);
	};

	const handleSend = () => {
		if (newDiscussionTitle.trim()) {
			const discussionData = {
				discussion_title: newDiscussionTitle,
				creatorId: clientId,
				creatorType: 'user',
			};
			socket.emit('createDiscussion', discussionData);
			socket.emit('listDiscussion');
			setModalVisible(false);
			setNewDiscussionTitle('');
			setTimeout(() => fetchDiscussions(), 100);
		}
	};

	const handleJoinDiscussion = useCallback(
		(discussion: Discussion) => {
			socket.emit('getMessageSenders', discussion._id);

			const joinData = {
				discussionId: discussion._id,
				userId: clientId,
				userType: 'user',
			};
			socket.emit('joinDiscussion', joinData);

			navigation.navigate('DiscussionDetail', {
				discussion,
			});
		},
		[socket, clientId, navigation]
	);

	useEffect(() => {
		if (!hasFetchedDiscussions.current) {
			fetchDiscussions();
			hasFetchedDiscussions.current = true;
		}
	}, [fetchDiscussions]);

	const renderItem = useCallback(({ item, index }: { item: Discussion, index: number }) => {

		const isCreatedByAdmin = item.createdby_admin !== null;

		const name = isCreatedByAdmin
			? `${item.createdby_admin?.name}`
			: `${item.createdby_user?.first_name || userName}`;

		const time = calculateTimeDifference(item?.createdAt);
		const content = item.discussion_title;
		const replyCount = item.total_message_count || 0;
		const isPinned = item.pinned;

		const avatarUrls = isCreatedByAdmin
			? item.replied_admins.map(() => 'https://lawcube.org/assets/lex-favicon-vs1suN-s.png')
			: item.replied_users.map(participant => {
				const profileImage = participant?.user_profile;
				return profileImage
					? profileImage
					: 'https://lawcube.org/assets/empty_profile-pBWKwnIj.webp';
			});

		const profileImage = isCreatedByAdmin
			? 'https://lawcube.org/assets/lex-favicon-vs1suN-s.png'
			: (item.createdby_user?.user_profile || 'https://lawcube.org/assets/empty_profile-pBWKwnIj.webp');

		return (
			loading ? (
				<View>
					<Loader />
				</View>
			) : (
				<TouchableOpacity
					activeOpacity={0.8}
					onPress={() => handleJoinDiscussion(item)}
					className={`${discussions.length === index + 1 ? Keyboard.isVisible() ? ' mb-14' : 'mb-60' : 'mb-3'}`}
				>
					<DiscussionCard
						name={name}
						time={time}
						content={content}
						profileImage={profileImage}
						avatarUrls={avatarUrls}
						replyCount={replyCount}
						onPinToggle={() => isPinned ? unpinDiscussion(item._id) : pinDiscussion(item._id)}
						isPinned={isPinned}
					/>
				</TouchableOpacity>
			)
		);
	}, [discussions, handleJoinDiscussion, pinDiscussion, unpinDiscussion, loading, userName]);

	return (
		<KeyboardAvoidingView
			style={{ flex: 1 }}
			behavior={!isAndroid ? 'padding' : 'height'}
		>
			<View className='flex-1 space-y-4 bg-white dark:bg-dark '>

				<CustomStatusBar />

				<Header search name='Discussion' index={3} />

				<View className='px-primary'>

					<View style={styles.searchContainer} className='h-12 w-full border-[0.8px] border-greycolor'>
						<View className='items-center justify-center w-10 h-full'>
							<Feather name="search" size={24} color="#c0c0c0" style={styles.searchIcon} />
						</View>
						<TextInput
							className='flex-1 text-secondary dark:text-white text-text16'
							value={searchQuery}
							onChangeText={setSearchQuery}
							placeholder='Search discussion...'
							placeholderTextColor='#aaa'
							style={{
								fontFamily: 'DMSans-Medium'
							}}
						/>
					</View>

				</View>

				<View>
					<DiscussionChip onChipPress={setSelectedChip} />
				</View>

				<View className='pb-5 px-primary'>

					<FlatList
						data={discussions}
						renderItem={renderItem}
						keyExtractor={(item, index) => index.toString()}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps="handled"
						ref={flatListRef}
						ListEmptyComponent={() => (
							loading ? (
								[...Array(10)].map((_, index) => (
									<Loader key={index} />
								))
							) : (
								<View className='w-full mt-5 mb-10'>
									<Text style={Global.text_medium} className='tracking-wide text-center text-text16 text-secondary dark:text-white'>No discussions is available</Text>
								</View>
							)
						)}
						ListFooterComponent={
							<View className={`${Platform.OS === 'ios' ? 'mb-40' : 'mb-24'}`}>
								{showPagination && <Pagination
									currentPage={currentPage}
									totalPages={totalPages}
									handlePageChange={handlePageChange}
									setCurrentPage={setCurrentPage}
								/>}
							</View>
						}
					/>

				</View>

				{!loading && !keyboardVisible && (
					<TouchableOpacity
						style={styles.floatingButton}
						onPress={handleNewDiscussion}
						activeOpacity={0.8}
					>
						<View className='flex-row items-center'>
							<AntDesign name="pluscircle" size={24} color="black" />
							<Text style={Global.text_bold}> Start New Discussion</Text>
						</View>
					</TouchableOpacity>
				)}

				<DiscussionModal
					modalVisible={modalVisible}
					setModalVisible={setModalVisible}
					inputValue={newDiscussionTitle}
					setInputValue={setNewDiscussionTitle}
					onSend={handleSend}
				/>

			</View>

		</KeyboardAvoidingView>
	);
};

const styles = StyleSheet.create({
	searchContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		borderRadius: 8,
		paddingHorizontal: 10,
	},
	searchBar: {
		flex: 1,
		height: 40,
		fontSize: 16,
	},
	searchIcon: {
		marginRight: 10,
		transform: [{ rotateY: '-180deg' }]
	},
	floatingButton: {
		position: 'absolute',
		bottom: tabBarHeight + 20,
		backgroundColor: '#FDD066',
		width: '90%',
		height: 55,
		borderRadius: 25,
		alignItems: 'center',
		justifyContent: 'center',
		elevation: 8,
		shadowColor: '#000',
		shadowOpacity: 0.3,
		shadowRadius: 2,
		alignSelf: 'center',
	},
	skeleton: {
		width: '100%',
		height: 130,
		borderRadius: 8,
		marginBottom: 10,
	},
});

export default DiscussionHome;