import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Dimensions, TouchableOpacity } from 'react-native';
import { Chip } from 'react-native-paper';
import Global from '../../../../globalStyle';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

const DiscussionChip = ({ onChipPress }: any) => {

	const [selectedChip, setSelectedChip] = useState('All');

	const data = [
		{ id: 1, title: 'All' },
		{ id: 2, title: 'Pinned' },
		{ id: 3, title: 'My Discussion' },
		{ id: 4, title: 'Interacted' }
	];

	const handleChipPress = (title: any) => {
		setSelectedChip(title);
		onChipPress(title);
	};

	const renderItem = ({ item, index }: { item: any, index: number }) => (
		<View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
			<TouchableOpacity
				activeOpacity={0.8}
				onPress={() => handleChipPress(item.title)}
				style={{
					borderRadius: 25
				}}
				className={`h-10 justify-center items-center border-[0.8px] px-[10px] ${selectedChip === item?.title ? ' bg-primary border-transparent' : 'bg-transparent border-secondary dark:border-white'}`}
			>
				<Text
					style={Global.text_bold}
					className={`${selectedChip === item.title ? 'text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14 px-[10px]`}
				>
					{item.title}
				</Text>
			</TouchableOpacity>
		</View>
	);

	return (
		<View className="w-full">
			<FlatList
				horizontal
				data={data}
				renderItem={renderItem}
				keyExtractor={(item) => item.id.toString()}
				showsHorizontalScrollIndicator={false}
			/>
		</View>
	);
};

export default DiscussionChip;