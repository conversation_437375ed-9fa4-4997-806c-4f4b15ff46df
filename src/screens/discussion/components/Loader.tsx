import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Skeleton } from '@rneui/themed';
const Loader = () => {
    return (
        <View style={styles.card} className='bg-white border mb-3 border-dark/30 dark:bg-[#2f2f2f] dark:border-[#f8f6f3]/30'>
            <View style={styles.header} className='bg-[#F8F6F3] dark:bg-[#2A2725]'>
                <Skeleton circle LinearGradientComponent={LinearGradient} animation='wave' style={styles.skeletonProfilePic} />
                <View style={styles.headerText}>
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={styles.skeletonText} />
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={styles.skeletonTextSmall} />
                </View>
            </View>

            <View className='px-4 space-y-2'>
                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={styles.skeletonContent} />
                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={styles.skeletonContentShort} />
            </View>

            <View style={styles.footer}>
                <View className='flex-row items-center'>
                    <Skeleton circle LinearGradientComponent={LinearGradient} animation='wave' style={[styles.skeletonAvatar, {right: 0}]} />
                    <Skeleton circle LinearGradientComponent={LinearGradient} animation='wave' style={[styles.skeletonAvatar, {right: 10}]} />
                    <Skeleton circle LinearGradientComponent={LinearGradient} animation='wave' style={[styles.skeletonAvatar, {right: 20}]} />
                </View>
                <View className='flex-row items-center space-x-2'>
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' width={20} height={20} circle />
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' width={50} height={13} style={{ borderRadius: 10 }} />
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        padding: 15,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
    skeletonProfilePic: {
        width: 45,
        height: 45,
        marginRight: 10,
    },
    headerText: {
        flex: 1,
    },
    skeletonText: {
        width: '70%',
        height: 15,
        borderRadius: 20,
        marginBottom: 5,
    },
    skeletonTextSmall: {
        width: '50%',
        height: 10,
        borderRadius: 20,
    },
    skeletonContent: {
        width: '80%',
        height: 15,
        borderRadius: 10,
    },
    skeletonContentShort: {
        width: '50%',
        height: 15,
        borderRadius: 10,
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingBottom: 16
    },
    skeletonAvatar: {
        height: 40,
        width: 40,
        marginTop: 15,
    }
});

export default Loader;