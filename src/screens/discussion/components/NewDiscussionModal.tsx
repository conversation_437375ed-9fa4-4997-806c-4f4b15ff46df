import React, { useState, useEffect, useContext } from 'react';
import { View, TextInput, TouchableOpacity, Modal, StyleSheet, KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard, Dimensions } from 'react-native';
import { AntDesign, MaterialIcons } from '@expo/vector-icons';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';

const { width, height } = Dimensions.get('window');
const small = width <= 375;

const DiscussionModal = ({ modalVisible, setModalVisible, inputValue, setInputValue, onSend, replyTo }: any) => {
	const [keyboardVisible, setKeyboardVisible] = useState(false);
	const { isAndroid, colorScheme } = useContext(ClientContext);
	const [inputHeight, setInputHeight] = useState(48);
	const handleBackgroundPress = () => {
		if (keyboardVisible) {
			Keyboard.dismiss();
		} else {
			setModalVisible(false);
		}
	};

	useEffect(() => {
		const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => setKeyboardVisible(true));
		const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => setKeyboardVisible(false));

		return () => {
			keyboardDidShowListener.remove();
			keyboardDidHideListener.remove();
		};
	}, []);

	useEffect(() => {
		if (replyTo && !inputValue.startsWith(`@${replyTo} `)) {
			setInputValue(`@${replyTo} `);
		}
	}, [replyTo]);

	const handleSend = () => {
		if (inputValue.trim().length > 0) {
			if (replyTo) {
				const replyToMention = `@${replyTo} `;
				if (inputValue.trim() === replyToMention.trim()) {
					return;
				}
			}
			onSend();
			setInputValue('');
		}
	};

	const handleTextChange = (text: string) => {
		if (replyTo) {
			const replyToMention = `@${replyTo} `;
			if (text.startsWith(replyToMention)) {
				setInputValue(text);
			} else {
				setInputValue(text.replace(replyToMention, ''));
			}
		} else {
			setInputValue(text);
		}
	};

	const placeholderText = replyTo ? `@${replyTo} Add a comment ` : 'Type a message...';

	return (
		<Modal
			animationType="slide"
			statusBarTranslucent={true}
			transparent={true}
			visible={modalVisible}
			onRequestClose={() => setModalVisible(false)}
		>
			<TouchableWithoutFeedback onPress={handleBackgroundPress}>
				<View style={styles.modalContainer}>
					<KeyboardAvoidingView
						behavior={!isAndroid ? 'padding' : 'height'}
					>
						<TouchableWithoutFeedback>
							<View style={[styles.modalContent, { bottom: small ? 0 : (keyboardVisible ? 0 : 10) }]}>
								<View style={[styles.textInputContainer]} className='bg-white dark:bg-[#2f2f2f]'>
									<TouchableOpacity style={styles.emojiButton}>
										<AntDesign name="smileo" size={24} color="#c0c0c0" />
									</TouchableOpacity>
									<TextInput
										multiline
										onContentSizeChange={(event) => {
											const { height } = event.nativeEvent.contentSize;
											setInputHeight(Math.min(Math.max(48, height), 100));
										}}
										style={[
											styles.textInput,
											{
												height: inputHeight,
											}, Global.text_medium
										]}
										placeholder={placeholderText}
										className='text-secondary dark:text-white'
										value={inputValue}
										onChangeText={handleTextChange}
										focusable={true}
										returnKeyType="next"
										autoFocus={true}
										placeholderTextColor={colorScheme === 'dark' ? 'white' : '#2D2828'}
									/>
									<TouchableOpacity onPress={handleSend} disabled={inputValue?.trim().length === 0} activeOpacity={0.8} className='rotate-90'>
										<MaterialIcons name="navigation" size={26} color="#c0c0c0" />
									</TouchableOpacity>
								</View>
							</View>
						</TouchableWithoutFeedback>
					</KeyboardAvoidingView>
				</View>
			</TouchableWithoutFeedback>
		</Modal>
	);
};

const styles = StyleSheet.create({
	modalContainer: {
		flex: 1,
		justifyContent: 'flex-end',
		backgroundColor: 'rgba(0,0,0,0.5)',
	},
	modalContent: {
		padding: 16,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
	},
	textInputContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		borderRadius: 5,
		paddingHorizontal: 10,
	},
	textInput: {
		flex: 1,
		paddingVertical: 15,
		paddingHorizontal: 2,
	},
	emojiButton: {
		marginRight: 10,
	},
	emojiPickerContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		height: 300,
		backgroundColor: 'white',
	},
});

export default DiscussionModal;
