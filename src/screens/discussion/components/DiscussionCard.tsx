import React, { useContext, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Global from '../../../../globalStyle';
import { AntDesign } from '@expo/vector-icons';
import { Discussion2, Discussion3 } from '../../../assets/icons/bottom-tab/DiscussionIcon';
import { ClientContext } from '../../../context/ClientContext';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { Image } from 'expo-image';

const DiscussionCard = ({
    name,
    time,
    content,
    avatarUrls = [],
    replyCount,
    isPinned,
    onPinToggle,
    showAvatarUrls = true,
    showReplyCount = true,
    showAddComment = false,
    onAddComment = () => { },
    profileImage = '',
}: any) => {


    const [pinned, setPinned] = useState(isPinned);
    const { colorScheme } = useContext(ClientContext);

    const handlePinToggle = () => {
        setPinned(!pinned);
        if (onPinToggle) onPinToggle();
    };

    return (
        <View style={styles.card} className='bg-white border border-dark/30 dark:bg-[#2f2f2f] dark:border-[#f8f6f3]/30'>
            <View style={styles.header} className='bg-[#F8F6F3] dark:bg-[#2A2725]'>

                {profileImage ? (
                    typeof profileImage === 'string' && profileImage.startsWith('http') ? (
                        <View className='h-[45px] w-[45px] rounded-full overflow-hidden mr-2'>
                            <Image source={{ uri: profileImage }} style={styles.profilePic} />
                        </View>
                    ) : (
                        <View style={[styles.profilePic, styles.initialsContainer]} className='h-[45px] w-[45px] rounded-full overflow-hidden mr-2'>
                            <Text style={styles.initialsText}>{profileImage}</Text>
                        </View>
                    )
                ) : (
                    <View style={[styles.profilePic, styles.initialsContainer]} className='h-[45px] w-[45px] rounded-full overflow-hidden mr-2'>
                        <Text style={styles.initialsText}>{profileImage}</Text>
                    </View>
                )}
                <View style={styles.headerText}>

                    <Text style={[Global.text_bold]} className='text-secondary dark:text-white'>{capitalizeMode(name)}</Text>

                    <Text style={[Global.text_regular]} className='text-sm text-secondary dark:text-white'>{time}</Text>

                </View>

                <TouchableOpacity onPress={handlePinToggle} activeOpacity={0.7} className='h-10 w-10 items-center justify-center'>
                    <AntDesign name="pushpin" size={23} color={pinned ? '#FDD066' : '#CBCBCB'} style={{ transform: [{ rotate: '90deg' }] }} />
                </TouchableOpacity>

            </View>

            <Text style={[styles.content, Global.text_medium]} numberOfLines={!showAddComment ? 4 : 0} className={` text-secondary dark:text-white ${!showAddComment && 'ease-linear line-clamp-2'}`}>{content}</Text>


            {showAvatarUrls && (
                <View style={styles.footer}>
                    <View className='flex-row items-center'>

                        {avatarUrls.slice(0, 3).map((url: any, index: any) => (
                            typeof url === 'string' && url.startsWith('http') ? (
                                <View className='w-10 h-10 -mr-4 overflow-hidden rounded-full' key={index}>
                                    <Image source={{ uri: url }} style={styles.avatar} />
                                </View>
                            ) : (
                                <View key={index} style={[styles.avatar, styles.initialsContainer]}>
                                    <Text style={styles.initialsText}>{url}</Text>
                                </View>
                            )
                        ))}

                        {avatarUrls.length > 3 && (
                            <View className='items-center justify-center w-10 h-10 -mr-4 overflow-hidden rounded-full bg-primary'>
                                <Text style={Global.text_medium} className='text-secondary text-text13'>+{avatarUrls.length - 3}</Text>
                            </View>
                        )}
                    </View>
                    <View className='flex-row items-center space-x-2'>
                        {
                            colorScheme === 'dark' ? <Discussion2 /> : <Discussion3 />}

                        {showReplyCount && (

                            <Text style={[Global.text_regular]} className='text-xs text-secondary dark:text-white'>{replyCount} replies</Text>
                        )}
                    </View>
                </View>
            )}

            {showAddComment && (
                <TouchableOpacity onPress={() => onAddComment(name)}>
                    <Text style={[Global.text_regular]} className='px-4 pb-4 text-sm underline text-secondary dark:text-white'>Add a Comment</Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        padding: 15,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
    profilePic: {
        width: '100%',
        height: '100%',
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
    },
    initialsContainer: {
        backgroundColor: '#FDD066',
    },
    initialsText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    headerText: {
        flex: 1,
    },
    content: {
        marginBottom: 8,
        paddingHorizontal: 16,
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingBottom: 16
    },
    avatar: {
        height: '100%',
        width: '100%',
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
    }
});


export default DiscussionCard;

