import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, LayoutAnimation } from 'react-native';
import Global from '../../../../globalStyle';
import { calculateTimeDifference } from '../../../helpers/calculateTime';
import { Image } from 'expo-image';

// if (Platform.OS === 'android') {
//     UIManager.setLayoutAnimationEnabledExperimental && UIManager.setLayoutAnimationEnabledExperimental(true);
// }

const ReplyCard = ({ name, time, content, subReplies, onReply, isTopLevel = true, senderId, subreplySend, subreplayId, typeOf }: any) => {

    const [showSubReplies, setShowSubReplies] = useState(false);
    const [lastReplyCount, setLastReplyCount] = useState(subReplies?.length || 0);

    const handleToggleReplies = () => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setShowSubReplies(!showSubReplies);
    };

    useEffect(() => {
        if (subReplies?.length > lastReplyCount) {
            LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
            setShowSubReplies(true);
        }
        setLastReplyCount(subReplies?.length || 0);
    }, [subReplies]);

    const hasReplies = subReplies && subReplies.length > 0;
    const viewRepliesButtonDisabled = !hasReplies;

    const renderAvatar = (senderType: any, senderId: any) => {
        if (senderType === 'admin') {
            return (
                <Image source={{ uri: 'https://lawcube.org/assets/lex-favicon-vs1suN-s.png' }} style={styles.avatar} />
            );
        } else if (senderId?.user_profile || senderId?.profile_image) {
            return (
                <Image source={{ uri: senderId?.user_profile || senderId?.profile_image }} style={styles.avatar} />
            );
        } else {
            return (
                <Image source={{ uri: 'https://lawcube.org/assets/empty_profile-pBWKwnIj.webp' }} style={styles.avatar} />
            );
        }
    };

    const renderContent = (content: string) => {
        const mentionRegex = /^(?:@(\w+)\s)(.*)$/;
        const match = content.match(mentionRegex);

        if (match) {
            return (
                <Text style={[Global.text_regular]} className='text-text15 text-secondary dark:text-white'>
                    <Text style={Global.text_bold}>@{match[1]}</Text> {match[2]}
                </Text>
            );
        }

        return (
            <Text style={[Global.text_regular]} className='text-text15 text-secondary dark:text-white'>
                {content}
            </Text>
        );
    };


    return (
        <View style={styles.cardContainer} className={`${isTopLevel && 'bg-cardLight dark:bg-[#2F2F2F]'}`}>
            <View className='flex-row '>
                {renderAvatar(senderId?.role || senderId?.senderType || typeOf, senderId)}
                <View className='space-y-2'>
                    <Text style={[Global.text_bold]} className='text-text16 text-secondary dark:text-white w-[90%]'>{name}</Text>
                    <Text style={[Global.text_regular]} className=' text-sm text-[#888] dark:text-white'>{time}</Text>
                    <View className='w-[92%]'>
                        {renderContent(content)}
                    </View>
                    <TouchableOpacity onPress={onReply}>
                        <Text style={Global.text_medium} className='mb-2 underline text-secondary dark:text-white'>Reply to this comment</Text>
                    </TouchableOpacity>
                </View>
            </View>


            {showSubReplies && subReplies && subReplies.length > 0 && (
                <View className='left-5'>
                    {subReplies.map((subReply: any, index: number) => {
                        // console.log(subReply?._id)
                        return (
                            <ReplyCard
                                key={index}
                                name={subReply.first_name}
                                time={calculateTimeDifference(subReply.timestamp)}
                                content={subReply.message}
                                subReplies={subReply.replies}
                                onReply={() => subreplySend(subReply.first_name, subreplayId)}
                                isTopLevel={false}
                                senderType={subReply.senderType}
                                senderId={subReply}
                            />
                        )
                    })}
                </View>
            )}

            {isTopLevel && (
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={hasReplies ? handleToggleReplies : undefined}
                    style={[styles.viewRepliesButton, viewRepliesButtonDisabled && styles.viewRepliesButtonDisabled]}
                    disabled={viewRepliesButtonDisabled}
                >
                    <Text style={[Global.text_bold, viewRepliesButtonDisabled ? styles.noRepliesText : styles.viewRepliesText]} className='text-base text-secondary'>
                        {showSubReplies
                            ? 'View less replies'
                            : (hasReplies ? 'View all replies' : 'No replies')}
                    </Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    cardContainer: {
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        marginVertical: 8
    },
    avatar: {
        width: 45,
        height: 45,
        borderRadius: 25,
        marginRight: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    viewRepliesButton: {
        marginTop: 8,
        backgroundColor: '#FDD066',
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 50,
        alignItems: 'center',
        alignSelf: 'center',
    },
    viewRepliesButtonDisabled: {
        backgroundColor: '#e0e0e0',
    },
    viewRepliesText: {
        color: '#444',
    },
    noRepliesText: {
        color: '#999',
    },
});

export default ReplyCard;