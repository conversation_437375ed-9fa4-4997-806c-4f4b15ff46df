import React, { useEffect, useState, useContext } from 'react';
import { View, ScrollView } from 'react-native';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import DiscussionCard from '../components/DiscussionCard';
import ReplyCard from '../components/ReplyCard';
import DiscussionModal from '../components/NewDiscussionModal';
import { calculateTimeDifference } from '../../../helpers/calculateTime';
import { ClientContext } from '../../../context/ClientContext';
import { useSocket } from '../../../context/SocketContext';

interface Message {
	message: string;
	discussionId: string;
	senderId: {
		first_name: string;
	};
	senderType: string;
	createdAt: any;
	replies?: Message[];
}

const DiscussionDetail = ({ route }: any) => {

	const { discussion } = route.params;

	const { clientId, userData } = useContext(ClientContext);
	const { submitNestedReply, socket, pinDiscussion, unpinDiscussion, sendMessage } = useSocket();
	const [modalVisible, setModalVisible] = useState(false);
	const [inputValue, setInputValue] = useState('');
	const [replyTo, setReplyTo] = useState('');
	const [parentReplyId, setParentReplyId] = useState<string | null>(null);
	const [replies, setReplies] = useState<Message[]>([]);
	const userName = `${userData?.first_name}`;
	const userProfile = userData?.user_profile;
	// console.log(discussion?._id)

	// getting the reply and messages
	useEffect(() => {
		socket.on('messageSenders', (messageSenders: any[]) => {
			setReplies(
				messageSenders.map((msg: any) => ({
					discussionId: msg?._id || '',
					message: msg.message,
					senderId: msg.senderId,
					senderType: msg.senderType,
					createdAt: new Date(msg.timestamp),
					replies: msg.replies || [],
				}))
			);
		});

		return () => {
			socket.off('messageSenders');
		};
	}, [replies]);

	// send a replay and nested replay
	const handleSend = () => {
		if (!inputValue.trim()) return;

		if (parentReplyId) {
			// Sending a nested reply
			submitNestedReply(discussion?._id, parentReplyId, inputValue.trim(), clientId as string, userName, userProfile, 'user');
			// Immediately update the UI with the new reply
			setReplies((prevReplies: any) => {
				return prevReplies.map((reply: any) => {
					if (reply.discussionId === parentReplyId) {
						return {
							...reply,
							replies: [
								...(reply.replies || []),
								{
									message: inputValue.trim(),
									first_name: userData?.first_name,
									user_profile: userData?.user_profile,
									senderType: 'user',
									createdAt: new Date(),
									replies: [],
								},
							],
						};
					}
					return reply;
				});
			});
		} else {
			// Sending a main message
			sendMessage(discussion?._id, inputValue.trim(), clientId as string, 'user', userName);
			// After a short delay, fetch the latest messages again to reflect the new message
			setTimeout(() => {
				console.log('sendMessage')
				socket.emit('getMessageSenders', discussion?._id);
			}, 100);
			// Immediately add the new message to the replies array
			setReplies((prevReplies: any) => [
				...prevReplies,
				{
					message: inputValue.trim(),
					senderId: userData,
					senderType: 'user',
					createdAt: new Date(),
					replies: [],
				},
			]);
		}

		setInputValue('');
		setReplyTo('');
		setParentReplyId(null);
		setModalVisible(false);
	};

	// sending nested replays
	const handleReply = (username: any, replyId: string | null) => {
		setReplyTo(username);
		setInputValue(`@${username} `);
		setParentReplyId(replyId);
		setModalVisible(true);
	};

	//sending replays
	const handleAddComment = (username: string) => {
		setReplyTo(username);
		setInputValue(`@${username} `);
		setModalVisible(true);
	};

	return (
		<View className="flex-1 bg-white dark:bg-dark">

			<CustomStatusBar />

			<Header search name="Discussion" index={-3} />

			<ScrollView nestedScrollEnabled showsVerticalScrollIndicator={false} className='px-primary'>

				<View className="py-2">

					<DiscussionCard
						name={discussion?.createdby_user
							? `${discussion?.createdby_user?.first_name}`
							: discussion?.createdby_admin?.name}
						time={`${calculateTimeDifference(discussion?.createdAt)}`}
						content={discussion?.discussion_title}
						profileImage={
							discussion?.createdby_user
								? (discussion?.createdby_user?.user_profile || 'https://lawcube.org/assets/empty_profile-pBWKwnIj.webp')
								: 'https://lawcube.org/assets/lex-favicon-vs1suN-s.png'
						}
						replyCount={discussion?.total_message_count}
						isPinned={discussion?.pinned}
						showAvatarUrls={false}
						showReplyCount={false}
						showAddComment={true}
						onAddComment={() => {
							const username = discussion?.createdby_user
								? `${discussion?.createdby_user?.first_name}`
								: discussion?.createdby_admin?.name || 'Admin';
							handleAddComment(username);
						}}
						onPinToggle={() => discussion?.pinned ? unpinDiscussion(discussion?._id) : pinDiscussion(discussion?._id)}
					/>

				</View>

				{replies.map((reply, index) => {
					// console.log("Replay", reply?.senderType)
					const isLastMessage = index === replies.length - 1;
					return (
						<View className={isLastMessage ? 'mb-[25%]' : ''} key={index}>
							<ReplyCard
								key={index}
								name={reply?.senderId?.first_name || reply?.senderType === 'admin' && 'Admin'}
								time={calculateTimeDifference(reply?.createdAt)}
								content={reply?.message}
								subReplies={reply?.replies}
								onReply={() => handleReply(reply?.senderId?.first_name || reply?.senderType === 'admin' && 'Admin', reply?.discussionId)}
								senderId={reply?.senderId}
								subreplySend={handleReply}
								subreplayId={reply?.discussionId}
								typeOf={reply?.senderType}
							/>
						</View>
					);
				})}

			</ScrollView>

			<DiscussionModal
				modalVisible={modalVisible}
				setModalVisible={setModalVisible}
				inputValue={inputValue}
				setInputValue={setInputValue}
				onSend={handleSend}
				replyTo={replyTo}
			/>
		</View>
	);
};

export default DiscussionDetail;
