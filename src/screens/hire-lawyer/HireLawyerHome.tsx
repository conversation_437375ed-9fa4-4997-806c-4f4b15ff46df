import React, { useContext, useEffect, useRef, useState } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import Filters from '../../components/Filters';
import { ClientContext } from '../../context/ClientContext';
import AdvocateCard from './components/AdvocateCard';
import { HireLawyerContext } from '../../context/HireLawyerContext';
import Pagination from './components/Pagination';
import LawyerCard from './components/LawyerCard';
import Global from '../../../globalStyle';
import ProfileCard from './components/ProfileCard';
import Loader from './components/Loader';
import { FilterContext } from '../../context/FilterContext';
import { useFocusEffect } from '@react-navigation/native';
import { ClientAxiosInstance } from '../../lib/axiosInstance';

const HireLawyerHome = () => {

    const { userData, isLarge, isAndroid, showAdvocateCard } = useContext(ClientContext);
    const { lawyers, showPagination, loading, refreshing, onRefresh } = useContext(HireLawyerContext);
    const { lawyerLocation, areaOfPractice } = useContext(FilterContext);

    const [myLawyerProfile, setMyLawyerProfile] = useState<any | null>(null);

    const fetchMyLawyerProfile = async () => {
        try {
            const response = await ClientAxiosInstance.get(`/freelancer/${userData?.freelancer_id}`);
            const responseData = response?.data?.data;
            setMyLawyerProfile(responseData);

        } catch (error) {
            console.log("Fetching Freelancer Data : ", error);
        }
    }

    useFocusEffect(
        React.useCallback(() => {
            fetchMyLawyerProfile();
        }, [])
    );

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
        }
    };

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Hire a Lawyer' index={2} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className='flex-1'
                ref={scrollViewRef}
                refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
                stickyHeaderIndices={[0]}
            >

                <View className='bg-ececec'>
                    <Filters
                        chipFrom='HireLawyer'
                        categoryFilter
                        locationFilter={lawyerLocation.length > 0}
                        practiceAreaFilter={areaOfPractice.length > 0}
                        experienceFilter
                        hireLawyerFilter
                    />
                </View>

                <View className={`mt-3 space-y-4 px-primary ${isAndroid ? 'mb-20' : 'mb-24'}`}>

                    {myLawyerProfile?.approval_status === 'Approved' && <View>
                        <ProfileCard myLawyerProfile={myLawyerProfile} />
                    </View>}

                    <Text className='text-text20 text-secondary dark:text-white' style={Global.text_bold}>Freelancers</Text>

                    <View className='space-y-[3%]'>
                        {loading ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {[1, 2, 3, 4, 5, 6].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))}
                                </View>
                            ) : (
                                [1, 2, 3,].map((index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <Loader />
                                    </View>
                                ))
                            )
                        ) : lawyers?.length > 0 ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {lawyers?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <LawyerCard data={item} />
                                        </View>
                                    ))}
                                </View>
                            ) :
                                lawyers?.map((item, index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <LawyerCard data={item} />
                                    </View>
                                ))
                        ) : (
                            <View className='flex-row items-center justify-center w-full h-12'>
                                <Text className='text-text17 text-secondary dark:text-white' style={Global.text_medium}>
                                    No lawyers is available
                                </Text>
                            </View>
                        )}
                    </View>

                    <View>

                        {userData?.occupation === 'Lawyer' && (myLawyerProfile?.approval_status === 'Decline' || myLawyerProfile?.approval_status === undefined) && showAdvocateCard && (
                            <View className='mb-5'>
                                <AdvocateCard />
                            </View>
                        )}

                        {showPagination && <View>
                            <Pagination onPageChange={handlePaginationChange} />
                        </View>}

                    </View>

                </View>

            </ScrollView>

        </View>
    );
};

export default HireLawyerHome;
