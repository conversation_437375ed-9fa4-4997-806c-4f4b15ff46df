import React, { useContext, useEffect, useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import EnhancedCardComponent from '../components/EnhancedCardComponent';
import TestimonialCard from '../components/TestimonialCard';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import FeedbackModal from '../../../components/FeedbackModel';

const LawyerDetails = ({ route }: any) => {

    const { data, isContacted, feedbackSubmited } = route.params;
    
    const [feedback, setFeedback] = useState([]);
    const [isFeedbackModalVisible, setIsFeedbackModalVisible] = useState(false);

    useEffect(() => {
        const fetchFeedback = async () => {
            try {
                const response = await ClientAxiosInstance.get(`/freelancerfeedback/searchApprovedFeedback?feedback_approval_status=Approved&freelancer_id=${data?._id}`);
                const matchedFeedback = response.data.data.filter(
                    (item: any) => item.freelancer_id === data?._id
                );
                setFeedback(matchedFeedback);
            } catch (error) {
                console.log('Error fetching feedback:', error);
            }
        };

        if (data?._id) {
            fetchFeedback();
        }
    }, [data?._id]);

    useEffect(() => {
        if (isContacted && !feedbackSubmited) {
            setIsFeedbackModalVisible(true);
        }
    }, [isContacted, feedbackSubmited]);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View>
                <Header search name='Lawyer Detail' index={-1} />
            </View>

            <ScrollView nestedScrollEnabled showsVerticalScrollIndicator={false} className='w-full h-full space-y-4'>

                <View className='mb-12 space-y-4'>

                    <View className='p-primary'>
                        <EnhancedCardComponent data={data} isContacted={isContacted} />
                    </View>

                    <View className='space-y-2 px-primary'>
                        <Text style={Global.text_bold} className='text-lg text-secondary dark:text-white'>
                            About {data?.freelancer_name}
                        </Text>
                        <Text style={Global.text_medium} className='text-base text-secondary dark:text-white'>
                            {data?.description}
                        </Text>
                    </View>

                    {feedback.length > 0 && (
                        <View>
                            <TestimonialCard data={feedback.reverse()} />
                        </View>
                    )}

                </View>

            </ScrollView>

            <FeedbackModal
                type='freelancer'
                visible={isFeedbackModalVisible}
                onClose={() => setIsFeedbackModalVisible(false)}
                id={data?._id}
                title={data.freelancer_name}
            />

        </View>
    );
};

export default LawyerDetails;
