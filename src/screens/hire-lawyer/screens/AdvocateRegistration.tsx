import { <PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyboardAvoidingView, Platform, Keyboard } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import FormOne from '../components/FormOne';
import FormTwo from '../components/FormTwo';
import FormThree from '../components/FormThree';
import { ClientContext } from '../../../context/ClientContext';
import { GlobalContext } from '../../../context/GlobalProvider';

import * as DocumentPicker from 'expo-document-picker';
import axios from 'axios';

const AdvocateRegistration = ({ route }: any) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { token, isAndroid, navigation } = useContext(ClientContext);

    const initialFormState = route?.params?.formState || 1;

    const [profilePhotoImage, setProfilePhotoImage] = useState();
    const [barCouncil, setBarCouncil] = useState('');
    const [imageUri, setImageUri] = useState<string | undefined>(undefined);

    const schema = yup.object().shape({
        bar_council_id: yup
            .string()
            .required('Bar Council ID is required')
            .matches(/^[a-zA-Z0-9]+$/, 'Bar Council ID must contain only letters and numbers')
            .min(3, 'Bar Council ID must be at least 3 characters'),
        email: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        mobile: yup
            .string()
            .required('Phone number is required')
            .matches(/^[0-9]+$/, 'Phone number must contain only digits')
            .min(10, 'Phone number must be 10 digits'),
        bar_council_certificate: yup
            .mixed()
            .required('Bar Council Certificate is required')
            .test('fileType', 'Unsupported File Format. Upload only PDF', (value: any) => {
                return value && (value.mimeType === 'application/pdf');
            }),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({
        resolver: yupResolver(schema),
    });

    const [formState, setFormState] = useState(initialFormState);

    const [advocateInfo, setAdvocateInfo] = useState<any>();
    const [certificateFile, setCertificateFile] = useState<DocumentPicker.DocumentPickerAsset>();

    const { setLoading } = useContext(GlobalContext);

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true)
            const formData = new FormData();
            const updatedInfo = { ...advocateInfo, ...data };
            setAdvocateInfo(updatedInfo);

            const mimeType = updatedInfo.thumbnail_image_path.mimeType;
            const fileExtension = mimeType.split('/').pop();

            formData.append('thumbnail_image_path[]', {
                name: updatedInfo.thumbnail_image_path.fileName || `thumbnail.${fileExtension}`,
                uri: updatedInfo.thumbnail_image_path.uri,
                type: updatedInfo.thumbnail_image_path.mimeType,
                size: updatedInfo.thumbnail_image_path.fileSize,
            });
            formData.append('freelancer_name', updatedInfo.name);
            formData.append('profession_name', updatedInfo.profession_name);
            updatedInfo.category.forEach((categoryId: string, index: number) => {
                formData.append(`global_category_id[${index}]`, categoryId);
            });
            formData.append('freelancer_type', updatedInfo.freelancer_type);
            formData.append('company_location', updatedInfo.company_location);
            formData.append('company_name', updatedInfo.company_name);
            formData.append('approval_status', "Pending");
            formData.append('area_of_practice', updatedInfo.area_practice);
            formData.append('description', updatedInfo.description);
            formData.append('profile_quotes', updatedInfo.tagline);
            formData.append('years_of_experience', updatedInfo.years_of_experience);

            formData.append('barcouncil_id', updatedInfo.bar_council_id);
            formData.append('barcouncil_certificate_image[]', {
                name: updatedInfo.bar_council_certificate.name,
                uri: updatedInfo.bar_council_certificate.uri,
                type: updatedInfo.bar_council_certificate.mimeType,
                size: updatedInfo.bar_council_certificate.size,
            });
            formData.append('contact_email', updatedInfo.email);
            formData.append('phone_number', updatedInfo.mobile);

            const response = await axios.post(
                `${api_link}/freelancer/create`,
                formData,
                {
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            const responseData = response.data.data;

            navigation.navigate('AdvocateConfirmation', {
                data: responseData,
                image: updatedInfo.thumbnail_image_path.uri,
            });

        } catch (error: any) {
            console.log('Error submitting form : ', error.response.data);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        const backAction = () => {
            if (formState === 2) {
                setFormState(1);
                return true;
            }
            if (formState === 3) {
                setFormState(2);
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [formState]);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Registration' index={-1} />

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'padding' : 'height'}
                style={{ flex: 1 }}
            >
                <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='w-full h-full'>

                    <View className='w-full h-full py-4 mb-4 px-primary'>

                        {formState === 1 ? (
                            <FormOne
                                setAdvocateInfo={setAdvocateInfo}
                                advocateInfo={advocateInfo}
                                setFormState={setFormState}
                                profilePhotoImage={profilePhotoImage}
                                setProfilePhotoImage={setProfilePhotoImage}
                                imageUri={imageUri}
                                setImageUri={setImageUri}
                            />
                        ) : formState === 2 ? (
                            <FormTwo setAdvocateInfo={setAdvocateInfo} advocateInfo={advocateInfo} setFormState={setFormState} />
                        ) : (
                            <FormThree
                                control={control}
                                errors={errors}
                                handleSubmit={handleSubmit}
                                onSubmit={onSubmit}
                                setFormState={setFormState}
                                setValue={setValue}
                                barCouncil={barCouncil}
                                setBarCouncil={setBarCouncil}
                                certificateFile={certificateFile}
                                setCertificateFile={setCertificateFile}
                            />
                        )}

                    </View>

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default AdvocateRegistration;
