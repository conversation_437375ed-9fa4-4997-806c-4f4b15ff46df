import { <PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyboardAvoidingView, Platform, Keyboard } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import { GlobalContext } from '../../../context/GlobalProvider';
import EditProfileOne from '../components/EditProfileOne';
import EditProfileTwo from '../components/EditProfileTwo';
import EditProfileThree from '../components/EditProfileThree';
import { ClientContext } from '../../../context/ClientContext';
import axios from 'axios';

const EditAdvocateProfile = ({ route }: any) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const initialFormState = route?.params?.formState || 1;
    const editDetails = route?.params?.lawyerDetail || {};

    const [profilePhotoImage, setProfilePhotoImage] = useState();
    const [imageUri, setImageUri] = useState<string>(editDetails?.thumbnail_image_path);
    const categoriesId: string[] = [];

    const schema = yup.object().shape({
        contact_email: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        phone_number: yup
            .string()
            .required('Phone number is required')
            .matches(/^[0-9]+$/, 'Phone number must contain only digits')
            .min(10, 'Phone number must be 10 digits'),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue
    } = useForm({
        resolver: yupResolver(schema),
    });

    const [formState, setFormState] = useState(initialFormState);
    const [newDetails, setNewDetails] = useState<any>();

    const { setLoading } = useContext(GlobalContext);
    const { token, navigation, isAndroid } = useContext(ClientContext);

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true);
            const details = { ...data, ...newDetails };

            const formData = new FormData();

            const mimeType = details.thumbnail_image_path.mimeType;
            const fileExtension = mimeType.split('/').pop();

            if (typeof details.thumbnail_image_path === 'object' && Object.keys(details.thumbnail_image_path).length !== 0) {
                formData.append('thumbnail_image_path[]', {
                    name: details?.thumbnail_image_path?.fileName || `thumbnail.${fileExtension}`,
                    uri: details?.thumbnail_image_path?.uri,
                    type: details?.thumbnail_image_path?.mimeType,
                    size: details?.thumbnail_image_path?.fileSize,
                });
            } else {
                formData.append('thumbnail_image_path[]', editDetails.thumbnail_image_path);
            }

            const fields = [
                'freelancer_name', 'profession_name',
                'area_of_practice', 'description', 'profile_quotes',
                'years_of_experience', 'contact_email', 'phone_number'
            ];

            fields.forEach(field => {
                const value = details[field];
                if (value) formData.append(field, value);
            });

            details.global_category_id.forEach((id: string) => {
                formData.append('global_category_id[]', id);
            });

            formData.append('barcouncil_id', editDetails.barcouncil_id);
            formData.append('barcouncil_certificate_image[]', editDetails.barcouncil_certificate_path);
            formData.append('company_location', editDetails.company_location);
            formData.append('company_name', editDetails.company_name);
            formData.append('freelancer_type', editDetails.company_name ? 'Company' : 'Individual');
            formData.append('approval_status', "Approved");

            const response = await axios.put(`${api_link}/freelancer/${editDetails?._id}`,
                formData,
                {
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            const responseData = response.data.data;
            // console.log("Updated profile : ", responseData);
            navigation.navigate('HireLawyerHome');

        } catch (error: any) {
            console.log('Error submitting form : ', error.response.data);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const backAction = () => {
            if (formState === 2) {
                setFormState(1);
                return true;
            }
            if (formState === 3) {
                setFormState(2);
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [formState]);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Edit Profile' index={-1} />
            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >
                <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='w-full h-full'>

                    <View className='w-full h-full py-4 px-primary'>

                        {formState === 1 ? (
                            <EditProfileOne
                                setFormState={setFormState}
                                profilePhotoImage={profilePhotoImage}
                                setProfilePhotoImage={setProfilePhotoImage}
                                newDetails={newDetails}
                                setNewDetails={setNewDetails}
                                imageUri={imageUri}
                                setImageUri={setImageUri}
                                editDetails={editDetails}
                                categoriesId={categoriesId}
                            />
                        ) : formState === 2 ? (
                            <EditProfileTwo
                                setFormState={setFormState}
                                newDetails={newDetails}
                                setNewDetails={setNewDetails}
                                editDetails={editDetails}
                            />
                        ) : (
                            <EditProfileThree
                                editDetails={editDetails}
                                control={control}
                                errors={errors}
                                handleSubmit={handleSubmit}
                                onSubmit={onSubmit}
                                setFormState={setFormState}
                            />
                        )}

                    </View>

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default EditAdvocateProfile;