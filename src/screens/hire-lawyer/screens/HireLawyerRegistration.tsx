import React, { useContext, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert, KeyboardAvoidingView, Platform, Keyboard } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { TextInput, Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import Button from '../../../components/Button';
import { GlobalContext } from '../../../context/GlobalProvider';
import { Dropdown } from 'react-native-element-dropdown';
import { FormAxiosInstance } from '../../../lib/axiosInstance';
import AsyncStorage from '@react-native-async-storage/async-storage';

const schema = yup.object().shape({
	name: yup
		.string()
		.required('Name is required')
		.min(3, 'Name must be at least 3 characters')
		.max(50, 'Name must be atmost 50 characters'),
	mail_id: yup.string()
		.required('Email is required')
		.transform((value) => (value ? value.toLowerCase() : value))
		.matches(
			/^[^\s@]+@[^\s@]+\.[^\s@]+$/,
			'Invalid email format'
		),
	phone_number: yup
		.string()
		.required('Phone number is required')
		.matches(/^[0-9]+$/, 'Phone number must contain only digits')
		.min(10, 'Phone number must be 10 digits'),
	description: yup
		.string()
		.required('Description is required')
		.min(5, 'Description must be at least 5 characters')
		.max(300, 'Description cannot exceed 300 characters'),
	country_code: yup.string()
});

const ContactForm = ({ onSuccess, freelancer_id }: any) => {

	const { setLoading } = useContext(GlobalContext);
	const { navigation, colorScheme, clientId, isAndroid } = useContext(ClientContext);

	const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm({
		resolver: yupResolver(schema),
	});
	const descriptionValue = watch('description', '');

	const onSubmit = async (data: any) => {
		try {
			Keyboard.dismiss();
			setLoading(true);
			const response = await FormAxiosInstance.post('/freelancercontact/create', {
				...data,
				user_id: clientId,
				freelancer_id: [freelancer_id]
			});

			const result = response.data;
			if (result.success) {
				await AsyncStorage.setItem('registrationDate', new Date().toISOString());
				onSuccess(result.data._id);
				navigation.navigate('SubmissionSuccess', { formData: result.data });
			} else {
				console.log('Submission failed', result.message || 'Please try again later.');
			}

		} catch (error) {
			console.log('Error', 'Failed to submit contact form. Please try again later.');
		} finally {
			setLoading(false);
		}
	};

	const renderItem = (item: any) => {
		return (
			<View
				style={[
					styles.item,
					{
						backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
						borderBottomColor: '#a8a8a8',
					},
				]}
			>
				<Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
					{item.label} ({item.name})
				</Text>
			</View>
		);
	};

	return (
		<View className='mb-3 px-primary top-4'>

			<View className='bg-cardLight dark:bg-[#2F2F2F] space-y-3 pb-5 rounded-lg'>

				<View className='items-center justify-center rounded-tl-lg rounded-tr-lg bg-primary'>
					<Text style={Global.text_bold} className='p-4 text-xl text-secondary'>Contact Me</Text>
				</View>

				<View className='px-3 space-y-3'>

					<View className='space-y-2'>
						<Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Name</Text>
						<View>
							<Controller
								control={control}
								render={({ field: { onChange, onBlur, value } }) => (
									<TextInput
										contentStyle={{ fontFamily: 'DMSans-Regular' }}
										className='w-full px-1 tracking-wide bg-transparent text-text17'
										placeholder='Name'
										placeholderTextColor='#a8a8a8'
										onChangeText={onChange}
										onBlur={onBlur}
										value={value}
										error={!!errors?.name}
										mode='outlined'
										activeOutlineColor='#a8a8a8'
										outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
										outlineColor='#a8a8a8'
										textColor={colorScheme === 'dark' ? '#fff' : 'black'}
									/>
								)}
								name="name"
								defaultValue=""
							/>
						</View>
						{errors.name && <Text style={[styles.error, Global.text_regular]}>* {errors.name.message}</Text>}
					</View>

					<View className='space-y-2'>
						<Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Mail Id</Text>
						<View>
							<Controller
								control={control}
								render={({ field: { onChange, onBlur, value } }) => (
									<TextInput
										contentStyle={{ fontFamily: 'DMSans-Regular' }}
										className='w-full px-1 tracking-wide bg-transparent text-text17'
										placeholder='E-Mail'
										placeholderTextColor='#a8a8a8'
										inputMode='email'
										onChangeText={item => (
											onChange(item.toLowerCase())
										)}
										onBlur={onBlur}
										value={value}
										error={!!errors?.mail_id}
										mode='outlined'
										autoCapitalize='none'
										keyboardType='email-address'
										activeOutlineColor='#a8a8a8'
										outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
										outlineColor='#a8a8a8'
										textColor={colorScheme === 'dark' ? '#fff' : 'black'}
									/>
								)}
								name="mail_id"
								defaultValue=""
							/>
						</View>
						{errors.mail_id && <Text style={[styles.error, Global.text_regular]}>* {errors.mail_id.message}</Text>}
					</View>

					<View className='space-y-2'>
						<Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Phone Number</Text>
						<View className='flex-row items-center'>
							<View className='w-full'>
								<Controller
									control={control}
									name="country_code"
									defaultValue='91'
									render={({ field: { onChange, value } }) => (
										<Dropdown
											style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: errors?.phone_number?.message ? 'red' : '#a8a8a8' }]}
											selectedTextStyle={{ color: colorScheme === 'light' ? '#000' : '#fff' }}
											data={[]}
											search
											searchPlaceholder='Search Country Code'
											inputSearchStyle={{ borderRightWidth: 0, borderLeftWidth: 0, borderTopWidth: 0 }}
											labelField="label"
											valueField="value"
											disable
											placeholder="(+91)"
											renderItem={renderItem}
											onChange={item => {
												onChange(item.value);
											}}
											itemContainerStyle={{
												borderBottomWidth: 0.5,
												borderBottomColor: '#a8a8a8',
												backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
											}}
											itemTextStyle={{ color: colorScheme === 'light' ? '#000' : '#fff', fontSize: 17 }}
											containerStyle={{
												borderRadius: 8,
												overflow: 'hidden',
												backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
											}}
											showsVerticalScrollIndicator={false}
											placeholderStyle={{ color: colorScheme === 'dark' ? '#fff' : '#2d2828' }}
											keyboardAvoiding
											dropdownPosition='auto'
											fontFamily='DMSans-Regular'
											value={value}
										/>
									)}
								/>
							</View>
							<View className='w-[75%] absolute right-0'>
								<Controller
									control={control}
									render={({ field: { onChange, onBlur, value } }) => (
										<TextInput
											contentStyle={{ fontFamily: 'DMSans-Regular' }}
											className='w-full tracking-wider bg-transparent text-text17'
											placeholder='Phone Number'
											placeholderTextColor='#a8a8a8'
											inputMode='numeric'
											maxLength={10}
											onChangeText={onChange}
											onBlur={onBlur}
											value={value}
											error={!!errors?.phone_number?.message}
											mode='outlined'
											activeOutlineColor='#a8a8a8'
											outlineStyle={{
												borderTopWidth: 1.2,
												borderBottomWidth: 1.2,
												borderRightWidth: 1.2,
												borderLeftWidth: 0,
												borderTopRightRadius: 8,
												borderBottomRightRadius: 8,
												borderBottomLeftRadius: 0,
											}}
											outlineColor='#a8a8a8'
											textColor={colorScheme === 'dark' ? '#fff' : 'black'}
										/>
									)}
									name="phone_number"
									defaultValue=''
								/>
							</View>
						</View>
						{errors.phone_number && <Text style={[styles.error, Global.text_regular]}>* {errors.phone_number.message}</Text>}
					</View>

					<View className='space-y-3'>
						<View className='space-y-1'>
							<Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Description About</Text>
							<Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_regular}>(Please provide some description)</Text>
						</View>
						<View>
							<Controller
								control={control}
								render={({ field: { onChange, onBlur, value } }) => (
									<TextInput
										contentStyle={{ fontFamily: 'DMSans-Regular', marginVertical: isAndroid ? 10 : 0 }}
										className='w-full tracking-wide bg-transparent text-text17'
										placeholder='Description'
										placeholderTextColor='#a8a8a8'
										onChangeText={(text) => {
											if (text.length <= 300) {
												onChange(text);
												setValue('description', text);
											}
										}}
										onBlur={onBlur}
										value={value}
										error={!!errors?.description}
										mode='outlined'
										style={[styles.input, { textAlignVertical: 'top' }]}
										multiline
										textAlignVertical='top'
										activeOutlineColor='#a8a8a8'
										outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
										outlineColor='#a8a8a8'
										textColor={colorScheme === 'dark' ? '#fff' : 'black'}
									/>
								)}
								name="description"
								defaultValue=""
							/>
							<Text style={Global.text_regular} className='self-end text-gray-500 -top-4'>
								({descriptionValue.length}/300)
							</Text>
							{errors.description && <Text className='-top-6' style={[styles.error, Global.text_regular]}>* {errors.description.message}</Text>}
						</View>
					</View>
					<View className='self-center'>
						<Button onPress={handleSubmit(onSubmit)} title='Submit Details' color='#000' />
					</View>
				</View>

			</View>

		</View>
	);
};

const HireLawyerRegistration = ({ route }: any) => {

	const freelancer_id = route?.params?.freelancer_id || '';
	const { isAndroid } = useContext(ClientContext);

	const [feedbackVisible, setFeedbackVisible] = useState(false);
	const [formId, setFormId] = useState(null);

	const handleRegistrationSuccess = (id: any) => {
		setFormId(id);
		setFeedbackVisible(true);
	};

	return (
		<PaperProvider theme={theme}>

			<View className='flex-1 bg-white dark:bg-dark'>

				<CustomStatusBar />

				<View><Header search name='Registration' index={-4} /></View>

				<KeyboardAvoidingView
					behavior={!isAndroid ? 'padding' : 'height'}
					style={{ flex: 1 }}
				>
					<ScrollView nestedScrollEnabled showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='w-full h-full'>

						<View className='mb-4 space-y-6'>
							<ContactForm freelancer_id={freelancer_id} onSuccess={handleRegistrationSuccess} />
						</View>

					</ScrollView>

				</KeyboardAvoidingView>

			</View>

		</PaperProvider>
	);
};

const theme = {
	...DefaultTheme,
	colors: {
		...DefaultTheme.colors,
		primary: '#FDD066',
		accent: '#FDD066',
	},
};

const styles = StyleSheet.create({
	header: {
		fontSize: 24,
		fontWeight: 'bold',
		marginBottom: 20,
	},
	input: {
		marginBottom: 20,
		minHeight: 150,
	},
	textArea: {
		marginBottom: 10,
	},
	error: {
		color: 'red',
		marginBottom: 10,
	},
	dropdown: {
		height: 56,
		borderLeftWidth: 1.2,
		borderTopWidth: 1.2,
		borderBottomWidth: 1.2,
		borderRadius: 8,
		paddingLeft: 16,
		paddingRight: '75%',
		overflow: 'hidden',
	},
	item: {
		padding: 16,
		borderBottomWidth: 0.5,
	}
});

export default HireLawyerRegistration;

