import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Global from '../../../../globalStyle';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Entypo from '@expo/vector-icons/FontAwesome6';
import { ClientContext } from '../../../context/ClientContext';

const SubmissionSuccess = ({ route }: any) => {
	
	const { formData } = route.params;
	const { navigation, colorScheme } = useContext(ClientContext);

	// console.log(formData)

	return (
		<View className='items-center justify-center flex-1 space-y-4 bg-white  px-primary dark:bg-black'>

			<CustomStatusBar barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} />

			<View className=' bg-[#2baa16] p-3 rounded-full'>
				<Entypo name='check' size={20} color='#fff' />
			</View>

			<Text style={Global.text_bold} className='text-xl  text-secondary dark:text-white'>We have submitted your details</Text>

			<Text style={Global.text_medium} className='text-base  text-secondary dark:text-white'>Please do share this with your friends</Text>

			<View className='w-full bg-[#f9f9f9] dark:bg-[#2F2F2F] p-3 rounded-lg space-y-4'>

				<Text style={Global.text_medium} className=' text-secondary dark:text-white'><Text style={Global.text_bold} className='text-base text-secondary dark:text-white'>Name                          :</Text> {formData?.name}</Text>
				<Text style={Global.text_medium} className=' text-secondary dark:text-white'><Text style={Global.text_bold} className='text-base text-secondary dark:text-white'>Mail Id                         :</Text> {formData?.mail_id}</Text>
				<Text style={Global.text_medium} className=' text-secondary dark:text-white'><Text style={Global.text_bold} className='text-base text-secondary dark:text-white'>Phone Number        :</Text> {formData?.phone_number}</Text>
				<Text style={Global.text_medium} className=' text-secondary dark:text-white'><Text style={Global.text_bold} className='text-base text-secondary dark:text-white'>Description About :</Text> {formData?.description}</Text>

				<TouchableOpacity
					activeOpacity={0.8}
					className='self-center rounded-full  bg-primary'
					onPress={() => navigation.navigate('HireLawyerHome')}
				>
					<Text style={Global.text_bold} className='px-4 py-2 text-lg text-secondary'>Continue</Text>
				</TouchableOpacity>

			</View>
			
		</View>
	);
};


export default SubmissionSuccess;
