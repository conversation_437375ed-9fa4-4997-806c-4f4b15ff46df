import { View, Text, BackHandler, <PERSON><PERSON><PERSON>, ScrollView } from 'react-native'
import React, { useContext, useEffect } from 'react'
import LawyerCard from '../components/LawyerCard'
import Global from '../../../../globalStyle';
import { Entypo } from '@expo/vector-icons';
import { ClientContext } from '../../../context/ClientContext';


const AdvocateConfirmation = ({ route }: any) => {

    const data = route.params?.data || {};
    const image = route.params?.image || '';
    const updatedInfo = {...data, thumbnail_image_path:image}

    const { colorScheme, navigation, isAndroid } = useContext(ClientContext);

    useEffect(() => {
        const backAction = () => {
            navigation.navigate("HireLawyerHome");
            return true;
        };

        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            backAction
        );

        return () => backHandler.remove();
    }, []);

    const STATUSBAR_HEIGHT = isAndroid ? StatusBar.currentHeight : 0;

    return (
        <View className='flex-1 bg-white dark:bg-dark' style={{ paddingTop: STATUSBAR_HEIGHT }}>

            <StatusBar
                barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'}
                translucent={true}
                animated={true}
                backgroundColor='transparent'
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexGrow: 1,
                    justifyContent: 'center'
                }}
            >

                <View className='h-full w-full px-primary space-y-2 items-center justify-center py-5'>

                    <View className='w-full flex-col items-center space-y-2'>

                        <View className='h-12 w-12 bg-[#2baa16] rounded-full items-center justify-center'>
                            <Entypo name='check' size={30} color='white' />
                        </View>

                        <View className='w-[75%]'>
                            <Text
                                className='text-xl text-secondary dark:text-white text-center'
                                style={Global.text_bold}
                            >
                                Thank you for joining in Lawcube
                            </Text>
                        </View>

                        <Text className='text-lg text-secondary dark:text-white' style={Global.text_regular}>Please do share this with your friends</Text>

                    </View>

                    <View className='w-full'>
                        <LawyerCard data={updatedInfo} showConfirmation />
                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default AdvocateConfirmation