import React, { useContext, useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import * as yup from 'yup';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import AdvocateProfileCard from './AdvocateProfileCard';
import Global from '../../../../globalStyle';
import { TextInput } from 'react-native-paper';
import { ClientContext } from '../../../context/ClientContext';
import { Feather, FontAwesome } from '@expo/vector-icons';
import { FilterContext } from '../../../context/FilterContext';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { Dropdown, MultiSelect } from 'react-native-element-dropdown';

interface EditProfileOneProps {
    setFormState: (value: number) => void;
    profilePhotoImage: any;
    setProfilePhotoImage: (info: any) => void;
    imageUri: string | undefined;
    setImageUri: (uri: string) => void;
    editDetails: any;
    categoriesId: string[];
    newDetails: any;
    setNewDetails: (val: any) => void;
}

const EditProfileOne: React.FC<EditProfileOneProps> = ({ imageUri, setImageUri, categoriesId, setFormState, editDetails, profilePhotoImage, setProfilePhotoImage, newDetails, setNewDetails }) => {

    const { colorScheme } = useContext(ClientContext);
    const { lawyerCategory } = useContext(FilterContext);

    const categories = lawyerCategory ? lawyerCategory.map((item: any) => ({
        label: capitalizeMode(item?.global_category_name),
        value: item?._id,
    })) : [];

    const schema = yup.object().shape({
        thumbnail_image_path: yup
            .mixed()
            .required('Profile image is required')
            .test(
                'is-string-or-object',
                (value) => typeof value === 'string' || (typeof value === 'object' && value !== null)
            ),
        freelancer_name: yup
            .string()
            .required('Name is required')
            .matches(/^[a-zA-Z ]+$/, 'Name must contain only letters')
            .min(3, 'Name must be at least 3 characters')
            .max(50, 'Name must be atmost 50 characters'),
        profession_name: yup
            .string()
            .required('Profession is required')
            .matches(/^[a-zA-Z ]+$/, 'Profession must contain only letters')
            .min(3, 'Profession name must be at least 3 characters')
            .max(50, 'Profession name must be atmost 50 characters'),
        global_category_id: yup.
            array()
            .min(1, 'Please select at least one category')
            .required('Category is required')
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        setError
    } = useForm({
        resolver: yupResolver(schema)
    });

    useEffect(() => {
        setValue("global_category_id", newDetails?.global_category_id || editDetails?.global_category_id.map((cat: any) => cat._id));
        setValue("freelancer_name", newDetails?.freelancer_name || editDetails?.freelancer_name);
        setValue("profession_name", newDetails?.profession_name || editDetails?.profession_name);
        setValue("thumbnail_image_path", newDetails?.thumbnail_image_path || editDetails?.thumbnail_image_path);
    }, [newDetails, setValue, editDetails]);

    const onSubmit = async (data: any) => {
        setNewDetails({ ...newDetails, ...data, "thumbnail_image_path": profilePhotoImage });
        setFormState(2);
    };

    const multiSelectItem = (item: any, selected: boolean | undefined) => {
        if (categoriesId.includes(item.value)) {
            const index = categoriesId.indexOf(item.value);
            if (index > -1) {
                categoriesId.splice(index, 1);
            }
        }
        else {
            categoriesId.push(item.value);
        }

        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
                className='flex-row items-center justify-between'
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item?.label}
                </Text>
                {selected && <Feather name="check" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
            </View>
        );
    };

    return (
        <View className='pb-4 space-y-2 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='items-center w-full py-4 bg-primary rounded-t-md'>
                <Text style={Global.text_bold} className='text-xl text-secondary'>
                    Freelancer Details (1/3)
                </Text>
            </View>

            <View className='px-2 space-y-4'>

                <View className='space-y-2'>
                    <Controller
                        control={control}
                        name="thumbnail_image_path"
                        defaultValue={editDetails.thumbnail_image_path}
                        render={({ field }) => (
                            <AdvocateProfileCard
                                setValue={field.onChange}
                                errors={errors}
                                imageUri={imageUri}
                                setImageUri={setImageUri}
                                setProfilePhotoImage={setProfilePhotoImage}
                                setError={setError}
                            />
                        )}
                    />
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Name'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.freelancer_name?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="freelancer_name"
                            defaultValue={editDetails.freelancer_name}
                        />
                        {errors?.freelancer_name?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.freelancer_name?.message?.toString()}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Profession Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Eg. Senior Advocate'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.profession_name?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="profession_name"
                            defaultValue={editDetails.profession_name}
                        />
                        {errors?.profession_name?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.profession_name?.message?.toString()}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Category</Text>
                    <View>
                        <Controller
                            control={control}
                            name="global_category_id"
                            defaultValue={editDetails.global_category_id}
                            render={({ field: { onChange, value } }) => (
                                <MultiSelect
                                    style={[styles.dropdown, { borderColor: errors?.global_category_id?.message ? '#ef4444' : '#a8a8a8' }]}
                                    placeholder={`Category ${Array.isArray(value) && value.length > 0 ? `(${value.length})` : ''}`}
                                    placeholderStyle={{
                                        color: Array.isArray(value) && value.length > 0 ? colorScheme === 'dark' ? '#fff' : '#2d2828' : '#a8a8a8',
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    containerStyle={{
                                        borderRadius: 8,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : 'white',
                                    }}
                                    renderItem={(item, selected) => multiSelectItem(item, selected)}
                                    showsVerticalScrollIndicator
                                    visibleSelectedItem={false}
                                    data={categories}
                                    labelField="label"
                                    valueField="value"
                                    value={value}
                                    onChange={onChange}
                                />
                            )}
                        />
                        {errors?.global_category_id?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.global_category_id?.message?.toString()}</Text>
                        )}
                    </View>
                </View>

                {!editDetails.company_name && <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Freelancer Type</Text>
                    <View>
                        <TextInput
                            contentStyle={{
                                fontFamily: 'DMSans-Regular'
                            }}
                            placeholder='Individual'
                            placeholderTextColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
                            disabled
                            className='w-full px-1 tracking-wide bg-transparent text-text17'
                            mode='outlined'
                            style={{ borderWidth: 0.8, borderRadius: 8, borderColor: '#a8a8a890', opacity: 0.7 }}
                        />
                    </View>
                </View>}

                {editDetails.company_name && <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Company Name</Text>
                    <View>
                        <TextInput
                            contentStyle={{
                                fontFamily: 'DMSans-Regular'
                            }}
                            placeholder={editDetails.company_name}
                            placeholderTextColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
                            disabled
                            className='w-full px-1 tracking-wide bg-transparent text-text17'
                            mode='outlined'
                            style={{ borderWidth: 1.2, borderRadius: 8, borderColor: '#a8a8a850', opacity: 0.7 }}
                        />
                    </View>
                </View>}

                {editDetails.company_location && <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Company Location</Text>
                    <View>
                        <TextInput
                            contentStyle={{
                                fontFamily: 'DMSans-Regular'
                            }}
                            placeholder={editDetails.company_location}
                            placeholderTextColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
                            disabled
                            className='w-full px-1 tracking-wide bg-transparent text-text17'
                            mode='outlined'
                            style={{ borderWidth: 1.2, borderRadius: 8, borderColor: '#a8a8a850', opacity: 0.7 }}
                        />
                    </View>
                </View>}

                <View className='flex-row items-center justify-around w-full'>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        disabled
                        className='bg-transparent border-[1px] border-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <FontAwesome name='angle-left' size={20} color='#a8a8a8' />
                        <Text style={Global.text_bold} className='text-greycolor text-text16'>Go Back</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={handleSubmit(onSubmit)}
                        className='bg-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <Text style={Global.text_bold} className='text-secondary text-text16'>Continue</Text>
                        <FontAwesome name='angle-right' size={20} color='#000' />
                    </TouchableOpacity>
                </View>

            </View>

        </View>
    );
};

export default EditProfileOne;


const styles = StyleSheet.create({
    dropdown: {
        height: 55,
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden',
        fontFamily: 'DMSans-SemiBold',
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    },
    selectedText: {
        color: '#2d2828',
        fontFamily: 'DMSans-SemiBold',
        fontWeight: '700',
    },
    weekDaysContainer: {
        borderBottomColor: '#dedede'
    },
});