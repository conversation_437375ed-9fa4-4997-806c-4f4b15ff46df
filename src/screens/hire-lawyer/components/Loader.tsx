import { View } from 'react-native'
import React, { useContext } from 'react'
import { Skeleton } from '@rneui/themed'
import { LinearGradient } from 'expo-linear-gradient'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { ClientContext } from '../../../context/ClientContext'

const Loader = () => {

    const { isLarge } = useContext(ClientContext);

    return (
        <View className='bg-cardLight dark:bg-[#2F2F2F] p-2 rounded-md space-y-2'>

            <Skeleton
                style={{
                    width: '100%',
                    height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50),
                    borderRadius: 5,
                }}
                LinearGradientComponent={LinearGradient}
                animation="wave"
            />

            <Skeleton style={{ height: 25, borderRadius: 25, width: '20%' }} LinearGradientComponent={LinearGradient} animation="wave" />

            <View className='w-full space-y-3'>
                <Skeleton style={{ height: 15, width: '85%', borderRadius: 2 }} LinearGradientComponent={LinearGradient} animation="wave" />
                <Skeleton style={{ height: 15, width: '50%', borderRadius: 2 }} LinearGradientComponent={LinearGradient} animation="wave" />
                <Skeleton style={{ height: 15, width: '60%', borderRadius: 2 }} LinearGradientComponent={LinearGradient} animation="wave" />
                <Skeleton style={{ height: 15, width: '80%', borderRadius: 2 }} LinearGradientComponent={LinearGradient} animation="wave" />
                <Skeleton style={{ height: 15, width: '80%', borderRadius: 2 }} LinearGradientComponent={LinearGradient} animation="wave" />
            </View>

        </View>
    )
}

export default Loader