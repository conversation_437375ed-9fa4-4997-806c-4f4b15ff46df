import { View, Text, Modal } from 'react-native'
import React from 'react';

import Entypo from '@expo/vector-icons/FontAwesome6'
import Global from '../../../../globalStyle';
import LawyerCard from './LawyerCard';

interface AdvocateConfirmProps {
    showConfirmation: boolean;
    setShowConfirmation: (val: boolean) => void;
    data: any;
}

const AdvocateConfirm: React.FC<AdvocateConfirmProps> = ({ showConfirmation, setShowConfirmation, data }) => {

    const handleClose = () => {
        setShowConfirmation(false);
    }

    return (
        <Modal animationType='fade' statusBarTranslucent visible={showConfirmation} onRequestClose={handleClose}>

            <View className='items-center justify-center w-full h-full py-5 space-y-2 bg-white dark:bg-dark px-primary'>

                <View className='flex-col items-center w-full space-y-2'>

                    <View className='h-12 w-12 bg-[#2baa16] rounded-full items-center justify-center'>
                        <Entypo name='check' size={30} color='white' />
                    </View>

                    <View className='w-[75%]'>
                        <Text
                            className='text-xl text-center text-secondary dark:text-white'
                            style={Global.text_bold}
                        >
                            Thank you for joining in Lawcube
                        </Text>
                    </View>

                    <Text className='text-lg text-secondary dark:text-white' style={Global.text_regular}>Please do share this with your friends</Text>

                </View>

                <View className='w-full'>
                    <LawyerCard data={data} showConfirmation={showConfirmation} />
                </View>

            </View>

        </Modal>
    )
}

export default AdvocateConfirm