import { View, Text } from 'react-native'
import React, { useContext, useState } from 'react'
import Global from '../../../../globalStyle'
import Badge from '../../../components/Badge'
import Button from '../../../components/Button'
import { ClientContext } from '../../../context/ClientContext'
import { ClientAxiosInstance } from '../../../lib/axiosInstance'
import { useFocusEffect } from '@react-navigation/native'
import { Image } from 'expo-image';

const ProfileCard = ({myLawyerProfile}:any) => {

    const { navigation, userData, colorScheme } = useContext(ClientContext);

    const handleAdvocateProfile = () => {
        navigation.navigate('LawyerDetails', { data: myLawyerProfile });
    }

    return (
        <View className='space-y-3'>

            <Text className='text-lg text-center text-secondary dark:text-white' style={Global.text_bold}>Your Profile</Text>

            <View className='w-full px-3 pt-4 pb-4 space-y-6 rounded-md bg-cardLight dark:bg-darkcard border-[1px] border-[#fdd066] dark:border-primary'>

                <View className='flex-row items-center justify-between w-full'>

                    <View className='w-[45%] h-[100px]'>
                        <Image
                            source={myLawyerProfile?.thumbnail_image_path ?
                                { uri: myLawyerProfile?.thumbnail_image_path } : require('../../../assets/images/placeholder-thumbnail.png')
                            }
                            style={{ width: '100%', height: '100%', borderRadius: 6 }}
                            contentFit='cover'
                        />
                    </View>

                    <View className='w-[52%] h-[100px] justify-between py-[3px]'>
                        <Badge name='Freelancer' />
                        <Text className='text-xl text-secondary dark:text-white' style={Global.text_bold} numberOfLines={1} ellipsizeMode='tail'>{myLawyerProfile?.freelancer_name}</Text>
                        <Text className='text-text15 text-greycolor' style={Global.text_medium} numberOfLines={1} ellipsizeMode='tail'>{myLawyerProfile?.profession_name}</Text>
                    </View>

                </View>

                <View>
                    <Button
                        title='Go to your profile'
                        onPress={handleAdvocateProfile}
                        height={55}
                    />
                </View>

            </View>

            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

        </View>
    )
}

export default ProfileCard