import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import Button from '../../../components/Button'
import { ClientContext } from '../../../context/ClientContext'

const AdvocateCard = () => {

    const { navigation } = useContext(ClientContext);

    return (
        <View className='py-6 px-4 bg-cardLight dark:bg-darkcard rounded-md border-[0.4px] border-[#fdd066] space-y-7'>

            <Text
                className='text-lg tracking-wide text-center text-secondary dark:text-white'
                style={Global.text_bold}
            >
                Are you seeking for Clients to improve your career ? Add your profile to Lawcube and get more clients
            </Text>

            <View className='space-y-4'>

                <View className='flex-row items-center space-x-2'>
                    <View className='w-2 h-2 rounded-full bg-primary' />
                    <Text
                        className='tracking-wide text-secondary dark:text-white text-text16'
                        style={Global.text_regular}
                    >
                        Submit details of your profession
                    </Text>
                </View>

                <View className='flex-row items-center space-x-2'>
                    <View className='w-2 h-2 rounded-full bg-primary' />
                    <Text
                        className='tracking-wide text-secondary dark:text-white text-text16'
                        style={Global.text_regular}
                    >
                        Connects with Lawcube Audience
                    </Text>
                </View>

                <View className='flex-row items-center space-x-2'>
                    <View className='w-2 h-2 rounded-full bg-primary' />
                    <Text
                        className='tracking-wide text-secondary dark:text-white text-text16'
                        style={Global.text_regular}
                    >
                        Get valued employees
                    </Text>
                </View>

            </View>

            <View className='items-center w-full'>
                <Button
                    title='Create Advocate Profile'
                    onPress={() => navigation.navigate('AdvocateRegistration')}
                    height={55}
                    paddingX={18}
                />
            </View>

        </View>
    )
}

export default AdvocateCard