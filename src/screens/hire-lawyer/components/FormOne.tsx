import React, { useContext, useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import * as yup from 'yup';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import AdvocateProfileCard from './AdvocateProfileCard';
import Global from '../../../../globalStyle';
import { TextInput } from 'react-native-paper';
import { ClientContext } from '../../../context/ClientContext';
import { Feather, FontAwesome } from '@expo/vector-icons';
import { Dropdown, MultiSelect } from 'react-native-element-dropdown';
import { FilterContext } from '../../../context/FilterContext';
import { capitalizeMode } from '../../../helpers/getCapitalize';

interface FormOneProps {
    setAdvocateInfo: (info: any) => void;
    setFormState: (value: number) => void;
    advocateInfo: any;
    profilePhotoImage: any;
    setProfilePhotoImage: (info: any) => void;
    imageUri: string | undefined;
    setImageUri: (uri: string) => void;
}

const FormOne: React.FC<FormOneProps> = ({ setAdvocateInfo, advocateInfo, imageUri, setImageUri, setFormState, profilePhotoImage, setProfilePhotoImage }) => {

    const { colorScheme } = useContext(ClientContext);
    const { lawyerCategory } = useContext(FilterContext);

    const [selectedOccupation, setSelectedOccupation] = useState('');

    const data = [
        { label: 'Individual', value: 'Individual' },
        { label: 'Company', value: 'Company' }
    ];

    const categories = lawyerCategory ? lawyerCategory.map((item: any) => ({
        label: capitalizeMode(item?.global_category_name),
        value: item?._id,
    })) : [];

    useEffect(() => {
        if (advocateInfo?.occupation) {
            setSelectedOccupation(advocateInfo?.occupation);
        }
    }, [advocateInfo?.occupation]);

    const schema = yup.object().shape({
        thumbnail_image_path: yup
            .mixed()
            .required('Profile image is required'),
        name: yup
            .string()
            .required('Name is required')
            .matches(/^[a-zA-Z ]+$/, 'Name must contain only letters')
            .min(3, 'Name must be at least 3 characters')
            .max(50, 'Name must be atmost 50 characters'),
        profession_name: yup
            .string()
            .required('Profession is required')
            .matches(/^[a-zA-Z ]+$/, 'Profession must contain only letters')
            .min(3, 'Profession name must be at least 3 characters')
            .max(50, 'Profession name must be atmost 50 characters'),
        category: yup.
            array()
            .min(1, 'Please select at least one category')
            .required('Category is required'),
        company_name: selectedOccupation === 'Company'
            ? yup
                .string()
                .required('Company Name is required')
                .matches(/^[a-zA-Z. ]+$/, 'Company Name must contain only letters, spaces and dots')
                .min(3, 'Company Name must be at least 3 characters')
                .max(50, 'Company Name must be atmost 50 characters')
            : yup.string().notRequired(),
        company_location: selectedOccupation === 'Company'
            ? yup
                .string()
                .required('Company Location is required')
                .matches(/^[a-zA-Z, -]+$/, 'Company Location must contain only letters')
                .min(3, 'Company Location must be at least 3 characters')
                .max(100, 'Company Location must be atmost 100 characters')
            : yup.string().notRequired(),
        freelancer_type: yup.string().required('Freelancer type is required'),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        clearErrors,
        setError
    } = useForm({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: any) => {
        try {
            if (data.freelancer_type === 'Individual') {
                data.company_name = '';
                data.company_location = '';
            }
            const formData = { ...advocateInfo, ...data, "thumbnail_image_path": profilePhotoImage };
            setAdvocateInfo(formData);
            setFormState(2);

        } catch (error: any) {
            console.log(error.message);
        }
    };

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : 'white',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item?.label}
                </Text>
            </View>
        );
    };

    const multiSelectItem = (item: any, selected: boolean | undefined) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
                className='flex-row items-center justify-between'
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label}
                </Text>
                {selected && <Feather name="check" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
            </View>
        );
    };

    return (
        <View className='pb-4 space-y-2 rounded-md bg-cardLight dark:bg-[#2F2F2F]'>

            <View className='items-center w-full py-4 bg-primary rounded-t-md'>
                <Text style={Global.text_bold} className='text-xl text-secondary'>
                    Freelancer Details (1/3)
                </Text>
            </View>

            <View className='px-2 space-y-4'>

                <View className='space-y-2'>
                    <Controller
                        control={control}
                        name="thumbnail_image_path"
                        defaultValue={imageUri}
                        render={({ field }) => (
                            <AdvocateProfileCard
                                setValue={field.onChange}
                                errors={errors}
                                setError={setError}
                                imageUri={imageUri}
                                setImageUri={setImageUri}
                                setProfilePhotoImage={setProfilePhotoImage}
                            />
                        )}
                    />
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Name'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.name?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="name"
                            defaultValue={advocateInfo?.name}
                        />
                        {errors?.name?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.name?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Profession Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Eg. Senior Advocate'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.profession_name?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="profession_name"
                            defaultValue={advocateInfo?.profession_name}
                        />
                        {errors?.profession_name?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.profession_name?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Category</Text>
                    <View>
                        <Controller
                            control={control}
                            name="category"
                            defaultValue={advocateInfo?.category}
                            render={({ field: { onChange, value } }) => (
                                <MultiSelect
                                    style={[styles.dropdown, { borderColor: errors?.category?.message ? '#ef4444' : '#a8a8a8' }]}
                                    placeholder={`Category ${Array.isArray(value) && value.length > 0 ? `(${value.length})` : ''}`}
                                    placeholderStyle={{
                                        color: Array.isArray(value) && value.length > 0 ? colorScheme === 'dark' ? '#fff' : '#2d2828' : '#a8a8a8',
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    containerStyle={{
                                        borderRadius: 8,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    renderItem={(item, selected) => multiSelectItem(item, selected)}
                                    showsVerticalScrollIndicator
                                    visibleSelectedItem={false}
                                    data={categories}
                                    labelField="label"
                                    valueField="value"
                                    value={value}
                                    onChange={onChange}
                                />
                            )}
                        />
                        {errors?.category?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.category?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>
                        Freelancer Type
                    </Text>
                    <Controller
                        control={control}
                        rules={{ required: 'Freelancer type is required' }}
                        render={({ field: { onChange, value } }) => (
                            <Dropdown
                                style={[
                                    {
                                        backgroundColor: colorScheme === 'dark' ? '' : '',
                                        borderColor: errors?.freelancer_type?.message ? '#ef4444' : '#a8a8a8',
                                        borderWidth: 1,
                                        borderRadius: 8,
                                        paddingHorizontal: 10,
                                        padding: 16,
                                        marginTop: 10,
                                    }
                                ]}
                                selectedTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : 'white'
                                }}
                                data={data}
                                search={false}
                                labelField="label"
                                valueField="value"
                                placeholder="Select"
                                onChange={(item) => {
                                    onChange(item.value);
                                    setSelectedOccupation(item.value);
                                    clearErrors(['company_name', 'company_location']);
                                }}
                                renderItem={renderItem}
                                itemContainerStyle={{
                                    borderBottomWidth: 0.5,
                                    borderBottomColor: '#a8a8a8',
                                    backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : 'white',
                                }}
                                itemTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : 'white',
                                    fontSize: 17
                                }}
                                containerStyle={{
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                    backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : 'white',
                                }}
                                showsVerticalScrollIndicator={false}
                                placeholderStyle={{
                                    color: '#a8a8a8',
                                }}
                                keyboardAvoiding
                                dropdownPosition='top'
                                fontFamily='DMSans-Regular'
                                value={value}
                            />
                        )}
                        name="freelancer_type"
                        defaultValue={advocateInfo?.freelancer_type}
                    />
                    {errors?.freelancer_type?.message && (
                        <Text style={Global.text_medium} className='text-sm text-red-500'>
                            * {errors?.freelancer_type?.message}
                        </Text>
                    )}
                </View>

                {selectedOccupation === 'Company' && (
                    <View className='space-y-2 '>
                        <View className='mb-2 space-y-2'>
                            <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Company Name</Text>
                            <View>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full px-1 tracking-wide bg-transparent text-text17'
                                            placeholder='Company Name'
                                            placeholderTextColor='#a8a8a8'
                                            inputMode='text'
                                            onChangeText={(item) => {
                                                onChange(item);
                                                clearErrors('company_location');
                                            }}
                                            onBlur={onBlur}
                                            value={value || undefined}
                                            error={!!errors?.company_name?.message}
                                            mode='outlined'
                                            maxLength={50}
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="company_name"
                                    defaultValue={advocateInfo?.company_name}
                                />
                                {errors?.company_name?.message && (
                                    <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.company_name?.message}</Text>
                                )}
                            </View>
                        </View>

                        <View className='space-y-2'>
                            <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Company Location</Text>
                            <View>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className={`w-full bg-transparent text-text17 px-1 tracking-wide ${!errors?.company_location?.message && 'dark:border-[1px] dark:border-[#a8a8a850] dark:rounded-lg'}`}
                                            placeholder='Company Location'
                                            placeholderTextColor='#a8a8a8'
                                            inputMode='text'
                                            onChangeText={onChange}
                                            onBlur={onBlur}
                                            value={value || undefined}
                                            error={!!errors?.company_location?.message}
                                            mode='outlined'
                                            maxLength={100}
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="company_location"
                                    defaultValue={advocateInfo?.company_location}
                                />
                                {errors?.company_location?.message && (
                                    <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.company_location?.message}</Text>
                                )}
                            </View>
                        </View>
                    </View>
                )}

                <View className='flex-row items-center justify-around w-full'>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        disabled
                        className='bg-transparent border-[1px] border-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <FontAwesome name='angle-left' size={20} color='#a8a8a8' />
                        <Text style={Global.text_bold} className='text-greycolor text-text16'>Go Back</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={handleSubmit(onSubmit)}
                        className='bg-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <Text style={Global.text_bold} className='text-secondary text-text16'>Continue</Text>
                        <FontAwesome name='angle-right' size={20} color='#000' />
                    </TouchableOpacity>
                </View>

            </View>

        </View>
    );
};

export default FormOne;

const styles = StyleSheet.create({
    dropdown: {
        height: 55,
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    },
    selectedText: {
        color: '#2d2828',
        fontFamily: 'DMSans-SemiBold',
        fontWeight: '700',
    },
    weekDaysContainer: {
        borderBottomColor: '#dedede'
    },
});
