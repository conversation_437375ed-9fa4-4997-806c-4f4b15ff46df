import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Dimensions, Pressable } from 'react-native';
import Global from '../../../../globalStyle';
import Carousel from 'react-native-snap-carousel';
import { AntDesign, Feather } from '@expo/vector-icons';
import { Image } from 'expo-image';

const carouselWidth = Dimensions.get('window').width;
const Width1 = carouselWidth - carouselWidth * 0.15;
const Width2 = carouselWidth - carouselWidth * 0.08;

interface CarouselData {
	name: string;
	profession: string;
	quotes: string;
	image: string;
}

const TestimonialCard = ({ data }: any) => {
	const carouselRef = useRef<Carousel<CarouselData>>(null);
	const [activeSlide, setActiveSlide] = useState(0);

	const handleNext = () => {
		if (activeSlide < data.length - 1) setActiveSlide((prev) => prev + 1);
	};

	const handlePrevious = () => {
		if (activeSlide > 0) setActiveSlide((prev) => prev - 1);
	};

	useEffect(() => {
		carouselRef.current?.snapToItem(activeSlide);
	}, [activeSlide]);

	const renderItem = ({ item }: { item: any }) => {
		return (
			<View className="w-full px-4 py-4 space-y-10 rounded-md bg-primary">
				<Text style={Global.text_regular} className="text-secondary text-text15">
					"{item?.feedback}"
				</Text>
				<View className="flex-row items-center space-x-3">
					<View className='h-[50px] w-[50px] rounded-full overflow-hidden bg-greycolor items-center justify-center'>
						{item?.user_id?.user_profile ?
							<Image
								source={{ uri: item?.user_id?.user_profile }}
								style={{
									width: '100%',
									height: '100%',
									borderRadius: 100
								}}
								contentFit="cover"
							/>
							: <Feather name="user" size={30} color="black" />
						}
					</View>
					<View className="h-[50px] flex-1 flex-col items-start justify-center">
						<Text style={Global.text_bold} className="text-lg text-secondary" numberOfLines={1} ellipsizeMode='tail'>
							{item?.user_id?.first_name} {item?.user_id?.last_name}
						</Text>
						<Text style={Global.text_medium} className="text-white text-text14">
							{item?.user_id?.occupation}
						</Text>
					</View>
				</View>
			</View>
		);
	};

	return (
		<View className="w-full space-y-3">

			<View className="px-primary">
				<Text style={Global.text_bold} className="text-lg text-secondary dark:text-white">
					Client's Feedback
				</Text>
			</View>

			<View className="flex-row items-center w-full">
				{data.length > 1 && (
					<Pressable
						onPress={handlePrevious}
						pressRetentionOffset={40}
						className="h-[45px] w-[45px] rounded-full items-center justify-center absolute left-2 z-20"
						style={{
							backgroundColor: activeSlide !== 0 ? '#2d2828' : '#a8a8a8',
						}}
					>
						<AntDesign name="arrowleft" size={22} color="white" />
					</Pressable>
				)}

				<Carousel
					layout="default"
					ref={carouselRef}
					data={data}
					onSnapToItem={(index) => setActiveSlide(index)}
					sliderWidth={carouselWidth}
					itemWidth={data.length > 1 ? Width1 : Width2}
					renderItem={renderItem}
				/>

				{data.length > 1 && (
					<Pressable
						onPress={handleNext}
						style={{
							backgroundColor: activeSlide < data.length - 1 ? '#2d2828' : '#a8a8a8',
						}}
						pressRetentionOffset={40}
						className="h-[45px] w-[45px] rounded-full items-center justify-center absolute right-2 z-20"
					>
						<AntDesign name="arrowright" size={24} color="#fff" />
					</Pressable>
				)}
			</View>

		</View>
	);
};

export default TestimonialCard;
