import { View, Text, TouchableOpacity } from 'react-native';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Controller, UseFormHandleSubmit } from 'react-hook-form';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { TextInput } from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import Feather from '@expo/vector-icons/Feather';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import { Dropdown } from 'react-native-element-dropdown';
import { StyleSheet } from 'react-native';
import Pdf from 'react-native-pdf'

interface FormProps {
    control: any;
    errors: any;
    handleSubmit: UseFormHandleSubmit<any>;
    onSubmit: (value: any) => void;
    setFormState: (value: number) => void;
    setValue: any;
    barCouncil: string;
    setBarCouncil: (value: string) => void;
    certificateFile: DocumentPicker.DocumentPickerAsset | undefined;
    setCertificateFile: (value: DocumentPicker.DocumentPickerAsset | undefined) => void;
}

const FormThree: React.FC<FormProps> = ({ errors, control, setValue, handleSubmit, onSubmit, setFormState, barCouncil, setBarCouncil, certificateFile, setCertificateFile }) => {

    const { colorScheme } = useContext(ClientContext);

    const handleCertificate = useCallback(async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: ['application/pdf'],
                copyToCacheDirectory: true
            });

            if (!res.canceled && res.assets && res.assets.length > 0) {
                const document = res.assets[0];
                setBarCouncil(document.name);
                setCertificateFile(res.assets[0])
                setValue('bar_council_certificate', document);
            } else {
                setBarCouncil('');
                console.log('DocumentPicker cancelled');
            }
        } catch (err) {
            console.log('Error picking document:', err);
        }
    }, [setValue]);

    const handleRemoveCertificate = () => {
        setValue('bar_council_certificate', null);
        setBarCouncil('');
        setCertificateFile(undefined);
    }

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label} ({item.name})
                </Text>
            </View>
        );
    };

    return (
        <View className='pb-4 space-y-2 rounded-md bg-cardLight dark:bg-[#2F2F2F]'>

            <View className='items-center w-full py-4 bg-primary rounded-t-md'>
                <Text style={Global.text_bold} className='text-xl text-secondary'>
                    Credential Details (3/3)
                </Text>
            </View>

            <View className='px-2 space-y-4'>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Bar Council ID</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Bar Council ID'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.bar_council_id?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="bar_council_id"
                            defaultValue=''
                        />
                        {errors?.bar_council_id?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.bar_council_id?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>

                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Bar Council Certificate</Text>

                    <View className='bg-[#a8a8a860] h-56 w-full rounded-md flex-row justify-center items-center'>
                        {certificateFile?.uri &&
                            <Pdf
                                source={{ uri: certificateFile?.uri }}
                                trustAllCerts={false}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    backgroundColor: 'transparent'
                                }}
                            />}
                    </View>

                    <View className='space-y-3'>

                        {errors?.bar_council_certificate?.message ? (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>
                                * {errors?.bar_council_certificate?.message}
                            </Text>
                        ) : barCouncil ? (
                            <View className='flex-row items-center justify-between w-full px-1'>

                                <View className='flex-row items-center w-full' style={{ maxWidth: '75%' }}>
                                    <Text className='tracking-wider text-text15 text-secondary dark:text-white' style={Global.text_medium}>
                                        (
                                    </Text>
                                    <Text className='tracking-wider text-text15 text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>
                                        {barCouncil}
                                    </Text>
                                    <Text className='tracking-wider text-text15 text-secondary dark:text-white' style={Global.text_medium}>
                                        )
                                    </Text>
                                </View>
                                {barCouncil && (
                                    <TouchableOpacity activeOpacity={0.8} onPress={handleRemoveCertificate} className='flex-row items-center space-x-2'>
                                        <FontAwesome6 name='trash' size={16} color='#ef4444' />
                                        <Text style={Global.text_medium} className='text-red-500 underline'>Remove</Text>
                                    </TouchableOpacity>
                                )}
                            </View>
                        ) : (
                            <Text className='self-center text-xs tracking-wider text-greycolor' style={Global.text_medium}>(Upload PDF only)</Text>
                        )}

                        {barCouncil ? (
                            <View className='items-center'>
                                <View className='bg-[#a8a8a8] h-[50] rounded-full flex-row items-center space-x-2 px-5'>
                                    <Feather name="upload" size={24} color="#000" />
                                    <Text style={Global.text_bold} className='text-secondary text-text16'>Upload File</Text>
                                </View>
                            </View>
                        ) : (
                            <View className='items-center'>
                                <TouchableOpacity
                                    activeOpacity={0.8}
                                    onPress={handleCertificate}
                                    className='bg-primary h-[50] rounded-full flex-row items-center space-x-2 px-5'
                                >
                                    <Feather name="upload" size={24} color="#000" />
                                    <Text style={Global.text_bold} className='text-secondary text-text16'>Upload File</Text>
                                </TouchableOpacity>
                            </View>
                        )}

                    </View>

                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Contact E-mail</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='E-mail'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='email'
                                    autoCapitalize='none'
                                    onChangeText={item =>(
                                        onChange(item.toLowerCase())
                                    )}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.email?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="email"
                            defaultValue=''
                        />
                        {errors?.email?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.email?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Phone Number</Text>
                    <View>
                        <View className='flex-row items-center'>
                            <View className='w-full'>
                                <Controller
                                    control={control}
                                    name="country_code"
                                    defaultValue='91'
                                    render={({ field: { onChange, value } }) => (
                                        <Dropdown
                                            style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: errors.mobile?.message ? "red" : '#a8a8a8', }]}
                                            selectedTextStyle={{
                                                color: colorScheme === 'light' ? '#000' : '#fff',
                                            }}
                                            data={[]}
                                            search
                                            disable
                                            searchPlaceholder='Search Country Code'
                                            inputSearchStyle={{
                                                borderRightWidth: 0,
                                                borderLeftWidth: 0,
                                                borderTopWidth: 0
                                            }}
                                            labelField="label"
                                            valueField="value"
                                            placeholder="(+91)"
                                            renderItem={renderItem}
                                            onChange={item => {
                                                onChange(item.value);
                                            }}
                                            itemContainerStyle={{
                                                borderBottomWidth: 0.5,
                                                borderBottomColor: '#a8a8a8',
                                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                            }}
                                            itemTextStyle={{
                                                color: colorScheme === 'light' ? '#000' : '#fff',
                                                fontSize: 17
                                            }}
                                            containerStyle={{
                                                borderRadius: 8,
                                                overflow: 'hidden',
                                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                            }}
                                            showsVerticalScrollIndicator={false}
                                            placeholderStyle={{
                                                color: colorScheme === 'dark' ? '#fff' : '#2d2828',
                                            }}
                                            keyboardAvoiding
                                            dropdownPosition='auto'
                                            fontFamily='DMSans-Regular'
                                            value={value}
                                        />
                                    )}
                                />
                            </View>
                            <View className='w-[75%] absolute right-0'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full tracking-wider bg-transparent text-text17'
                                            placeholder='Phone Number'
                                            placeholderTextColor='#a8a8a8'
                                            inputMode='numeric'
                                            maxLength={10}
                                            onChangeText={onChange}
                                            onBlur={onBlur}
                                            value={value}
                                            error={!!errors?.mobile?.message}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderTopWidth: 1.2,
                                                borderBottomWidth: 1.2,
                                                borderRightWidth: 1.2,
                                                borderLeftWidth: 0,
                                                borderTopRightRadius: 8,
                                                borderBottomRightRadius: 8,
                                                borderTopLeftRadius: 0,
                                                borderBottomLeftRadius: 0,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="mobile"
                                    defaultValue=''
                                />
                            </View>
                        </View>
                        {errors?.mobile?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.mobile?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='flex-row items-center justify-around w-full'>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => setFormState(2)}
                        className='bg-transparent border-[0.7px] border-secondary dark:border-white h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <FontAwesome name='angle-left' size={20} color={colorScheme === 'dark' ? "#fff" : '#000'} />
                        <Text style={Global.text_bold} className='text-secondary dark:text-white text-text16'>Go Back</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={handleSubmit(onSubmit)}
                        className='bg-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <Text style={Global.text_bold} className='text-secondary text-text16'>Submit Details</Text>
                        <FontAwesome name='angle-right' size={20} color='#000' />
                    </TouchableOpacity>
                </View>

            </View>

        </View>
    );
}

const styles = StyleSheet.create({
    dropdown: {
        height: 56,
        borderColor: '#a8a8a8',
        borderLeftWidth: 1.2,
        borderTopWidth: 1.2,
        borderBottomWidth: 1.2,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: '75%',
        overflow: 'hidden',
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default FormThree;