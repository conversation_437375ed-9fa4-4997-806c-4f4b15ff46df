import React, { useContext } from 'react';
import { View, Text } from 'react-native';
import Global from '../../../../globalStyle';
import Button from '../../../components/Button';
import { ClientContext } from '../../../context/ClientContext';
import { TouchableOpacity } from 'react-native';
import { Image } from 'expo-image';
import { StarRatingDisplay } from 'react-native-star-rating-widget';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import Badge from '../../../components/Badge';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';

const LawyerCard = ({ data, showConfirmation }: any) => {

    const { navigation, colorScheme, isLarge, clientId, userData, setShowAdvocateCard } = useContext(ClientContext);

    const feedbackSubmited = async (id: string) => {
        try {
            const response = await ClientAxiosInstance.get(`/freelancerfeedback/freelancer/${clientId}/${id}`);
            return response?.data?.success;

        } catch (error: any) {
            console.log("Lawyer feedback submitted error : ", error.response.data.message);
            return false;
        }
    }

    const handleLawyerDetails = async () => {
        const feedbackSubmit = await feedbackSubmited(data?._id);
        const isContacted = userData?.contacted_freelancers?.includes(data?._id) || false;
        navigation.navigate('LawyerDetails', { data: data, isContacted: isContacted, feedbackSubmited: feedbackSubmit });
    };

    return (
        <TouchableOpacity activeOpacity={0.8} onPress={showConfirmation ? undefined : handleLawyerDetails} className='bg-cardLight dark:bg-[#2F2F2F] shadow-sm shadow-gray-300 dark:shadow-none p-2 rounded-md space-y-2'>

            <View>
                <Image
                    source={data?.thumbnail_image_path ?
                        { uri: data?.thumbnail_image_path } :
                        require('../../../assets/images/profile.png')
                    }
                    style={{
                        width: '100%',
                        height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50),
                        borderRadius: 5,
                    }}
                    contentFit="cover"
                />
            </View>

            <View className='space-y-3'>

                <View><Badge height={30} name='Freelancer' /></View>

                <View>
                    <Text
                        style={Global.text_bold}
                        numberOfLines={2}
                        ellipsizeMode='tail'
                        className='text-lg text-secondary dark:text-white'
                    >
                        {data?.freelancer_name || data?.name}
                    </Text>
                </View>

                <View>
                    <Text
                        style={Global.text_regular}
                        className='text-text14 text-secondary2 dark:text-white'
                    >
                        {capitalizeMode(data?.profession_name)}
                    </Text>
                </View>

                <View className='flex-row items-center w-full'>
                    <Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white'>Bar Council Number</Text>
                    <Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
                    <Text style={Global.text_medium} numberOfLines={1} className='w-[50%] text-text14 sm:text-ellipsis text-secondary dark:text-white'>{data?.barcouncil_id || data?.bar_council_id}</Text>
                </View>

                <View className='flex-row items-center w-full'>
                    <Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white'>Area Of Practice</Text>
                    <Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
                    <Text style={Global.text_medium} numberOfLines={data?.area_of_practice ? 2 : undefined} ellipsizeMode="tail" className='w-[50%] text-text14 text-secondary dark:text-white'>{data?.area_of_practice || data?.area_practice}</Text>
                </View>

                <View className='flex-row items-center w-full'>
                    <Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white'>Years Of Experience</Text>
                    <Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
                    <Text style={Global.text_medium} className='w-[50%] text-text14 text-secondary dark:text-white'>{data?.years_of_experience} Years</Text>
                </View>

                {!showConfirmation && data?.average_rating && (
                    <View className='flex-row items-center w-full space-x-2'>
                        <Text style={Global.text_bold} className='text-secondary dark:text-white'>Client Rating</Text>
                        <Text style={Global.text_bold} className='text-secondary dark:text-white'>:</Text>
                        <View className='flex-row items-center'>
                            <Text style={Global.text_regular} className='text-secondary dark:text-white'>({data?.average_rating?.toFixed(1) || '0'})</Text>
                            <StarRatingDisplay
                                rating={data?.average_rating}
                                maxStars={5}
                                starSize={18}
                                color='#fdd066'
                                emptyColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
                                style={{ padding: 0 }}
                            />
                        </View>
                    </View>
                )}

                {showConfirmation && (
                    <View>
                        <Button
                            title='View more Lawyers'
                            onPress={() => {navigation.navigate('HireLawyerHome')
                                setShowAdvocateCard(false);
                            }}
                        />
                    </View>
                )}

            </View>

        </TouchableOpacity>
    );
};

export default LawyerCard;
