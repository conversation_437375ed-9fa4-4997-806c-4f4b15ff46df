import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import Feather from '@expo/vector-icons/Feather';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';
import { FontAwesome6 } from '@expo/vector-icons';
import { Image } from 'expo-image';

interface AdvocateProfileCardProps {
    setValue: (name: string, value: any) => void;
    errors: any;
    imageUri: string | undefined;
    setImageUri: (uri: string) => void;
    setProfilePhotoImage: (info: any) => void;
    setError: any;
}

const AdvocateProfileCard: React.FC<AdvocateProfileCardProps> = ({ setValue, errors, setError, imageUri, setImageUri, setProfilePhotoImage }) => {

    const { colorScheme } = useContext(ClientContext);

    const handleChangeProfilePicture = async () => {
        try {
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (!permissionResult.granted) {
                Alert.alert('Permission','Permission to access camera roll is required!');
                return;
            }

            const imageResult = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [3, 2],
                quality: 1,
            });

            if (!imageResult.canceled && imageResult.assets.length > 0) {
                const selectedImage = imageResult.assets[0];
                if (selectedImage.fileSize && selectedImage.fileSize < 1048576) {
                    setError('thumbnail_image_path', {
                        type: 'manual',
                        message: ''
                    });
                    setValue('thumbnail_image_path', selectedImage);
                    setProfilePhotoImage(selectedImage);
                    setImageUri(selectedImage.uri);
                } else {
                    setError('thumbnail_image_path', {
                        type: 'manual',
                        message: 'The selected image file size must be less than 1MB.'
                    });
                }
            }

        } catch (error) {
            console.log('Error updating profile image:', error);
        }
    };

    const handleRemoveProfilePicture = () => {
        setImageUri('');
        setValue('thumbnail_image_path', '');
    };

    return (
        <View className='space-y-3'>

            <Text className='tracking-wide text-text17 text-secondary dark:text-white' style={Global.text_bold}>Profile Photo</Text>

            <View className='items-center w-full'>
                <View className='h-52 w-full rounded-md bg-[#a8a8a850] overflow-hidden self-center items-center justify-center'>
                    {imageUri ? (
                        <Image
                            source={{ uri: imageUri }}
                            style={{ height: '100%', width: '100%' }}
                            contentFit='cover'
                        />
                    ) : (
                        <Image
                            source={require('../../../assets/images/profile.png')}
                            style={{ height: '100%', width: '100%' }}
                            contentFit='cover'
                        />
                    )}
                </View>
            </View>

            {imageUri ? (
                <View className='flex-row items-center justify-between w-full px-1'>
                    <Text className='tracking-wider text-text15 text-secondary dark:text-white' style={Global.text_medium} numberOfLines={1} ellipsizeMode='tail'>
                        (Profile image selected)
                    </Text>
                    <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-2' onPress={handleRemoveProfilePicture}>
                        <FontAwesome6 name='trash' size={16} color='#ef4444' />
                        <Text style={Global.text_medium} className='text-red-500 underline'>Remove</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <View className='items-center w-full'>
                    <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-2' onPress={handleChangeProfilePicture}>
                        <Feather name='upload' size={20} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                        <Text className='underline text-secondary dark:text-white text-text16' style={Global.text_bold}>Upload Image</Text>
                    </TouchableOpacity>
                </View>
            )}

            {errors?.thumbnail_image_path?.message && (
                <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.thumbnail_image_path?.message}</Text>
            )}
        </View>
    );
};

export default AdvocateProfileCard;
