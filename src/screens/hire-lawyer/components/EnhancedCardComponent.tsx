import React, {useContext } from 'react';
import { View, Text,StyleSheet } from 'react-native';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';
import Button from '../../../components/Button';
import { StarRatingDisplay } from 'react-native-star-rating-widget';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { Image } from 'expo-image';

const EnhancedCardComponent = ({ data, isContacted }: any) => {

	const { navigation, colorScheme, userData } = useContext(ClientContext);

	const handleEditLawyerProfile = () => {
		navigation.navigate('EditAdvocateProfile', { formState: 1, lawyerDetail: data });
	}

	return (
		<View style={styles.card} className='bg-cardLight dark:bg-[#2F2F2F] pt-2 pb-3 space-y-4'>

			<View className='px-2'>
				<Image source={data?.thumbnail_image_path ?
					{ uri: data?.thumbnail_image_path } :
					require('../../../assets/images/profile.png')
				} style={styles.image} />
			</View>

			<View className='px-2'>

				<View className='mb-3 space-y-2'>
					<Text style={[Global.text_bold]} className='text-lg text-secondary dark:text-white'>{data?.freelancer_name}</Text>
					<Text style={[Global.text_regular]} className='text-secondary dark:text-white text-text14'>{capitalizeMode(data?.profession_name)}</Text>
				</View>

				<View className='mb-6 space-y-4'>

					<View className='flex-row items-center'>
						<Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white tracking-wide pr-1' numberOfLines={2} ellipsizeMode='tail'>Bar Council Number</Text>
						<Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
						<Text style={[styles.value, Global.text_medium]} className='w-[50%] text-text14 text-secondary dark:text-white tracking-wide'>{data?.barcouncil_id}</Text>
					</View>

					<View className='flex-row items-start'>
						<Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white tracking-wide pr-1' numberOfLines={2} ellipsizeMode='tail'>Area Of Practice</Text>
						<Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
						<Text style={[styles.value, Global.text_medium]} className='w-[50%] text-text14 text-secondary dark:text-white tracking-wide'>{data?.area_of_practice}</Text>
					</View>

					<View className='flex-row items-center'>
						<Text style={Global.text_bold} className='w-[45%] text-text14 text-secondary dark:text-white tracking-wide pr-1' numberOfLines={2} ellipsizeMode='tail'>Years Of Experience</Text>
						<Text style={Global.text_bold} className='w-[5%] text-text14 text-secondary dark:text-white'>:</Text>
						<Text style={[styles.value, Global.text_medium]} className='w-[50%] text-text14 text-secondary dark:text-white tracking-wide'>{data?.years_of_experience} Years</Text>
					</View>

					{data?.average_rating && <View className='flex-row items-center space-x-2'>
						<Text style={Global.text_bold} className='text-text14 text-secondary dark:text-white tracking-wide pr-1' numberOfLines={2} ellipsizeMode='tail'>Client Rating</Text>
						<Text style={Global.text_bold} className='text-text14 text-secondary dark:text-white'>:</Text>
						<View className='flex-row items-center'>
							<Text style={Global.text_regular} className='text-secondary dark:text-white text-text14 mr-1 tracking-wide'>({data?.average_rating?.toFixed(1) || '0'})</Text>
							<StarRatingDisplay
								rating={data?.average_rating}
								maxStars={5}
								starSize={18}
								color='#fdd066'
								emptyColor={colorScheme === 'dark' ? '#fff' : '#2d2828'}
								style={{ padding: 0 }}
							/>
						</View>
					</View>}

				</View>

				<Text style={[Global.text_bold_italic]} className='mb-5 text-lg text-center text-secondary dark:text-white'>"{data?.profile_quotes?.trim()}"</Text>

				{userData.freelancer_id !== data._id ?
					<View className='items-center w-full mb-3'>
						<Button
							title={isContacted ? 'Contact Again' : 'Contact Me'}
							height={48}
							paddingX={14}
							onPress={() => navigation.navigate('HireLawyerRegistration', { freelancer_id: data?._id })}
						/>
					</View>
					:
					<View className='items-center w-full mb-3'>
						<Button
							title='Edit Profile'
							height={48}
							paddingX={14}
							onPress={handleEditLawyerProfile}
						/>
					</View>
				}

			</View>

		</View>
	);
};

const styles = StyleSheet.create({
	card: {
		borderRadius: 10,
		shadowColor: '#000',
		shadowOpacity: 0.1,
		shadowOffset: { width: 0, height: 2 },
		shadowRadius: 4,
		elevation: 3,
		overflow: 'hidden',
	},
	image: {
		width: '100%',
		height: widthPercentageToDP(50),
		borderRadius: 5,
	},
	freelancerTag: {
		backgroundColor: '#f0f0f0',
		borderRadius: 4,
		padding: 4,
		alignSelf: 'flex-start',
		marginBottom: 8,
	},
	value: {
		flex: 1,
	},
	rating: {
		flexDirection: 'row',
		marginLeft: 8,
	},
});

export default EnhancedCardComponent;
