import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext, useEffect } from 'react'

import * as yup from 'yup';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Global from '../../../../globalStyle';
import { TextInput } from 'react-native-paper';
import { ClientContext } from '../../../context/ClientContext';
import { FontAwesome } from '@expo/vector-icons';
import { Dropdown } from 'react-native-element-dropdown';

interface EditProfileTwoProps {
    setFormState: (value: number) => void;
    editDetails: any;
    newDetails: any;
    setNewDetails: (val: any) => void;
}

const EditProfileTwo: React.FC<EditProfileTwoProps> = ({ setFormState, editDetails, newDetails, setNewDetails }) => {

    const { colorScheme } = useContext(ClientContext);

    const schema = yup.object().shape({
        description: yup
            .string()
            .required('Description is required')
            .min(100, 'Description must be at least 100 characters')
            .max(250, 'Description must be atmost 250 characters'),
        profile_quotes: yup
            .string()
            .required('Quotes is required')
            .min(100, 'Quotes must be at least 100 characters')
            .max(200, 'Quotes must be atmost 250 characters'),
        area_of_practice: yup
            .string()
            .required('Area of Practice is required')
            .min(3, 'Area of Practice must be at least 3 characters')
            .max(150, 'Area of Practice must be atmost 150 characters'),
        years_of_experience: yup
            .string()
            .required('Experience is required')
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue
    } = useForm({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        setValue("description", newDetails?.description || editDetails?.description || '');
        setValue("profile_quotes", newDetails?.profile_quotes || editDetails?.profile_quotes || '');
        setValue("area_of_practice", newDetails?.area_of_practice || editDetails?.area_of_practice || '');
        setValue("years_of_experience", newDetails?.years_of_experience || editDetails?.years_of_experience || '');
    }, [newDetails, editDetails, setValue]);

    const onSubmit = async (data: any) => {
        setNewDetails({
            ...newDetails,
            ...data,
        });
        setFormState(3);
    };


    const expData = [
        {
            label: '0-1',
            value: '0-1'
        },
        {
            label: '1-3',
            value: '1-3'
        },
        {
            label: '3-5',
            value: '3-5'
        },
        {
            label: '5-8',
            value: '5-8'
        },
        {
            label: '8-10',
            value: '8-10'
        },
        {
            label: '10+',
            value: '10+'
        }
    ];

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : 'white',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item?.label}
                </Text>
            </View>
        );
    };

    return (
        <View className='pb-4 space-y-2 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='items-center w-full py-4 bg-primary rounded-t-md'>
                <Text style={Global.text_bold}
                    className='text-xl text-secondary'>
                    About you (2/3)
                </Text>
            </View>

            <View className='px-2 space-y-4'>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Your Description</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Description'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.description?.message}
                                    mode='outlined'
                                    style={{
                                        textAlignVertical: 'top',
                                        minHeight: 100,
                                    }}
                                    maxLength={250}
                                    multiline
                                    textAlignVertical='top'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="description"
                            defaultValue={editDetails?.description}
                        />
                        {errors?.description?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.description?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Your Quotes / Tag Line</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Tagline'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.profile_quotes?.message}
                                    mode='outlined'
                                    style={{
                                        textAlignVertical: 'top',
                                        minHeight: 100,
                                    }}
                                    maxLength={200}
                                    multiline
                                    textAlignVertical='top'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="profile_quotes"
                            defaultValue={editDetails?.profile_quotes}
                        />
                        {errors?.profile_quotes?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.profile_quotes?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Area of Practice</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Area of Practice'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.area_of_practice?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="area_of_practice"
                            defaultValue={editDetails?.area_of_practice}
                        />
                        {errors?.area_of_practice?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.area_of_practice?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Years of Experience</Text>
                    <View>
                        <Controller
                            control={control}
                            rules={{ required: 'Year of experience' }}
                            render={({ field: { onChange, value } }) => (
                                <Dropdown
                                    data={expData}
                                    style={{
                                        height: 55,
                                        borderWidth: 1,
                                        borderRadius: 8,
                                        paddingLeft: 16,
                                        paddingRight: 8,
                                        overflow: 'hidden',
                                        backgroundColor: 'transparent',
                                        borderColor: errors?.years_of_experience?.message ? 'red' : '#a8a8a8'
                                    }}
                                    renderItem={renderItem}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Years of Experience"
                                    placeholderStyle={{
                                        color: '#a8a8a8',
                                        fontFamily: 'DMSans-Regular',
                                    }}
                                    selectedTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : '#fff',
                                        fontFamily: 'DMSans-Regular',
                                    }}
                                    itemContainerStyle={{
                                        borderBottomWidth: 0.5,
                                        borderBottomColor: '#a8a8a8',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    itemTextStyle={{
                                        color: colorScheme === 'dark' ? '#fff' : '#000',
                                        fontSize: 17
                                    }}
                                    containerStyle={{
                                        borderRadius: 8,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    showsVerticalScrollIndicator={false}
                                    keyboardAvoiding
                                    dropdownPosition='top'
                                    value={value}
                                    onChange={(item) => onChange(item.value)}
                                />
                            )}
                            name="years_of_experience"
                            defaultValue={editDetails?.years_of_experience}
                        />
                        {errors?.years_of_experience?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.years_of_experience?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='flex-row items-center justify-around w-full'>

                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => setFormState(1)}
                        className='bg-transparent border-[0.7px] border-secondary dark:border-white h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <FontAwesome name='angle-left' size={20} color={colorScheme === 'dark' ? "#fff" : '#000'} />
                        <Text style={Global.text_bold} className='text-secondary dark:text-white text-text16'>Go Back</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={handleSubmit(onSubmit)}
                        className='bg-primary h-[53] rounded-full flex-row items-center space-x-2 px-5'
                    >
                        <Text style={Global.text_bold} className='text-secondary text-text16'>Continue</Text>
                        <FontAwesome name='angle-right' size={20} color='#000' />
                    </TouchableOpacity>

                </View>

            </View>

        </View>
    )
}

export default EditProfileTwo

const styles = StyleSheet.create({
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    },
});