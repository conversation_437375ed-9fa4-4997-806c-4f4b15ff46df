import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RefreshControl } from "react-native";
import React, { use<PERSON><PERSON>back, useContext, useEffect, useRef, useState } from "react";
import CustomStatusBar from "../../../components/CustomStatusBar";
import Header from "../../../components/Header";
import CourseTracker from "../components/booked-course/CourseTracker";
import CourseSections from "../components/booked-course/CourseSections";
import BottomSheet from "@gorhom/bottom-sheet";
import ContentSlider from "../components/booked-course/ContentSlider";
import Description from "../components/booked-course/Description";
import Player from "../components/booked-course/Player";
import { ClientContext } from "../../../context/ClientContext";
import { setStatusBarHidden } from "expo-status-bar";
import { ClientAxiosInstance } from "../../../lib/axiosInstance";
import * as ScreenOrientation from "expo-screen-orientation";
import Global from "../../../../globalStyle";
import FeedbackModal from "../../../components/FeedbackModel";
import { useFocusEffect } from "@react-navigation/native";
import QuizSection from "../components/booked-course/QuizSection";
import CertificateSection from "../components/booked-course/CertificateSection";
import { widthPercentageToDP } from "react-native-responsive-screen";
import { Skeleton } from "@rneui/themed";
import { LinearGradient } from "expo-linear-gradient";
import { setLectureFinished } from "../../../helpers/courseFinished";

const BookedCourse = ({ route }: any) => {

	const { navigation, clientId, isLarge } = useContext(ClientContext);
	const currentCourse = route?.params?.currentCourse;

	const scrollviewRef = useRef<ScrollView>(null);

	const [isFeedbackModalVisible, setIsFeedbackModalVisible] = useState(false);

	const courseSectionRef = useRef<BottomSheet>(null);
	const [courseSectionOpen, setCourseSectionOpen] = useState(false);

	const [isCourseFinished, setIsCourseFinished] = useState(false);

	const [inFullscreen, setInFullscreen] = useState(false);

	const [currentLecture, setCurrentLecture] = useState<any>(null);
	const [lectureVideoQuizTime, setLectureVideoQuizTime] = useState<any>();

	const [lectureIndex, setLectureIndex] = useState(0);

	const [totalLectures, setTotalLectures] = useState<any[]>([]);
	const [courseProgress, setCourseProgress] = useState<string[]>([]);

	const [refreshing, setRefreshing] = useState(false);

	const onRefresh = useCallback(() => {
		setRefreshing(true);
		setTimeout(() => {
			setRefreshing(false);
		}, 2000);
	}, []);

	const fetchBookedCourse = async () => {
		try {
			let progress = [];
			progress = await getCourseProgress();

			const totalLectures = currentCourse?.topics?.flatMap((topic: any) => {
				return topic?.lectures?.map((lecture: any) => ({
					topic_id: topic?._id,
					course_id: currentCourse?._id,
					...lecture,
					isFinished: progress.includes(lecture?._id),
				}));
			});
			setTotalLectures(totalLectures);

			const first_lecture = totalLectures.find((lecture: any) => !lecture.isFinished);
			setIsCourseFinished(!first_lecture)
			const first_lecture_index = totalLectures.findIndex((lecture: any) => !lecture.isFinished);

			setLectureIndex(first_lecture_index);
			setCurrentLecture(first_lecture);

		} catch (error) {
			console.log("Error fetching booked course: ", error);
		}
	};

	const getCourseProgress = async () => {
		try {
			const response = await ClientAxiosInstance.get(`/courseprogress/progress/${clientId}/${currentCourse?._id}`);
			const reponseData = response.data.data;

			if (reponseData?.current_lecture) setLectureVideoQuizTime(reponseData?.current_lecture || {});

			if (reponseData?.completed_lectures?.length) {
				const progress = reponseData?.completed_lectures?.map((item: any) => item?.lecture_id);
				setCourseProgress(progress);
				return progress;
			} else {
				setCourseProgress([]);
				return [];
			}

		} catch (error: any) {
			console.log("Course Progress Error : ", error.response);
			return [];
		}
	}

	const toggleCourseSection = () => {
		if (courseSectionOpen) {
			setCourseSectionOpen(false);
			courseSectionRef.current?.snapToIndex(0);
		} else {
			setCourseSectionOpen(true);
			courseSectionRef.current?.snapToIndex(1);
		}
	};

	const handleNext = async (topic_id: string, lecture_id: string) => {

		const nextLectureIndex = lectureIndex + 1;

		if (nextLectureIndex < totalLectures.length) {
			setLectureIndex(nextLectureIndex);
			setCurrentLecture(totalLectures[nextLectureIndex]);
		} else {
			setIsCourseFinished(true);
			setCurrentLecture(null);
		}
		await setLectureFinished(topic_id, lecture_id, currentCourse?._id, clientId ?? '');
		await getCourseProgress();
		scrollviewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
	};

	const [courseQuizAnswers, setCourseQuizAnswers] = useState<any>();

	const getCourseQuizResult = async (id: string) => {
		try {
			const response = await ClientAxiosInstance.get(`/coursequiz/attended/${id}`);
			const responseData = response.data.data;
			setCourseQuizAnswers(responseData);

		} catch (error) {
			console.log("Get course quiz result error : ", error);
		}
	}

	useEffect(() => {
		const backAction = () => {
			if (inFullscreen) {
				setStatusBarHidden(false, "fade");
				setInFullscreen(false);
				ScreenOrientation.lockAsync(
					ScreenOrientation.OrientationLock.PORTRAIT_UP
				);
				return true;
			} else {
				navigation.navigate("Courses");
				return true;
			}
		};

		const backHandler = BackHandler.addEventListener(
			"hardwareBackPress",
			backAction
		);

		return () => backHandler.remove();
	}, [inFullscreen, navigation]);


	useEffect(() => {
		fetchBookedCourse();
	}, [refreshing]);

	useFocusEffect(
		React.useCallback(() => {
			getCourseProgress();
		}, [])
	);

	return (
		<View className="flex-1 bg-white dark:bg-dark">

			{!inFullscreen && <CustomStatusBar />}

			{!inFullscreen && (
				<View>
					<Header search name="Courses" index={-1} />
				</View>
			)}

			<ScrollView
				showsVerticalScrollIndicator={false}
				scrollEnabled={!inFullscreen}
				className="flex-1"
				refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
				ref={scrollviewRef}
			>
				<View className={`space-y-5 pt-2 ${inFullscreen ? 'bg-dark pb-0' : 'bg-transparent pb-[90px]'}`}>

					{!inFullscreen && (
						<View className="px-primary">
							<Text
								className="text-lg tracking-wider text-secondary dark:text-white"
								style={Global.text_bold}
							>
								{currentCourse?.title}
							</Text>
						</View>
					)}

					{(!currentLecture && isCourseFinished) ? (
						<View>
							<CertificateSection
								currentCourse={currentCourse}
								setIsFeedbackModalVisible={setIsFeedbackModalVisible}
								isCourseFinished={isCourseFinished}
							/>
						</View>
					) : currentLecture?.item_type === "video" ? (
						<View>
							<Player
								inFullscreen={inFullscreen}
								setInFullscreen={setInFullscreen}
								handleNext={handleNext}
								currentLecture={currentLecture}
								lectureVideoQuizTime={lectureVideoQuizTime}
								courseProgress={courseProgress}
							/>
						</View>
					) : currentLecture?.item_type === "text" ? (
						<View className="px-primary">
							<ContentSlider
								currentLecture={currentLecture}
								handleNext={handleNext}
							/>
						</View>
					) : currentLecture?.item_type === "quiz" ? (
						<View>
							<QuizSection
								currentLecture={currentLecture}
								totalLectures={totalLectures}
								currentCourse={currentCourse}
								handleNext={handleNext}
								courseProgress={courseProgress}
								courseQuizAnswers={courseQuizAnswers}
								lectureVideoQuizTime={lectureVideoQuizTime}
								getCourseQuizResult={getCourseQuizResult}
							/>
						</View>
					) : (
						<View className="px-primary" style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
							<Skeleton LinearGradientComponent={LinearGradient} animation='wave'
								style={{
									width: '100%',
									height: '100%',
									borderRadius: 5
								}}
							/>
						</View>
					)}

					{!inFullscreen && (
						<View className="px-primary">
							<CourseTracker courseProgress={courseProgress} currentCourse={currentCourse} />
						</View>
					)}

					{!inFullscreen && (
						<View className="px-primary">
							<Description currentCourse={currentCourse} />
						</View>
					)}

				</View>

			</ScrollView>

			{!inFullscreen && (
				<CourseSections
					ref={courseSectionRef}
					toggleCourseSection={toggleCourseSection}
					courseSectionOpen={courseSectionOpen}
					currentCourse={currentCourse}
					setCourseSectionOpen={setCourseSectionOpen}
					courseProgress={courseProgress}
					setCurrentLecture={setCurrentLecture}
					currentLecture={currentLecture}
					setLectureIndex={setLectureIndex}
					totalLectures={totalLectures}
					getCourseQuizResult={getCourseQuizResult}
				/>
			)}

			<FeedbackModal
				visible={isFeedbackModalVisible}
				onClose={() => setIsFeedbackModalVisible(false)}
				id={currentCourse?._id}
				type="course"
				title={currentCourse?.title}
			/>

		</View>
	);
};

export default BookedCourse;
