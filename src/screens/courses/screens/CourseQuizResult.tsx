import { <PERSON><PERSON><PERSON><PERSON>, Text, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import { ClientContext } from '../../../context/ClientContext';
import Button from '../../../components/Button';
import Global from '../../../../globalStyle';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { Image } from 'expo-image';

const CourseQuizResult = ({ route }: any) => {

    const { navigation } = useContext(ClientContext);

    const currentCourse = route?.params?.currentCourse || {};
    const courseQuizId = route.params?.courseQuizId || '';

    const [score, setScore] = useState(0);

    const [courseQuizAnswers, setCourseQuizAnswers] = useState<any>();

    const handleQuizAnswers = () => {
        navigation.navigate('CourseQuizAnswers', { courseQuizAnswers: courseQuizAnswers });
    }

    const getCourseQuizResult = async () => {
        try {
            const response = await ClientAxiosInstance.get(`/coursequiz/attended/${courseQuizId}`);
            setScore(response.data.data.percentage_score);
            setCourseQuizAnswers(response.data.data);

        } catch (error) {
            console.log("Get course quiz result error : ", error);
        }
    }

    useEffect(() => {
        getCourseQuizResult();
    }, [])

    const backAction = () => {
        navigation.navigate('BookedCourse', { currentCourse: currentCourse });
    };

    useEffect(() => {
        const backAction = () => {
            navigation.navigate('BookedCourse', { currentCourse: currentCourse });
            return true;
        };
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
        return () => backHandler.remove();
    }, []);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Quiz' index={-1} handleNavigation={backAction} />

            <View className='h-[80%] w-full px-primary justify-center'>

                <View className='w-full flex-col items-center bg-cardLight dark:bg-secondary pb-10 rounded-md'>

                    <Text
                        style={Global.text_bold}
                        className='w-full py-6 text-secondary bg-primary rounded-t-md tracking-wide text-xl text-center'
                    >
                        Quiz has ended
                    </Text>

                    <Image
                        source={require('../../../assets/images/crown.png')}
                        style={{ height: 180, width: '100%' }}
                        contentFit='cover'
                    />

                    <Text
                        style={Global.text_medium}
                        className='w-3/4 self-center py-5 text-secondary dark:text-white tracking-wide text-center text-lg'
                    >
                        You have scored <Text style={Global.text_bold}>
                            ({Number(score) % 1 === 0
                                ? Number(score)
                                : score?.toFixed(2)}/100)
                        </Text>
                    </Text>

                    <View className='items-center'>
                        <Button
                            title='View answers'
                            onPress={handleQuizAnswers}
                        />
                    </View>

                </View>

            </View>

        </View>
    )
}

export default CourseQuizResult