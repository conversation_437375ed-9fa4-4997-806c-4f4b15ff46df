import { View, Text, ScrollView } from 'react-native';
import React from 'react';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import Global from '../../../../globalStyle';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Entypo } from '@expo/vector-icons';
import AnswerOption from '../components/quiz/QuizAnswerOption';
import { Image } from 'expo-image';

const CourseQuizAnswers = ({ route }: any) => {

    const { courseQuizAnswers } = route?.params || {};
    const userAnswers = courseQuizAnswers?.user_answers || [];

    return (
        <View className="flex-1 bg-white dark:bg-dark">

            <CustomStatusBar />

            <Header search name="Quiz Answers" index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className="flex-1 space-y-3">

                <View className="w-full h-full px-primary space-y-3 mt-3 mb-8">

                    <View className="bg-primary py-4 items-center rounded-md space-y-4">
                        <Text className="text-secondary text-xl" style={Global.text_bold}>Quiz Details</Text>
                        <Text className="text-secondary text-lg" style={Global.text_bold}>Your total score in this quiz</Text>
                        <Text className="text-secondary text-xl" style={Global.text_bold}>
                            ({Number(courseQuizAnswers?.percentage_score) % 1 === 0
                                ? Number(courseQuizAnswers?.percentage_score)
                                : courseQuizAnswers?.percentage_score?.toFixed(2)}/100
                            )
                        </Text>
                    </View>

                    {userAnswers.map((item: any, index: number) => {

                        const { question, description, question_image_path, all_answers } = item;
                        const noAnswer = !item.userAnswer
                        const isUserCorrect = item.userAnswer === item.correctAnswer;

                        return (
                            <Animated.View
                                entering={FadeInUp.delay(100 * index).duration(1000).springify()}
                                className="w-full bg-cardLight dark:bg-secondary px-3 py-3 space-y-3 rounded-md"
                                key={index}
                            >
                                <View className="px-2 py-3 space-y-4">
                                    <View className="w-full flex-row items-start justify-between">
                                        <Text style={Global.text_medium} className="w-[10%] text-text16 text-secondary dark:text-white tracking-wide">
                                            {index + 1}.
                                        </Text>
                                        <View className="space-y-2 w-[90%]">
                                            <Text style={Global.text_regular} className="text-text16 text-secondary dark:text-white tracking-wide">
                                                {question}
                                            </Text>
                                            {description && (
                                                <Text style={Global.text_medium} className="text-text16 text-secondary dark:text-white tracking-wide">
                                                    {description}
                                                </Text>
                                            )}
                                        </View>
                                    </View>

                                    {question_image_path && (
                                        <View className="h-[180px] w-full">
                                            <Image
                                                source={{ uri: question_image_path }}
                                                style={{ width: '100%', height: '100%', borderRadius: 5 }}
                                                contentFit="cover"
                                            />
                                        </View>
                                    )}
                                </View>

                                {all_answers.map((option: any, optionIndex: number) => {
                                    const isCorrectAnswer = option.correct;
                                    const isUserAnswer = item.userAnswer === option.text;
                                    return (
                                        <View key={optionIndex}>
                                            <AnswerOption
                                                isFillups={all_answers.length === 1}
                                                option={option}
                                                isCorrectAnswer={isCorrectAnswer}
                                                isUserAnswer={isUserAnswer}
                                                noAnswer={noAnswer}
                                                isUserCorrect={isUserCorrect}
                                                userAnswer={item.userAnswer}
                                            />
                                        </View>
                                    )
                                })}

                                <View className='w-full flex-row items-center space-x-2'>

                                    <View className={`h-6 w-6 rounded-full items-center justify-center ${isUserCorrect ? 'bg-green-600' : 'bg-red-500'}`}>
                                        <Entypo name={isUserCorrect ? 'check' : 'cross'} size={18} color='white' />
                                    </View>

                                    <Text style={Global.text_medium} className="text-text15 text-secondary dark:text-white tracking-wide w-[90%]">
                                        Answer is {isUserCorrect ? 'correct' : 'wrong'} ({item.correctAnswer})
                                    </Text>

                                </View>

                            </Animated.View>
                        );
                    })}

                </View>

            </ScrollView>

        </View>
    );
};

export default CourseQuizAnswers;
