import { View, Text, ScrollView } from 'react-native'
import React, { useContext, useRef, useState } from 'react'

import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import Header from '../../../components/Header';
import CustomStatusBar from '../../../components/CustomStatusBar';

import RegisterPrice from '../components/others/RegisterPrice';
import { StarRatingDisplay } from 'react-native-star-rating-widget';
import BottomSheet from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheet/BottomSheet';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import CourseSections from '../components/others/CourseSections';
import { Image } from 'expo-image';
import Feedbacks from '../../../components/Feedbacks';

const CourseDetails = ({ route }: any) => {

    const { colorScheme, currentCourseChip } = useContext(ClientContext);

    const courseSectionRef = useRef<BottomSheet>(null);
    const [courseSectionOpen, setCourseSectionOpen] = useState(false);

    const [feedback, setFeedback] = useState<any[]>([]);
    const [feedbackLoading, setFeedbackLoading] = useState(true);

    const toggleCourseSection = () => {
        if (courseSectionOpen) {
            setCourseSectionOpen(false);
            courseSectionRef.current?.snapToIndex(0);
        } else {
            setCourseSectionOpen(true);
            courseSectionRef.current?.snapToIndex(1);
        }
    };

    const currentCourse = route?.params?.currentCourse || {};

    const getTotalDuration = (item: any[]) => {
        let total_mins = 0;
        item.map((topic: any) => (
            total_mins += topic?.total_lecture_duration
        ))
        return `${total_mins === 60 ? '1 hr' : total_mins < 60 ? `${total_mins} mins` : `${total_mins % 60} hrs`}`;
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Courses' index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='w-full h-full py-3 space-y-5 mb-20'>

                    <View style={{ height: widthPercentageToDP(50) }} className='px-primary'>
                        <Image
                            source={currentCourse?.thumbnail_image_path ?
                                { uri: currentCourse?.thumbnail_image_path } :
                                require('../../../assets/images/placeholder-thumbnail.png')
                            }
                            style={{
                                width: '100%',
                                height: '100%',
                                borderRadius: 5
                            }}
                        />
                    </View>

                    <View className='px-primary'>

                        <Text style={Global.text_bold} className='mb-3 text-text19 text-secondary dark:text-white'>Course Details</Text>

                        <View className='min-h-[60px] w-full flex-row items-center justify-between py-2'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Course Name</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={2}
                                    ellipsizeMode='tail'
                                >
                                    {currentCourse?.title}
                                </Text>
                            </View>
                        </View>

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        <View className='h-[60px] w-full flex-row items-center justify-between'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Total Hours</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={1}
                                    ellipsizeMode='tail'
                                >
                                    {getTotalDuration(currentCourse.topics)}
                                </Text>
                            </View>
                        </View>

                        <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                        <View className='min-h-[60px] w-full flex-row items-center justify-between py-2'>
                            <View className='w-2/5'>
                                <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Tutored by</Text>
                            </View>
                            <View className='items-end w-3/5'>
                                <Text
                                    style={Global.text_regular}
                                    className='tracking-wider text-text17 text-secondary dark:text-white'
                                    numberOfLines={1}
                                    ellipsizeMode='tail'
                                >
                                    {currentCourse?.tutor_name}
                                </Text>
                            </View>
                        </View>

                        {currentCourse?.average_rating > 0 &&
                            <>
                                <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

                                <View className='h-[60px] w-full flex-row items-center justify-between'>
                                    <View className='w-2/5'>
                                        <Text style={Global.text_medium} className='tracking-wide text-text17 text-secondary dark:text-white'>Rating</Text>
                                    </View>
                                    <View className='flex-row items-center justify-end w-3/5 space-x-2'>
                                        <Text
                                            style={Global.text_regular}
                                            className='tracking-wider text-text17 text-secondary dark:text-white'
                                            numberOfLines={1}
                                            ellipsizeMode='tail'
                                        >
                                            ({currentCourse?.average_rating?.toFixed(1)})
                                        </Text>
                                        <StarRatingDisplay
                                            rating={currentCourse?.average_rating}
                                            maxStars={5}
                                            starSize={20}
                                            color='#fdd066'
                                            emptyColor={colorScheme === 'dark' ? '#fff' : '#64748b'}
                                            style={{ padding: 0 }}
                                        />
                                    </View>
                                </View>
                            </>
                        }

                    </View>

                    <View className='px-primary'>
                        <RegisterPrice
                            currentCourse={currentCourse}
                            selectedChip={currentCourseChip}
                            setFeedback={setFeedback}
                            setFeedbackLoading={setFeedbackLoading}
                        />
                    </View>

                    <View className='w-full'>
                        <Feedbacks
                           data={feedback.reverse()}
                            selectedChip='Course'
                            loading={feedbackLoading}
                        />
                    </View>

                </View>

            </ScrollView>

            <CourseSections
                ref={courseSectionRef}
                currentCourse={currentCourse}
                toggleCourseSection={toggleCourseSection}
                courseSectionOpen={courseSectionOpen}
                setCourseSectionOpen={setCourseSectionOpen}
            />

        </View>
    )
}

export default CourseDetails