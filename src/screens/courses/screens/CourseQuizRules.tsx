import { View, ScrollView } from 'react-native'
import React, { useContext } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import Button from '../../../components/Button';
import RulesRegulations from '../components/quiz/RulesRegulations';
import { ClientContext } from '../../../context/ClientContext';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';


const CourseQuizRules = ({ route }: any) => {

    const courseQuiz = route?.params?.courseQuiz || {};
    const currentCourse = route?.params?.currentCourse || {};
    const currentLecture = route?.params?.currentLecture || {};
    const lectureVideoQuizTime = route?.params?.lectureVideoQuizTime || {};
    const totalLectures = route?.params?.totalLectures || [];

    const { navigation, clientId } = useContext(ClientContext);

    const handleUpdateCurrentLectureProgress = async () => {
        try {
            if (currentLecture._id === lectureVideoQuizTime.lecture_id) {
                navigation.navigate('CourseQuiz', {
                    courseQuiz: courseQuiz,
                    currentCourse: currentCourse,
                    totalLectures: totalLectures,
                    lectureVideoQuizTime: lectureVideoQuizTime
                });
            }
            else {
                const response = await ClientAxiosInstance.put(`/courseprogress/update-current-lecture`, {
                    user_id: clientId,
                    course_id: currentLecture?.course_id,
                    topic_id: currentLecture?.topic_id,
                    lecture_id: currentLecture?._id,
                    video_played_duration: 0,
                    started_at: new Date().toISOString(),
                });
                console.log("Video progress update : ", response.data);

                if (response.data.success)
                    navigation.navigate('CourseQuiz', {
                        courseQuiz: courseQuiz,
                        currentCourse: currentCourse,
                        totalLectures: totalLectures,
                        lectureVideoQuizTime: response.data.current_lecture
                    });
            }

        } catch (err) {
            console.log("Video progress update error : ", err);
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Quiz' index={-1} />

            <View className='h-[80%] w-full px-primary justify-center space-y-5'>

                <View><RulesRegulations /></View>

                <View className='w-full items-center'>
                    <Button title='Start Evaluation Test' onPress={handleUpdateCurrentLectureProgress} />
                </View>

            </View>

        </View>
    )
}

export default CourseQuizRules