import { View, Text, ScrollView, TouchableOpacity, AppState, BackHandler } from 'react-native';
import React, { useContext, useEffect, useRef, useState } from 'react';
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import QuestionCard from '../components/quiz/QuestionCard';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Global from '../../../../globalStyle';
import { FormProvider, useForm } from 'react-hook-form';
import Button from '../../../components/Button';
import QuizWarning from '../components/quiz/QuizWarning';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { ClientContext } from '../../../context/ClientContext';
import { Toast } from '../../../components/Toast';
import { formatDuration } from '../../../helpers/dateTimeFormat';
import { GlobalContext } from '../../../context/GlobalProvider';
import AppMinimize from '../components/quiz/AppMinimize';
import { Image } from 'expo-image';
import { setLectureFinished } from '../../../helpers/courseFinished';

const CourseQuiz = ({ route }: any) => {

    // usePreventScreenCapture();

    const courseQuiz = route?.params?.courseQuiz || {};
    const currentCourse = route?.params?.currentCourse || {};
    const totalLectures = route?.params?.totalLectures || [];
    const lectureVideoQuizTime = route?.params?.lectureVideoQuizTime || {};

    const [currentLecture, setCurrentLecture] = useState<any>();

    const { navigation, clientId } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const [questionIndex, setQuestionIndex] = useState(0);

    const [totalDuration, setTotalDuration] = useState(0);
    const [totalDurationTaken, setTotalDurationTaken] = useState(0);

    const appState = useRef(AppState.currentState);
    const [showAppConfirmation, setShowAppConfirmation] = useState(false);
    const [appStateVisible, setAppStateVisible] = useState(0);

    const [showConfirmation, setShowConfirmation] = useState(false);
    const methods = useForm();

    const getTotalDuration = () => {
        const totalDurationMilliseconds = (courseQuiz?.total_duration ?? 0) * 60 * 1000;
        const startedTime = new Date(lectureVideoQuizTime.started_at);
        const remainingTime = totalDurationMilliseconds - Math.round((new Date().getTime() - startedTime.getTime()));
        const remainingDuration = remainingTime < 0 ? 0 : remainingTime;
        setTotalDuration(Math.round(remainingDuration / 1000));
    };

    const getCurrentLecture = () => {
        const lecture = totalLectures.find((lecture: any) => (
            lecture.quiz_id === courseQuiz?._id
        ));
        setCurrentLecture(lecture);
    }

    const onSubmit = async (data: any) => {

        const formattedData = Object.keys(data).map(questionId => ({
            questionId: questionId,
            selectedAnswer: data[questionId]?.toString()?.trim(),
        }));

        try {
            setLoading(true);

            const response = await ClientAxiosInstance.post(`/coursequiz/attend/${currentCourse?._id}/${courseQuiz?._id}`, {
                answers: formattedData,
                quiz_attend_time_duration: formatDuration(totalDurationTaken)
            });

            await setLectureFinished(
                currentLecture?.topic_id,
                currentLecture?._id,
                currentLecture?.course_id,
                clientId ?? ''
            );

            setLoading(false);

            navigation.navigate('CourseQuizResult', { courseQuizId: courseQuiz?._id, currentCourse: currentCourse });

            Toast.show({
                type: 'success',
                message: 'Quiz submitted successfully',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/images/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

        } catch (error) {
            console.log("Error while submitting course quiz: ", error);
            setLoading(false);
        }
    };

    const handleNextQuestion = () => {
        if (courseQuiz?.questions?.length > questionIndex + 1) {
            setQuestionIndex(prevIndex => prevIndex + 1);
        }
    };

    const handlePreviousQuestion = () => {
        if (questionIndex > 0) {
            setQuestionIndex(prevIndex => prevIndex - 1);
        }
    };

    useEffect(() => {
        const interval = setInterval(() => {
            setTotalDuration(prevDuration => {
                if (prevDuration <= 1) {
                    console.log(prevDuration)
                    clearInterval(interval);
                    onSubmit(methods.getValues());
                    return 0;
                }
                return prevDuration - 1;
            });
            setTotalDurationTaken(prevDuration => prevDuration + 1);
        }, 1000);

        return () => clearInterval(interval);

    }, [totalDuration, methods]);

    useEffect(() => {
        const subscription = AppState.addEventListener('change', nextAppState => {
            if (
                appState.current.match(/inactive|background/) &&
                nextAppState === 'active'
            ) {
                console.log('App is in foreground');
            }
            else if (nextAppState.match(/inactive|background/)) {
                setTimeout(() => {
                    setAppStateVisible((prev) => {
                        const updated = prev + 1;
                        if (updated === 1) setShowAppConfirmation(true);
                        else if (updated === 2) onSubmit(methods.getValues());
                        return updated;
                    });
                }, 0);
            }
            appState.current = nextAppState;
        });
        return () => {
            subscription.remove();
        };
    }, []);

    useEffect(() => {
        const backAction = () => {
            if (questionIndex > 0 && questionIndex < courseQuiz?.questions?.length) {
                handlePreviousQuestion();
                return true;
            }
            else if (questionIndex === 0) {
                setShowConfirmation(true);
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [questionIndex]);

    const handleClose = () => {
        setShowConfirmation(false);
    }

    useEffect(() => {
        getTotalDuration();
        getCurrentLecture();
    }, []);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header name='Quiz' index={-1} setShowConfirmation={setShowConfirmation} menu={false} />

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <FormProvider {...methods}>

                    <View className='h-full w-full px-primary py-3'>

                        <QuestionCard
                            key={courseQuiz?.questions[questionIndex]?._id}
                            questionIndex={questionIndex}
                            courseQuiz={courseQuiz}
                            totalDuration={totalDuration}
                            currentQuestion={courseQuiz?.questions[questionIndex]}
                        />

                        <View className='w-full bg-cardLight dark:bg-secondary rounded-b-md flex-row items-center justify-center space-x-4 py-8'>
                            <TouchableOpacity
                                onPress={handlePreviousQuestion}
                                activeOpacity={0.8}
                                className={`h-[42px] w-[42px] rounded-full pl-2 items-center justify-center ${questionIndex > 0 ? 'bg-primary' : 'border border-[#aaa]'}`}
                            >
                                <MaterialIcons name='arrow-back-ios' size={20} color={questionIndex > 0 ? "#000" : '#aaa'} />
                            </TouchableOpacity>

                            <Text className={`text-xl text-secondary dark:text-white text-center tracking-widest`} style={Global.text_medium}>
                                {questionIndex + 1 < 10 ? `0${questionIndex + 1}` : questionIndex + 1} / {courseQuiz?.questions?.length < 10 ? `0${courseQuiz?.questions?.length}` : courseQuiz?.questions?.length}
                            </Text>

                            <TouchableOpacity
                                onPress={handleNextQuestion}
                                activeOpacity={0.8}
                                className={`h-[42px] w-[42px] rounded-full items-center justify-center ${courseQuiz?.questions?.length > questionIndex + 1 ? 'bg-primary' : 'border border-[#aaa]'}`}
                            >
                                <MaterialIcons name='arrow-forward-ios' size={20} color={courseQuiz?.questions?.length > questionIndex + 1 ? "#000" : '#aaa'} />
                            </TouchableOpacity>
                        </View>

                        {courseQuiz?.questions?.length === questionIndex + 1 && (
                            <View className="w-full flex-row justify-center py-4">
                                <Button title="Submit" height={53} onPress={methods.handleSubmit(onSubmit)} />
                            </View>
                        )}

                    </View>

                </FormProvider>

            </ScrollView>

            <QuizWarning
                handleClose={handleClose}
                showConfirmation={showConfirmation}
                onSubmit={onSubmit}
                handleSubmit={methods.handleSubmit}
            />

            <AppMinimize
                handleAppClose={() => setShowAppConfirmation(false)}
                showAppConfirmation={showAppConfirmation}
            />

        </View>
    );
};

export default CourseQuiz;
