import { View, Text, ScrollView, StatusBar, BackHandler } from 'react-native'
import React, { useContext, useEffect } from 'react'
import { Entypo } from '@expo/vector-icons'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import DetailCard from '../components/others/DetailCard'

const CoursePaymentConfirmation = ({ route }: any) => {

    const currentCourse = route.params?.currentCourse || {};
    const { colorScheme, navigation, isAndroid } = useContext(ClientContext);

    useEffect(() => {
        const backAction = () => {
            navigation.navigate("Courses");
            return true;
        };

        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            backAction
        );

        return () => backHandler.remove();
    }, []);

    const STATUSBAR_HEIGHT = isAndroid ? StatusBar.currentHeight : 0;

    return (
        <View className='flex-1 bg-white dark:bg-dark' style={{ paddingTop: STATUSBAR_HEIGHT }}>

            <StatusBar
                barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'}
                translucent={true}
                animated={true}
                backgroundColor='transparent'
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexGrow: 1,
                    justifyContent: 'center'
                }}
            >
                <View className='px-primary space-y-2 items-center justify-center py-5'>

                    <View className='w-full flex-col items-center space-y-2'>

                        <View className='h-10 w-10 bg-[#2baa16] rounded-full items-center justify-center'>
                            <Entypo name='check' size={25} color='white' />
                        </View>

                        <View className='w-[70%]'>
                            <Text
                                className='text-lg text-secondary dark:text-white text-center'
                                style={Global.text_bold}
                            >
                                Thank you for joining in this course
                            </Text>
                        </View>

                        <Text className='text-text16 text-secondary dark:text-white' style={Global.text_regular}>Please do share this with your friends</Text>

                    </View>

                    <View className='w-full'>
                        <DetailCard showConfirmation confirmationData={currentCourse} />
                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default CoursePaymentConfirmation