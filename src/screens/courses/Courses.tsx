import { View, Text, ScrollView, Dimensions, RefreshControl } from 'react-native'
import React, { useContext, useRef } from 'react'
import CustomStatusBar from '../../components/CustomStatusBar'
import Header from '../../components/Header'
import CourseChip from './components/others/CourseChip'
import Filters from '../../components/Filters'
import Global from '../../../globalStyle'
import DetailCard from './components/others/DetailCard'
import Pagination from './components/others/Pagination'
import { CourseContext } from '../../context/CourseContext'
import { ClientContext } from '../../context/ClientContext'
import Loader from './components/others/Loader'

const Courses = () => {

    const { showPagination, loading, selectedChipData, onRefresh, refreshing } = useContext(CourseContext);
    const { currentCourseChip, isAndroid, isLarge, isMedium } = useContext(ClientContext);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    };

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Courses' index={1} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className='flex-1'
                ref={scrollViewRef}
                refreshControl={<RefreshControl refreshing={refreshing} tintColor='#fdd066' onRefresh={onRefresh} />}
                stickyHeaderIndices={[0]}
            >

                <View className='pt-3 pb-4 bg-cardLight dark:bg-secondary'>
                    <CourseChip handlePaginationChange={handlePaginationChange} />
                </View>

                <View className='bg-ececec'>
                    <Filters
                        chipFrom={currentCourseChip}
                        categoryFilter
                        priceFilter
                        courseSort
                    />
                </View>

                <View className={`space-y-3 ${isMedium ? 'mb-28' : isAndroid ? 'mb-20' : 'mb-24'}`}>

                    <View className='w-full pt-4 pb-2 space-y-4 px-primary'>

                        <Text
                            className='text-text20 text-secondary dark:text-white'
                            style={Global.text_bold}
                        >
                            List Of Courses
                        </Text>

                        <View className='space-y-[3%]'>
                            {loading ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {[1, 2, 3, 4, 5, 6].map((index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <Loader />
                                            </View>
                                        ))}
                                    </View>
                                ) : (
                                    [1, 2, 3,].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))
                                )
                            ) : selectedChipData?.length > 0 ? (
                                isLarge ? (
                                    <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                        {selectedChipData?.map((item, index) => (
                                            <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                                <DetailCard data={item} />
                                            </View>
                                        ))}
                                    </View>
                                ) :
                                    selectedChipData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <DetailCard data={item} />
                                        </View>
                                    ))
                            ) : (
                                <View className='flex-row items-center justify-center w-full h-12'>
                                    <Text className='text-text17 text-secondary dark:text-white' style={Global.text_medium}>
                                        No course is available
                                    </Text>
                                </View>
                            )}
                        </View>

                    </View>

                    {showPagination && <View className='px-1'>
                        <Pagination onPageChange={handlePaginationChange} />
                    </View>}

                </View>

            </ScrollView>

        </View>
    )
}

export default Courses