import { View, Text } from 'react-native'
import React from 'react'
import Global from '../../../../../globalStyle'

const RulesRegulations = () => {
    return (
        <View className='w-full'>

            <View className='bg-primary py-5 rounded-t-md'>
                <Text className='text-secondary text-xl text-center' style={Global.text_bold}>Quiz Rules and Regulations</Text>
            </View>

            <View className='bg-cardLight dark:bg-secondary rounded-b-md space-y-5 py-10 px-primary'>

                <View className='flex-row items-start space-x-1 mr-8'>
                    <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>1. </Text>
                    <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                        Have to complete this quiz to earn your certificate.
                    </Text>
                </View>

                <View className='flex-row items-start space-x-1 mr-8'>
                    <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>2. </Text>
                    <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                        Quiz answer will be submitted after the time ends.
                    </Text>
                </View>

                <View className='flex-row items-start space-x-1 mr-8'>
                    <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>3. </Text>
                    <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                        You cannot re-take this quiz.
                    </Text>
                </View>

                <View className='flex-row items-start space-x-1 mr-8'>
                    <Text className='text-secondary dark:text-white text-text16' style={Global.text_bold}>4. </Text>
                    <Text className='text-text16 text-secondary dark:text-white tracking-wider' style={Global.text_regular}>
                        Quiz score will appear on the certificate.
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default RulesRegulations