import { View, Text, TouchableOpacity } from 'react-native';
import React from 'react';
import Global from '../../../../../globalStyle';

interface QuizOptionsProps {
    onPress: () => void;
    title: string;
    height?: number;
    color?: string;
    bgColor?: string;
    borderColor?: string;
    borderWidth?: number;
    selected?: boolean;
}

const QuizOptions = React.forwardRef<any, QuizOptionsProps>((props, ref) => {

    const { onPress, title, height, color, bgColor, borderColor, borderWidth, selected } = props;

    return (
        <TouchableOpacity
            ref={ref}
            style={{
                minHeight: height || 57,
                backgroundColor: selected ? 'gray' : bgColor || 'transparent',
                borderColor: borderColor || '#a8a8a8',
                borderWidth: borderWidth || 0.7,
            }}
            onPress={onPress}
            activeOpacity={0.8}
            className="rounded-full items-center justify-center w-full px-3 py-1 mb-3"
        >
            <Text
                className="text-text15 tracking-wide text-center"
                style={[
                    Global.text_regular,
                    {
                        color: selected ? 'white' : color || '#2d2828',
                    },
                ]}
            >
                {title}
            </Text>
        </TouchableOpacity>
    );
});

export default QuizOptions;
