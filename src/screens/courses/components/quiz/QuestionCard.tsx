import { View, Text } from 'react-native';
import React, { useContext} from 'react';
import Global from '../../../../../globalStyle';
import Feather from '@expo/vector-icons/Feather';
import { ClientContext } from '../../../../context/ClientContext';
import QuizOptions from '../quiz/QuizOptions';
import { Controller, useFormContext } from 'react-hook-form';
import QuizInput from './QuizInput';
import { Image } from 'expo-image';

interface QuestionCardProps {
    courseQuiz: any;
    questionIndex: number;
    totalDuration: number;
    currentQuestion:any;
}

const QuestionCard: React.FC<QuestionCardProps> = ({ courseQuiz, questionIndex, totalDuration, currentQuestion }) => {

    const { colorScheme } = useContext(ClientContext);
    const { control, watch } = useFormContext();

    const questionName = currentQuestion?._id;

    const selectedAnswer = watch(questionName);
    
    const handleDurationFormat = () => {
        const minutes = Math.floor(totalDuration / 60);
        const seconds = totalDuration % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    return (
        <View className="w-full bg-cardLight dark:bg-secondary rounded-t-md">

            <View className="items-center bg-primary rounded-t-md py-3 space-y-3 px-3">

                <Text style={Global.text_bold} className="text-lg text-secondary">
                    {courseQuiz?.title}
                </Text>

                <View className="flex-row items-center justify-center space-x-2 bg-dark dark:bg-white h-10 px-3 rounded-full">
                    <Feather name="clock" size={20} color={colorScheme === 'dark' ? 'black' : 'white'} />
                    <Text className="text-text14 text-white dark:text-secondary tracking-wide" style={Global.text_medium}>
                        {handleDurationFormat()} Mins Left
                    </Text>
                </View>

            </View>

            {currentQuestion && (

                <Controller
                    control={control}
                    name={questionName}
                    render={({ field: { onChange } }) => (

                        <View className="px-3 py-3 space-y-4">

                            <View className="w-full flex-row items-start justify-between">

                                <Text style={Global.text_medium} className="w-[10%] text-text15 text-secondary dark:text-white tracking-wide">
                                    {questionIndex + 1}.
                                </Text>

                                <View className="space-y-2 w-[90%]">
                                    <Text style={Global.text_regular} className="text-text15 text-secondary dark:text-white tracking-wide">
                                        {currentQuestion?.question}
                                    </Text>
                                    {currentQuestion?.description && <Text style={Global.text_medium} className="text-text15 text-secondary dark:text-white tracking-wide">
                                        {currentQuestion?.description}
                                    </Text>}
                                </View>

                            </View>

                            {currentQuestion?.question_image_path && <View className='h-[180px] w-full'>
                                <Image
                                    source={{ uri: currentQuestion?.question_image_path }}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        borderRadius: 5
                                    }}
                                />
                            </View>}

                            {currentQuestion?.type === 'Text' ? (
                                <View>
                                    <QuizInput control={control} questionName={questionName} />
                                </View>
                            ) : (
                                <View className="space-y-5">
                                    {currentQuestion?.answers?.map((answer: any, index: number) => (
                                        <QuizOptions
                                            key={index}
                                            title={answer.text}
                                            height={57}
                                            onPress={() => onChange(answer.text)}
                                            color={colorScheme === 'dark' ? 'white' : 'black'}
                                            bgColor={colorScheme === 'dark' ? 'transparent' : 'white'}
                                            selected={selectedAnswer === answer.text}
                                        />
                                    ))}
                                </View>
                            )}

                        </View>
                    )}
                />

            )}

        </View>
    );
};

export default QuestionCard;
