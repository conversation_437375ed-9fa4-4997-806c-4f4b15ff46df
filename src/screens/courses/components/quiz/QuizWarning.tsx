import { View, Text, TouchableOpacity, Modal } from 'react-native'
import React from 'react'
import Global from '../../../../../globalStyle'

interface QuizWarningProps {
    showConfirmation: boolean;
    onSubmit: any;
    handleSubmit: any;
    handleClose: () => void;
}

const QuizWarning: React.FC<QuizWarningProps> = ({ showConfirmation, handleClose, onSubmit, handleSubmit }) => {
    return (
        <Modal animationType='fade' statusBarTranslucent transparent={true} visible={showConfirmation} onRequestClose={handleClose}>

            <View className='items-center justify-center w-full h-full bg-dark/70 dark:bg-dark/80'>

                <View className='w-3/4 px-5 py-6 space-y-5 rounded-md bg-cardLight dark:bg-secondary'>

                    <Text
                        className='tracking-wide text-center text-secondary dark:text-white text-text16'
                        style={Global.text_bold}
                    >
                        Are you sure, you want to exit and submit the quiz ?
                    </Text>

                    <View className='flex-row items-center w-full justify-evenly'>

                        <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={handleSubmit(onSubmit)}
                            className='px-4 py-2 rounded-full bg-primary'
                        >
                            <Text className='text-text16 text-secondary' style={Global.text_bold}>Submit</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={handleClose}
                            className='bg-transparent border border-[#aaa] rounded-full px-4 py-2'
                        >
                            <Text className='text-text16 text-secondary dark:text-white' style={Global.text_medium}>Cancel</Text>
                        </TouchableOpacity>

                    </View>

                </View>

            </View>

        </Modal>
    )
}

export default QuizWarning