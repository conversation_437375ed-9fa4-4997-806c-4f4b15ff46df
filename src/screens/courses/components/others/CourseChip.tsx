import { View, Text, FlatList, Dimensions } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { ClientContext } from '../../../../context/ClientContext';
import Global from '../../../../../globalStyle';
import { CourseContext } from '../../../../context/CourseContext';
import BeginnerSelectedIcon from '../../../../assets/icons/course-icon/BeginnerSelectedIcon';
import { BeginnerIconLight, BeginnerIcon } from '../../../../assets/icons/course-icon/BeginnerIcon';
import { IntermediateIconLight, IntermediateIcon } from '../../../../assets/icons/course-icon/IntermediateIcon';
import IntermediateSelectedIcon from '../../../../assets/icons/course-icon/IntermediateSelectedIcon';
import { AdvancedIconLight, AdvancedIcon } from '../../../../assets/icons/course-icon/AdvancedIcon';
import AdvancedSelectedIcon from '../../../../assets/icons/course-icon/AdvancedSelectedIcon';
import { TouchableOpacity } from 'react-native-gesture-handler';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface CourseChipProps {
    handlePaginationChange: () => void;
}

const CourseChip: React.FC<CourseChipProps> = ({ handlePaginationChange }) => {

    const { setCurrentCourseChip, colorScheme } = useContext(ClientContext);
    const { selectedChip, setSelectedChip } = useContext(CourseContext);

    const defaultData = [
        {
            id: 1,
            title: 'Beginner',
            icon: selectedChip === 'Beginner'
                ? colorScheme === 'dark' ? <BeginnerIcon /> : <BeginnerSelectedIcon />
                : colorScheme === 'dark' ? <BeginnerIconLight /> : <BeginnerIcon />,
        },
        {
            id: 2,
            title: 'Intermediate',
            icon: selectedChip === 'Intermediate'
                ? colorScheme === 'dark' ? <IntermediateIcon /> : <IntermediateSelectedIcon />
                : colorScheme === 'dark' ? <IntermediateIconLight /> : <IntermediateIcon />,
        },
        {
            id: 3,
            title: 'Advanced',
            icon: selectedChip === 'Advanced'
                ? colorScheme === 'dark' ? <AdvancedIcon /> : <AdvancedSelectedIcon />
                : colorScheme === 'dark' ? <AdvancedIconLight /> : <AdvancedIcon />,
        }
    ]

    const [data, setData] = useState(defaultData);
    const flatListRef = useRef<FlatList>(null);

    useEffect(() => {
        if (selectedChip) {
            const selectedData = defaultData.find(item => item.title === selectedChip);
            const newData = defaultData.filter(item => item.title !== selectedChip);
            if (selectedData) {
                setData([selectedData, ...newData]);
            }
        } else {
            setData(defaultData);
        }
    }, [selectedChip]);

    const handleChipSelection = (chipId: number) => {
        setSelectedChip(defaultData[chipId - 1]?.title);
        setCurrentCourseChip(defaultData[chipId - 1]?.title);
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
        handlePaginationChange();
    }

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => handleChipSelection(item.id)}
                    style={{
                        borderRadius: 25,
                        backgroundColor: selectedChip === item.title ? colorScheme === 'dark' ? '#fdd066' : '#2d2828' : 'transparent',
                        borderWidth: 0.8,
                        borderColor: selectedChip === item.title ? "transparent" : colorScheme === 'dark' ? "#fff" : "#2d2828"
                    }}
                    className='h-10 flex-row items-center justify-center space-x-2 px-[10px]'
                >
                    <View>
                        {item.icon}
                    </View>
                    <Text
                        style={Global.text_bold}
                        className={`${selectedChip === item.title ? 'text-primary dark:text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14`}
                    >
                        {item.title}
                    </Text>
                </TouchableOpacity>
            </View>
        )
    };

    return (
        <View className='w-full'>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
}

export default CourseChip