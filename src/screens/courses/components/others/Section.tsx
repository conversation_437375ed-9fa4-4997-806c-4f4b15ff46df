import React, { useContext } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { <PERSON><PERSON><PERSON>, Feather, FontAwesome6, Ionicons, Octicons } from '@expo/vector-icons';
import Global from '../../../../../globalStyle';
import { ClientContext } from '../../../../context/ClientContext';
import AutoScroll from "@homielab/react-native-auto-scroll";

interface SectionProps {
    topic: any;
    isOpen: boolean;
    onToggle: () => void;
    currentCourseLength: number;
    currentCourseTopicIndex: number;
}

const Section: React.FC<SectionProps> = ({ topic, isOpen, onToggle, currentCourseLength, currentCourseTopicIndex }) => {

    const { colorScheme } = useContext(ClientContext);

    return (
        <View>

            <TouchableOpacity
                activeOpacity={0.8}
                className={`w-full h-14 px-primary flex-row items-center justify-between ${isOpen ? 'bg-primary' : 'bg-transparent'}`}
                onPress={onToggle}
            >

                <View className="flex-row items-center flex-[0.5]">
                    <Ionicons
                        name={isOpen ? 'chevron-up-outline' : 'chevron-down-outline'}
                        size={20}
                        color={isOpen ? '#000' : colorScheme === 'dark' ? '#a8a8a8' : '#000'}
                    />
                    <View className='flex-1 mx-1'>
                        {isOpen ? <AutoScroll endPaddingWidth={0}>
                            <Text
                                style={Global.text_bold}
                                className={`${isOpen ? 'text-secondary' : 'text-greycolor'} text-text15 tracking-wide`}
                            >
                                {topic?.title}
                            </Text>
                        </AutoScroll> :
                            <Text
                                style={Global.text_bold}
                                className={`${isOpen ? 'text-secondary' : 'text-greycolor'} text-text15 tracking-wide`}
                                numberOfLines={1}
                                ellipsizeMode='tail'
                            >
                                {topic?.title}
                            </Text>
                        }
                    </View>
                </View>

                <View className="h-full flex-row items-center justify-end flex-[0.5]">
                    <Text
                        style={Global.text_bold}
                        className={`${isOpen ? 'text-secondary' : 'text-greycolor'} text-text14 tracking-wide`}
                    >
                        {topic?.lectures?.length} Lectures
                    </Text>
                    <View className={`h-1 w-1 rounded-full mx-2 ${isOpen ? 'bg-black' : 'bg-greycolor'}`} />
                    <Text
                        style={Global.text_bold}
                        className={`${isOpen ? 'text-secondary' : 'text-greycolor'} text-text14 tracking-wider`}
                    >
                        {topic?.total_lecture_duration === 60
                            ? '1 hr'
                            : topic?.total_lecture_duration < 60
                                ? `${topic?.total_lecture_duration} mins`
                                : `${Math.floor(topic?.total_lecture_duration / 60)} hrs ${topic?.total_lecture_duration % 60} mins`}
                    </Text>
                </View>

            </TouchableOpacity>

            {isOpen && (
                <View className='mt-3'>
                    <View style={{ marginBottom: currentCourseLength === currentCourseTopicIndex + 1 ? 0 : 12 }}>
                        {topic?.lectures?.map((lecture: any, index: number) => (
                            <View className='flex-row items-start w-full' key={index}>

                                <View className='flex-col items-center w-[15%]'>
                                    <View className='h-[30] w-[30] rounded-full bg-transparent items-center justify-center border-[1.5px] border-greycolor'>
                                        {lecture?.item_type !== 'quiz' ? (
                                            <Entypo name="check" size={18} color='#a8a8a8' />
                                        ) : (
                                            <FontAwesome6 name="question" size={16} color='#a8a8a8' />
                                        )}
                                    </View>
                                    {topic?.lectures?.length !== index + 1 && <View className='h-8 w-[1.5px] bg-greycolor' />}
                                </View>

                                <View className='w-[80%] mt-[5px]'>
                                    <Text className='text-greycolor text-text15 tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_bold}>
                                        {lecture?.item_type !== 'quiz' ? lecture?.title : 'Take a small quiz'}
                                    </Text>
                                </View>

                            </View>
                        ))}
                    </View>
                    <View>
                        {currentCourseLength === currentCourseTopicIndex + 1 &&
                            <View className='flex-row items-end w-full mb-3'>

                                <View className='flex-col items-center w-[15%]'>
                                    <View className={`h-8 w-[1.5px] bg-greycolor`} />
                                    <View className={`h-[30] w-[30] rounded-full bg-transparent items-center justify-center border-[1.5px] border-greycolor`}>
                                        <Octicons name="download" size={20} color="#a8a8a8" />
                                    </View>
                                </View>

                                <View className='w-[80%] mb-2 flex-row items-center'>
                                    <Text className={`text-text15 tracking-wide text-greycolor`} style={Global.text_bold}>
                                        Download Certificate
                                    </Text>
                                </View>

                            </View>
                        }
                        <View className='px-primary'>
                            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                        </View>

                    </View>
                </View>
            )}
        </View>
    );
};

export default Section;
