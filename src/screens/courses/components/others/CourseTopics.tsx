import React, { useMemo, useCallback, forwardRef, useContext, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { Entypo, FontAwesome6, Ionicons } from '@expo/vector-icons';
import { ClientContext } from '../../../../context/ClientContext';
import Global from '../../../../../globalStyle';

interface CourseSectionProps {
    toggleCourseSection: () => void;
    courseSectionOpen: boolean;
    currentCourse: any;
    setCourseSectionOpen: (value: boolean) => void;
}

const CourseTopics = forwardRef<BottomSheet, CourseSectionProps>(({ currentCourse, toggleCourseSection, courseSectionOpen, setCourseSectionOpen }, ref) => {

    const { colorScheme } = useContext(ClientContext);

    const snapPoints = useMemo(() => ["7%", "60%"], []);

    const renderBackdrop = useCallback((props: any) => {
        return (
            <BottomSheetBackdrop
                appearsOnIndex={4}
                disappearsOnIndex={1}
                {...props}
            />
        );
    }, []);

    const handleSheetChange = useCallback((index: number) => {
        if (index === 0) {
            setCourseSectionOpen(false);
        } else {
            setCourseSectionOpen(true);
        }
    }, [setCourseSectionOpen]);

    const [lesson, setLesson] = useState(false);

    const toggleLesson = () => {
        setLesson(!lesson);
    }

    return (
        <BottomSheet
            snapPoints={snapPoints}
            ref={ref}
            enablePanDownToClose={false}
            onClose={toggleCourseSection}
            handleStyle={{ display: "none" }}
            backdropComponent={renderBackdrop}
            onChange={handleSheetChange}
        >

            <TouchableOpacity
                activeOpacity={0.8}
                onPress={toggleCourseSection}
                className='bg-primary h-16 flex-row items-center justify-center space-x-1'
            >
                <Text className='text-secondary text-xl tracking-wider mb-1' style={Global.text_bold}>Course Sections</Text>
                <Ionicons name={courseSectionOpen ? 'caret-down-outline' : 'caret-up-outline'} size={23} color="black" />

            </TouchableOpacity>

            <BottomSheetScrollView
                showsVerticalScrollIndicator={false}
                style={{
                    backgroundColor: colorScheme === 'dark' ? '#000' : '#fff'
                }}
            >
                <View className='mt-1'>
                    {currentCourse?.topics?.map((topic: any, index: number) => (
                        <View key={index}>

                            <TouchableOpacity activeOpacity={0.8} className='w-full h-14 px-2 bg-primary flex-row items-center justify-between' onPress={toggleLesson}>

                                <View className='flex-row items-center space-x-2'>
                                    <Ionicons name={lesson ? 'chevron-up-outline' : 'chevron-down-outline'} size={20} color='#000' />
                                    <Text style={Global.text_bold} className='text-secondary text-text16 tracking-wide'>
                                        {topic?.title}
                                    </Text>
                                </View>

                                <View className='flex-row items-center space-x-2'>
                                    <Text style={Global.text_bold} className='text-secondary text-text16 tracking-wide'>
                                        {topic?.lectures?.length} Lectures
                                    </Text>
                                    <View className='h-1 w-1 rounded-full bg-black' />
                                    <Text style={Global.text_bold} className='text-secondary text-text16 tracking-wide'>
                                        20 mins
                                    </Text>
                                </View>

                            </TouchableOpacity>

                            {lesson && <View className='my-3'>

                                {topic?.lectures?.map((lecture: any, index: number) => (

                                    <View className='flex-row items-start w-full' key={index}>

                                        <View className='flex-col items-center w-[15%]'>
                                            <View className='h-[30] w-[30] rounded-full bg-transparent items-center justify-center border-[1.5px] border-black dark:border-white'>
                                                {lecture?.title ? <Entypo name="check" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} /> :
                                                    <FontAwesome6 name="question" size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                                            </View>
                                            {topic?.lectures?.length !== index + 1 ? <View className='h-8 w-[1.5px] bg-black dark:bg-white' /> : null}
                                        </View>

                                        <View className='w-[80%] mt-[5px]'>
                                            <Text className='text-secondary dark:text-white text-text16 tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_bold}>
                                                {lecture?.title ? lecture?.title : 'Take a small quiz'}
                                            </Text>
                                        </View>

                                    </View>
                                ))}

                            </View>}

                        </View>
                    ))}
                </View>
            </BottomSheetScrollView>

        </BottomSheet>
    );
});

export default CourseTopics;
