import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import Ionicons from '@expo/vector-icons/Ionicons'

import Badge from '../../../../components/Badge'
import Global from '../../../../../globalStyle'
import { ClientContext } from '../../../../context/ClientContext'
import Button from '../../../../components/Button'
import { Feather } from '@expo/vector-icons'

import { StarRatingDisplay } from 'react-native-star-rating-widget'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../../context/LikeSaveContext'

interface DetailCardProps {
    showConfirmation?: boolean;
    confirmationData?: any;
    data?: any;
    paymentCancelled?: boolean;
}

const DetailCard: React.FC<DetailCardProps> = ({ showConfirmation, confirmationData, data, paymentCancelled }) => {

    const { colorScheme, navigation, currentCourseChip, registeredCourses, userData, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, handleView, registeredItems } = useContext(LikeSaveContext);

    const item = confirmationData
        ? confirmationData
        : data;

    const exploreMoreCourse = () => {
        navigation.navigate('Courses');
    }

    const viewBookedCourse = () => {
        navigation.navigate('Profile', {
            screen: 'ProfileHome',
            params: {
                from: 'Enrolled',
                to: 'Courses'
            }
        });
    }

    const handleCourse = () => {
        if (isPaymentCompleted())
            navigation.navigate('BookedCourse', { currentCourse: item });
        else {
            navigation.navigate('CourseDetails', { currentCourse: item });
            handleView(item?._id, currentCourseChip);
        }
    }

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(item?._id);
        const isRegisteredPreviously = registeredCourses?.includes(item?._id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    const getTotalDuration = (topics: any[]) => {
        let total_mins = 0;
        topics?.map((topic: any) => (
            total_mins += topic.total_lecture_duration
        ))
        return `${total_mins === 60 ? '1 hr' : total_mins < 60 ? `${total_mins} mins` : `${total_mins % 60} hrs`}`;
    }

    const isCoursesCompleted = () => {
        const completed = userData?.completed_courses?.find(course => course.course_id === item?._id);
        return completed ? true : false;
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={showConfirmation ? undefined : handleCourse}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                    {!paymentCancelled && <View className='absolute bottom-1 left-1'>
                        {isCoursesCompleted()
                            ? <Badge name='Completed' backgroundColor='#00000066' borderColor='#fff' color='#fff' />
                            : isPaymentCompleted()
                                ? <Badge name='Enrolled' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                                : null
                        }
                    </View>}
                </View>

                <View>
                    <Badge height={30} name={currentCourseChip} />
                </View>

                <View className='w-full space-y-2'>
                    <Text
                        className='text-lg text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={2}
                        ellipsizeMode='tail'
                    >
                        {item?.title}
                    </Text>
                    <Text
                        className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                        style={Global.text_medium}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        By {item?.posted_by}
                    </Text>
                </View>

                <View className='flex-row items-center justify-between w-full'>

                    {item?.average_rating !== 0 &&
                        <View className='flex-row items-center space-x-1'>
                            <Text
                                className='text-text15 text-slate-500 dark:text-white'
                                style={Global.text_medium}
                                numberOfLines={1}
                                ellipsizeMode='tail'
                            >
                                ({item?.average_rating?.toFixed(1)})
                            </Text>
                            <StarRatingDisplay
                                rating={item?.average_rating}
                                maxStars={5}
                                starSize={20}
                                color='#fdd066'
                                emptyColor={colorScheme === 'dark' ? '#fff' : '#64748b'}
                                style={{ padding: 0 }}
                            />
                        </View>
                    }

                    <View className='flex-row items-center space-x-2'>
                        <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                        <Text className='tracking-wide text-text14 text-secondary dark:text-white' style={Global.text_medium}>
                            {getTotalDuration(item?.topics)}
                        </Text>
                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(item._id, currentCourseChip)}>
                    <Ionicons name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

            <View className='space-y-2'>
                {showConfirmation && <View>
                    <Button
                        title='Explore more upcoming course'
                        onPress={exploreMoreCourse}
                        borderColor='gray'
                        color={colorScheme === 'dark' ? "white" : 'black'}
                        bgColor='transparent'
                        height={57}
                    />
                </View>}

                {paymentCancelled && <View className=''>
                    <Button
                        title='Back to course page'
                        onPress={exploreMoreCourse}
                        height={57}
                    />
                </View>}

                {showConfirmation && <View className=''>
                    <Button
                        title='View booked course details'
                        onPress={viewBookedCourse}
                        height={57}
                    />
                </View>}

            </View>

        </View>
    )
}

export default DetailCard;
