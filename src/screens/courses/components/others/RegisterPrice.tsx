import { View, Text } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import Global from '../../../../../globalStyle'
import Button from '../../../../components/Button'
import { ClientContext } from '../../../../context/ClientContext'
import { LikeSaveContext } from '../../../../context/LikeSaveContext'
import { ClientAxiosInstance } from '../../../../lib/axiosInstance'

interface RegisterPriceProps {
    currentCourse: any;
    selectedChip: string;
    setFeedbackLoading: (value: boolean) => void;
    setFeedback: (data: any[]) => void;
}

const RegisterPrice: React.FC<RegisterPriceProps> = ({ currentCourse, selectedChip, setFeedbackLoading, setFeedback }) => {

    const { navigation, registeredCourses } = useContext(ClientContext);
    const { registeredItems } = useContext(LikeSaveContext);

    const handleRegisteration = () => {
        navigation.navigate('CourseCheckout', { selectedChip: selectedChip, currentCourse: currentCourse })
    }

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(currentCourse?._id);
        const isRegisteredPreviously = registeredCourses?.includes(currentCourse?._id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    const fetchFeedback = async () => {
        try {
            const response = await ClientAxiosInstance.get(`/coursefeedback/singlecoursefeedback/${currentCourse?._id}`);
            const feedback_approved = response.data.data.filter((feed: any) => feed.feedback_approval_status === 'Approved');
            setFeedback(feedback_approved);

        } catch (error: any) {
            console.log('Error fetching feedback : ', error.response.data.message);

        } finally {
            setFeedbackLoading(false);
        }
    };

    useEffect(() => {
        fetchFeedback();
    }, [])

    return (
        <View className='space-y-5'>

            <View className='bg-cardLight dark:bg-secondary py-6 flex-row items-center justify-center rounded-md'>
                <Text style={Global.text_bold} className='text-xl text-secondary dark:text-white tracking-wide'>
                    Price : &nbsp;
                </Text>
                <Text style={Global.text_bold} className='text-secondary dark:text-white text-xl tracking-wide line-through'>
                    &#8377;{currentCourse?.price}
                </Text>
                {currentCourse?.discount_price ? <Text style={Global.text_bold} className='text-primary text-xl tracking-wide'>&nbsp;&#8377;{currentCourse?.discount_price}</Text> :
                    <Text style={Global.text_bold} className='text-primary text-xl tracking-wide'>&nbsp;Free</Text>
                }
                <Text style={Global.text_bold} className='text-secondary dark:text-white text-text17 tracking-wide'>&nbsp;+ GST/-</Text>
            </View>

            {!isPaymentCompleted() && <View className='w-full'>
                <Button title={currentCourse?.discount_price > 0 ? 'Register Now' : 'Free Registration'} height={57} onPress={handleRegisteration} />
            </View>}

        </View>
    )
}

export default RegisterPrice