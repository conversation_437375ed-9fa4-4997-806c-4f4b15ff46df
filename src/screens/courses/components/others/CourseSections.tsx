import React, { useMemo, useCallback, forwardRef, useContext, useState } from 'react';
import { Dimensions, Platform, Text, TouchableOpacity, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { ClientContext } from '../../../../context/ClientContext';
import Global from '../../../../../globalStyle';
import { Ionicons } from '@expo/vector-icons';
import Section from './Section';

const { height, width } = Dimensions.get('window');

interface CourseSectionProps {
    toggleCourseSection: () => void;
    courseSectionOpen: boolean;
    currentCourse: any;
    setCourseSectionOpen: (value: boolean) => void;
}

const CourseSections = forwardRef<BottomSheet, CourseSectionProps>((
    { currentCourse, toggleCourseSection, courseSectionOpen, setCourseSectionOpen }, ref) => {

    const { colorScheme,isAndroid } = useContext(ClientContext);
    
    const snapPoints = useMemo(() => [
        height * 0.08,
        height * 0.6
    ], []);

    const [openSectionIndex, setOpenSectionIndex] = useState<number | null>(0);

    const renderBackdrop = useCallback((props: any) => (
        <BottomSheetBackdrop appearsOnIndex={4} disappearsOnIndex={1} {...props} />
    ), []);

    const handleSheetChange = useCallback((index: number) => {
        setCourseSectionOpen(index !== 0);
    }, [setCourseSectionOpen]);

    const handleSectionToggle = useCallback((index: number) => {
        setOpenSectionIndex(openSectionIndex === index ? null : index);
    }, [openSectionIndex]);

    return (
        <BottomSheet
            snapPoints={snapPoints}
            ref={ref}
            enablePanDownToClose={false}
            onClose={toggleCourseSection}
            handleStyle={{ display: "none" }}
            backdropComponent={renderBackdrop}
            onChange={handleSheetChange}
        >
            <TouchableOpacity
                activeOpacity={0.8}
                onPress={toggleCourseSection}
                className='bg-primary h-[14%] flex-row items-center justify-center space-x-1 rounded-t-lg'
            >
                <Text className='text-secondary text-xl tracking-wider mb-1' style={Global.text_bold}>Course Sections</Text>
                <Ionicons name={courseSectionOpen ? 'caret-down-outline' : 'caret-up-outline'} size={23} color="black" />
            </TouchableOpacity>

            <BottomSheetScrollView
                showsVerticalScrollIndicator={false}
                style={{ backgroundColor: colorScheme === 'dark' ? '#000' : '#fff' }}
            >
                <View className={`mb-4 ${!isAndroid ? (width >= 400 ? (courseSectionOpen ? 'mt-1' : 'mt-0') : 'mt-1') : 'mt-1'}`}>
                    {currentCourse?.topics?.map((topic: any, index: number) => (
                        <View key={index}>
                            <Section
                                topic={topic}
                                isOpen={openSectionIndex === index}
                                onToggle={() => handleSectionToggle(index)}
                                currentCourseLength={currentCourse?.topics?.length}
                                currentCourseTopicIndex={index}
                            />
                            {openSectionIndex !== index && openSectionIndex !== index + 1 && (
                                <View className='px-primary'>
                                    <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                                </View>
                            )}
                        </View>
                    ))}
                </View>

            </BottomSheetScrollView>

        </BottomSheet>
    );
});

export default CourseSections;
