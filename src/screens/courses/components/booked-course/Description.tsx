import { View, Text } from 'react-native';
import React, { useContext } from 'react';
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons';
import Accordion from 'react-native-accordion-wrapper';
import { Image } from 'expo-image';
import HTMLView from 'react-native-htmlview';
import { ClientContext } from '../../../../context/ClientContext';
import { styles } from '../../../style/styles';
import Global from '../../../../../globalStyle';
import FaqImage from '../../../../components/FaqImage';

interface DescriptionProps {
    currentCourse: any;
}


const Description: React.FC<DescriptionProps> = ({ currentCourse }) => {

    const { colorScheme } = useContext(ClientContext);

    const courseDescription = [
        {
            title: `About Course`,
            child: (
                <View className='bottom-1 w-full space-y-3 bg-cardLight dark:bg-darkcard rounded-b-md px-[10px]'>
                    <Text
                        className='tracking-wide text-center text-secondary dark:text-white text-text15'
                        style={Global.text_bold}
                    >
                        Helping Lawyers in building skills and knowledge in Legal studies.
                    </Text>
                    <View className='h-[100px] w-full self-center items-center'>
                        <FaqImage />
                    </View>
                    <View className='mb-3'>
                        <HTMLView
                            value={currentCourse?.description}
                            style={{
                                backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#f8f6f3',
                                borderBottomLeftRadius: 5,
                                borderBottomRightRadius: 5
                            }}
                            stylesheet={styles(colorScheme)}
                        />
                    </View>
                </View>
            )
        }
    ];

    const facultyDescription = [
        {
            title: `About Faculty`,
            child: (
                <View className='w-full bottom-1 bg-cardLight dark:bg-darkcard rounded-b-md'>
                    <View className='h-[170px] w-full self-center mb-3 px-[10px]'>
                        <Image
                            source={{ uri: currentCourse?.tutor_image_path }}
                            style={{
                                height: '100%',
                                width: '100%',
                                borderRadius: 6
                            }}
                            contentFit='cover'
                        />
                    </View>
                    <View className='mb-3 px-[10px]'>
                        <Text style={Global.text_medium} className='text-secondary dark:text-white text-text14'>{currentCourse?.about_tutor}</Text>
                    </View>
                </View>
            )
        }
    ];

    return (
        <View className='space-y-3'>

            {courseDescription.length > 0 && <View>
                <Accordion
                    dataSource={courseDescription}
                    rightChevronIcon={<SimpleLineIcons name='arrow-up' size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                    headerItemsStyle={{
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#F8F6F3',
                        height: 70,
                        paddingHorizontal: 10,
                        borderRadius: 5,
                        borderBottomWidth: 0,
                    }}
                    headerTitleLabelStyle={{
                        color: colorScheme === 'dark' ? "#fff" : '#111',
                        fontSize: 16,
                        fontFamily: 'DMSans-Bold',
                        letterSpacing: 0.5
                    }}
                />
            </View>}

            {facultyDescription.length > 0 && <View>
                <Accordion
                    dataSource={facultyDescription}
                    rightChevronIcon={<SimpleLineIcons name='arrow-up' size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                    headerItemsStyle={{
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#F8F6F3',
                        height: 70,
                        paddingHorizontal: 10,
                        borderRadius: 5,
                        borderBottomWidth: 0,
                    }}
                    headerTitleLabelStyle={{
                        color: colorScheme === 'dark' ? "#fff" : '#111',
                        fontSize: 16,
                        fontFamily: 'DMSans-Bold',
                        letterSpacing: 0.5
                    }}
                />
            </View>}

        </View>
    );
};

export default Description;