import { View, Dimensions, Text, Pressable } from 'react-native';
import { Image } from 'expo-image';
import React, { useContext, useEffect, useRef, useState } from 'react';
import * as ScreenOrientation from 'expo-screen-orientation';
import { setStatusBarHidden } from 'expo-status-bar';
import { VideoView, useVideoPlayer, VideoSource } from 'expo-video';
import { FontAwesome5, Fontisto, MaterialIcons } from '@expo/vector-icons';
import Button from '../../../../components/Button';
import Global from '../../../../../globalStyle';
import Slider from '@react-native-community/slider';
import { capitalizeMode } from '../../../../helpers/getCapitalize';
import { ClientAxiosInstance } from '../../../../lib/axiosInstance';
import { ClientContext } from '../../../../context/ClientContext';
import { Toast } from '../../../../components/Toast';
import { SystemBars } from "react-native-edge-to-edge";

interface VideoPlayerProps {
    inFullscreen: boolean;
    setInFullscreen: (val: boolean) => void;
    handleNext: (topic_id: string, lecture_id: string) => void;
    currentLecture: any;
    lectureVideoQuizTime: any;
    courseProgress: string[];
}

const Player: React.FC<VideoPlayerProps> = ({
    inFullscreen,
    currentLecture,
    setInFullscreen,
    courseProgress,
    handleNext,
    lectureVideoQuizTime
}) => {
    const { clientId } = useContext(ClientContext);

    const videoRef = useRef<VideoView>(null);
    const [playBtn, setPlayBtn] = useState(true);
    const [isPlaying, setIsPlaying] = useState(false);
    const [videoHeight, setVideoHeight] = useState(220);
    const [isFinished, setIsFinished] = useState(false);
    const [position, setPosition] = useState(0);
    const [duration, setDuration] = useState(0);
    const [seekToValue, setSeekToValue] = useState(0);
    const [isSeeking, setIsSeeking] = useState(false);
    const [playPause, setPlayPause] = useState(false);
    const [lastUpdatePosition, setLastUpdatePosition] = useState(1);

    const player = useVideoPlayer(currentLecture?.video_url as VideoSource, (player) => {
        player.loop = false;
        player.muted = false;
    });

    useEffect(() => {
        if (!lectureVideoQuizTime || !currentLecture) return;

        const initializeVideoProgress = async () => {
            if (lectureVideoQuizTime.lecture_id === currentLecture._id) {
                const playedDuration = lectureVideoQuizTime.video_played_duration;
                setPosition(playedDuration);
                setIsPlaying(false);

                if (player) {
                    player.currentTime = playedDuration / 1000;
                }
            }
        };
        initializeVideoProgress();
    }, [lectureVideoQuizTime, currentLecture, player]);

    useEffect(() => {
        if (!player) return;

        const timeUpdateListener = player.addListener('timeUpdate', (payload) => {
            const currentTimeMs = payload.currentTime * 1000;
            if (!isSeeking) {
                setPosition(currentTimeMs);
                if (duration > 0) {
                    setSeekToValue(currentTimeMs / duration);
                }

                if (!courseProgress.includes(currentLecture?._id) && currentTimeMs >= (120000 * lastUpdatePosition)) {
                    setLastUpdatePosition(lastUpdatePosition + 1);
                    handleUpdateCurrentLectureProgress();
                }
            }
        });

        const statusChangeListener = player.addListener('statusChange', (status) => {
            if (status.status === 'readyToPlay' && duration === 0) {
                setDuration(player.duration * 1000);
            }
        });

        const playingChangeListener = player.addListener('playingChange', (isPlayingNow) => {
            setIsPlaying(isPlayingNow.isPlaying);
        });

        const playToEndListener = player.addListener('playToEnd', () => {
            setPlayBtn(true);
            setIsPlaying(false);
            setIsFinished(true);
            player.currentTime = 0;
        });

        return () => {
            timeUpdateListener.remove();
            statusChangeListener.remove();
            playingChangeListener.remove();
            playToEndListener.remove();
        };
    }, [player, duration, isSeeking, courseProgress, currentLecture?._id, lastUpdatePosition]);

    const enterFullscreen = () => {
        setStatusBarHidden(true, 'fade');
        setInFullscreen(true);
        ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    };

    const exitFullscreen = () => {
        setStatusBarHidden(false, 'fade');
        setInFullscreen(false);
        ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
    };

    const handlePlay = async () => {
        setPlayBtn(false);
        setIsPlaying(true);
        player.play();
    };

    const handleDuration = (time: number | undefined) => {
        if (time) {
            const convertedTime = Math.floor(time / 1000);
            if (convertedTime <= 59) return `${convertedTime} secs`;
            if (convertedTime === 60) return `1 min`;
            return `${(time / 60000).toFixed(2)} mins`;
        }
    };

    const handleSliderValueChange = (value: number) => {
        if (!courseProgress.includes(currentLecture?._id)) return;
        setSeekToValue(value);
        setIsSeeking(true);
    };

    const handleSliderComplete = async (value: number) => {
        const seekPosition = value * duration;
        const seekPositionSeconds = seekPosition / 1000;

        if (!courseProgress.includes(currentLecture?._id) && seekPosition <= position) {
            player.currentTime = seekPositionSeconds;
        } else if (!courseProgress.includes(currentLecture?._id) && seekPosition > position) {
            Toast.show({
                type: 'error',
                message: `You can't skip forward now`,
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        } else if (courseProgress.includes(currentLecture?._id)) {
            player.currentTime = seekPositionSeconds;
        }
        setIsSeeking(false);
    };

    const calculateVideoHeight = (width: number) => {
        const aspectRatio = 16 / 9;
        return width / aspectRatio;
    };

    const updateVideoHeight = () => {
        const { width } = Dimensions.get('window');
        const newHeight = calculateVideoHeight(width);
        setVideoHeight(newHeight);
    };

    const playPauseModal = () => {
        setPlayPause(true);
        setTimeout(() => {
            setPlayPause(false);
        }, 4000);
    };

    const handlePlayVideo = () => {
        player.play();
        setIsPlaying(true);
        if (!courseProgress.includes(currentLecture?._id)) handleUpdateCurrentLectureProgress();
    };

    const handlePauseVideo = () => {
        player.pause();
        setIsPlaying(false);
        if (!courseProgress.includes(currentLecture?._id)) handleUpdateCurrentLectureProgress();
    };

    useEffect(() => {
        updateVideoHeight();
        const subscription = Dimensions.addEventListener('change', updateVideoHeight);
        return () => subscription?.remove();
    }, []);

    useEffect(() => {
        setPlayBtn(true);
        setIsPlaying(false);
        setPosition(0);
        setDuration(0);
        setSeekToValue(0);
        setIsSeeking(false);
        setPlayPause(false);
        setIsFinished(false);
    }, [currentLecture]);

    const handleUpdateCurrentLectureProgress = async () => {
        try {
            const response = await ClientAxiosInstance.put(`/courseprogress/update-current-lecture`, {
                user_id: clientId,
                course_id: currentLecture?.course_id,
                topic_id: currentLecture?.topic_id,
                lecture_id: currentLecture?._id,
                video_played_duration: Math.floor(position),
                started_at: new Date().toISOString(),
            });
            console.log("Video progress update : ", response.data);
        } catch (err) {
            console.log("Video progress update error : ", err);
        }
    };

    return (
        <View className="items-center justify-center w-full" style={{ height: inFullscreen ? Dimensions.get('window').height : videoHeight }}>
            <SystemBars hidden={inFullscreen} />
            <View className={`h-full ${!inFullscreen && 'overflow-hidden'}`}>
                <VideoView
                    ref={videoRef}
                    style={{
                        height: '100%',
                        width: Dimensions.get('window').width,
                    }}
                    player={player}
                    nativeControls={false}
                    allowsFullscreen={false}
                    allowsPictureInPicture={false}
                    showsTimecodes={false}
                    requiresLinearPlayback={false}
                    contentFit="cover"
                    onTouchStart={playPauseModal}
                />
            </View>

            {!playBtn && playPause && (
                <View className="absolute z-20 justify-between w-full h-full py-1 space-y-1 bg-black/20">
                    <View className='self-center' style={{ width: inFullscreen ? '92%' : '100%', paddingHorizontal: inFullscreen ? 0 : 12 }}>
                        <Text className='text-white text-text15' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>
                            {capitalizeMode(currentLecture?.title)}
                        </Text>
                    </View>

                    {isPlaying ? (
                        <View className='items-center w-full'>
                            <Pressable
                                onPress={handlePauseVideo}
                                pressRetentionOffset={40}
                                className='z-50 items-center justify-center w-12 h-12 rounded-full bg-primary'
                            >
                                <Fontisto name="pause" size={18} color="#2d2828" />
                            </Pressable>
                        </View>
                    ) : (
                        <View className='items-center w-full'>
                            <Pressable
                                onPress={handlePlayVideo}
                                pressRetentionOffset={40}
                                className='z-50 items-center justify-center w-12 h-12 pl-1 rounded-full bg-primary'
                            >
                                <FontAwesome5 name="play" size={20} color="#2d2828" />
                            </Pressable>
                        </View>
                    )}

                    <View className='h-[30px] self-center flex-row items-center justify-between' style={{ width: inFullscreen ? '92%' : '100%', paddingHorizontal: inFullscreen ? 0 : 12, bottom: inFullscreen ? 12 : null }}>
                        <Text className='text-white' style={Global.text_medium}>
                            {new Date(position).toISOString().substring(14, 19)}
                        </Text>
                        <View className='items-center justify-center h-full' style={{ width: inFullscreen ? '80%' : '60%' }}>
                            <Slider
                                value={seekToValue}
                                minimumValue={0}
                                maximumValue={1}
                                thumbTintColor="#fdd066"
                                maximumTrackTintColor="#fdd066"
                                style={{
                                    width: '100%',
                                    height: '100%',
                                }}
                                onValueChange={handleSliderValueChange}
                                onSlidingComplete={handleSliderComplete}
                            />
                        </View>
                        <Text className='text-white' style={Global.text_medium}>
                            {new Date(duration - position).toISOString().substring(14, 19)}
                        </Text>
                        <Pressable
                            onPress={inFullscreen ? exitFullscreen : enterFullscreen}
                            pressRetentionOffset={40}
                        >
                            <MaterialIcons name={inFullscreen ? "fullscreen-exit" : "fullscreen"} size={28} color="#fff" />
                        </Pressable>
                    </View>
                </View>
            )}

            {playBtn && (
                <View className="absolute z-50 items-center justify-end w-full h-full py-3 space-y-1 bg-black/20">
                    {isFinished ? (
                        <Button
                            title="Continue next"
                            onPress={() => { exitFullscreen(); handleNext(currentLecture?.topic_id, currentLecture?._id) }}
                            height={52}
                        />
                    ) : (
                        <>
                            <Button
                                icon
                                buttonIcon={<FontAwesome5 name="play" size={15} color="black" />}
                                title="Start lesson"
                                onPress={handlePlay}
                                height={52}
                            />
                            {duration > 0 && (
                                <Text className="text-white text-text14" style={Global.text_medium}>
                                    ({handleDuration(duration)})
                                </Text>
                            )}
                        </>
                    )}
                </View>
            )}
        </View>
    );
};

export default Player;
