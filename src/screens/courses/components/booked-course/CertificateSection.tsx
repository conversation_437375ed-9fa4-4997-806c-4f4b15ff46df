import { View, Text} from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { ClientContext } from '../../../../context/ClientContext'
import { ClientAxiosInstance } from '../../../../lib/axiosInstance';
import Button from '../../../../components/Button';
import { handleCertificateDownload } from '../../../../helpers/downloadCertificate';
import { Image } from 'expo-image';
import Global from '../../../../../globalStyle';

interface CertificateSectionProps {
    currentCourse: any;
    setIsFeedbackModalVisible: (visible: boolean) => void;
    isCourseFinished: boolean;
}

const CertificateSection: React.FC<CertificateSectionProps> = ({ currentCourse, setIsFeedbackModalVisible, isCourseFinished }) => {

    const { userData, clientId, getClientData } = useContext(ClientContext);

    const [courseCertificate, setCourseCertificate] = useState<string>('');

    const feedbackSubmited = async (id: string) => {
        try {
            const response = await ClientAxiosInstance.get(`/coursefeedback/${clientId}/${id}`);
            setIsFeedbackModalVisible(!response.data.isFeedbackGiven);

        } catch (error: any) {
            console.log("Checking feedback is submitted? : ", error.response.data.message);
        }
    }

    const getCertificateData = async () => {
        const isUserCompletedCourse = userData?.completed_courses?.find(course => course.course_id === currentCourse?._id);
        if (isCourseFinished) {
            if (!isUserCompletedCourse) {
                try {
                    const res = await ClientAxiosInstance.post(`/courseprogress/generate-certificate/${clientId}/${currentCourse?._id}`);
                    getClientData();
                    setCourseCertificate(res.data?.url);
                } catch (err) {
                    console.log("Getting certificate error : ", err);
                }
            } else {
                setCourseCertificate(isUserCompletedCourse?.url || "");
            }
            await feedbackSubmited(currentCourse?._id);
        }
    }

    useEffect(() => {
        getCertificateData();
    }, [currentCourse?._id, isCourseFinished, clientId, userData?.completed_courses]);

    return (
        <View className="px-primary space-y-6 mt-3">

            <View className='h-[210px] w-full items-center justify-center'>

                <Image
                    source={{ uri: currentCourse.thumbnail_image_path }}
                    style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: 5
                    }}
                />

                <View className='w-full absolute px-6'>
                    <View className='bg-cardLight w-full rounded-md py-6 space-y-4 shadow-lg'>
                        <Text className='text-text17 tracking-wide text-center text-secondary' style={Global.text_bold}>
                            {currentCourse?.title}
                        </Text>
                        <Text className='text-text17 tracking-wide text-center text-[#F9AE00]' style={Global.text_bold}>
                            Completed!
                        </Text>
                    </View>
                </View>

            </View>

            <View className="items-center">
                <Button
                    title='Download Certificate'
                    onPress={() => handleCertificateDownload(currentCourse?.title, courseCertificate)}
                    height={50}
                    paddingX={15}
                />
            </View>

        </View>
    )
}

export default CertificateSection