import { View, Text, TouchableOpacity, Animated } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { AntDesign, Entypo, Feather, FontAwesome6, Ionicons, Octicons } from '@expo/vector-icons'
import Global from '../../../../../globalStyle';
import { ClientContext } from '../../../../context/ClientContext';
import { ClientAxiosInstance } from '../../../../lib/axiosInstance';
import AutoScroll from "@homielab/react-native-auto-scroll";

interface SectionProps {
    topic: any;
    courseProgress?: string[];
    setCurrentLecture: (value: any) => void;
    setLectureIndex: (value: number) => void;
    totalLectures: any[];
    getCourseQuizResult: (id: string) => any;
    isOpen: boolean;
    onToggle: () => void;
    currentLecture: any;
    currentCourseLength: number;
    currentCourseTopicIndex: number;
    currentCourse: any;
    isLastLecture: boolean;
}

const Section: React.FC<SectionProps> = (
    {
        courseProgress, setCurrentLecture, currentLecture, setLectureIndex,
        totalLectures, getCourseQuizResult, topic, isOpen, onToggle,
        currentCourseLength, currentCourseTopicIndex, currentCourse, isLastLecture
    }
) => {

    const { userData, colorScheme, navigation } = useContext(ClientContext);
    const [certificate, setCertificate] = useState(false);

    const [isLastLectureQuiz, setIsLastLectureQuiz] = useState(false);

    const [courseQuizAnswers, setCourseQuizAnswers] = useState<any>();

    const handleMakeCurrentLecture = (id: string) => {

        const lecture = totalLectures?.find((lecture: any) => (lecture._id === id));
        const lecture_index = totalLectures?.findIndex((lecture: any) => (lecture._id === id));

        if (courseProgress?.includes(id)) {
            setCurrentLecture(lecture);
            setLectureIndex(lecture_index);
            getCourseQuizResult(lecture.quiz_id);
        }
    }

    const handleCertificateUi = () => {
        const isCertificateGenerated = userData?.completed_courses?.find((course: any) =>
            course.course_id === currentCourse?._id)?.url;
        setCertificate(isCertificateGenerated ? true : false);
    }

    const getCourseQuiz = async (id: string) => {
        if (courseQuizAnswers?.quiz_id === id) return;
        try {
            const response = await ClientAxiosInstance.get(`/coursequiz/attended/${id}`);
            const responseData = response.data.data;
            if (responseData) setCourseQuizAnswers({ ...responseData, quiz_id: id });

        } catch (error) {
            console.log("Error fetching quiz result: ", error);
        }
    };

    const handleQuizNavigation = () => {
        if (courseQuizAnswers) {
            navigation.navigate('CourseQuizAnswers', { courseQuizAnswers: courseQuizAnswers });
        }
    }

    const setCertificateLecture = () => {
        setCurrentLecture(null);
    }

    useEffect(() => {
        handleCertificateUi();
        if (courseProgress) {
            topic?.lectures?.forEach((lecture: any) => {
                if (lecture.item_type === 'quiz' && courseProgress.includes(lecture._id)) {
                    getCourseQuiz(lecture.quiz_id);
                }
            });
        }
    }, [courseProgress]);

    useEffect(() => {
        const lecture_length = topic?.lectures?.length;
        const isLastQuiz = topic?.lectures?.some((lecture: any, index: number) => {
            return lecture_length === index + 1 && lecture?.item_type === 'quiz';
        });
        setIsLastLectureQuiz(isLastQuiz);
    }, [topic]);

    return (
        <View>

            <TouchableOpacity
                activeOpacity={0.8}
                className={`w-full h-14 px-primary flex-row items-center justify-between ${isOpen ? 'bg-primary' : 'bg-transparent'}`}
                onPress={onToggle}
            >
                <View className="flex-row items-center flex-[0.5]">
                    <Ionicons
                        name={isOpen ? 'chevron-up-outline' : 'chevron-down-outline'}
                        size={20}
                        color={isOpen ? '#000' : colorScheme === 'dark' ? '#fff' : '#000'}
                    />
                    <View className='flex-1 mx-1'>
                        {isOpen ? <AutoScroll endPaddingWidth={0}>
                            <Text
                                style={Global.text_bold}
                                className={`${isOpen ? 'text-secondary' : 'text-secondary dark:text-white'} text-text15 tracking-wide`}
                            >
                                {topic?.title}
                            </Text>
                        </AutoScroll> :
                            <Text
                                style={Global.text_bold}
                                className={`${isOpen ? 'text-secondary' : 'text-secondary dark:text-white'} text-text15 tracking-wide`}
                                numberOfLines={1}
                                ellipsizeMode='tail'
                            >
                                {topic?.title}
                            </Text>
                        }
                    </View>
                </View>

                <View className="h-full flex-row items-center justify-end flex-[0.5]">
                    <Text
                        style={Global.text_bold}
                        className={`${isOpen ? 'text-secondary' : 'text-secondary dark:text-white'} text-text14 tracking-wide`}
                    >
                        {topic?.lectures?.length} Lectures
                    </Text>
                    <View className={`h-1 w-1 rounded-full mx-2 ${isOpen ? 'bg-black' : 'bg-black dark:bg-white'}`} />
                    <Text
                        style={Global.text_bold}
                        className={`${isOpen ? 'text-secondary' : 'text-secondary dark:text-white'} text-text14 tracking-wider`}
                    >
                        {topic?.total_lecture_duration === 60
                            ? '1 hr'
                            : topic?.total_lecture_duration < 60
                                ? `${topic?.total_lecture_duration} mins`
                                : `${Math.floor(topic?.total_lecture_duration / 60)} hrs ${topic?.total_lecture_duration % 60} mins`}
                    </Text>
                </View>

            </TouchableOpacity>

            {isOpen &&
                <View className='mt-3'>

                    <View style={{ marginBottom: currentCourseLength === currentCourseTopicIndex + 1 ? 0 : 12 }}>

                        {topic?.lectures?.map((lecture: any, index: number) => (
                            <View className='flex-row items-start w-full' key={index}>

                                <View className='flex-col items-center w-[15%]'>
                                    <View className={`h-[30px] w-[30px] rounded-full bg-transparent items-center justify-center border-[1.5px] ${courseProgress?.includes(lecture?._id) ? 'border-[#2BAA16]' : 'border-greycolor'}`}>
                                        {lecture?.item_type !== 'quiz' ?
                                            <Entypo name="check" size={18} color={courseProgress?.includes(lecture?._id) ? "#2BAA16" : '#a8a8a8'} /> :
                                            courseProgress?.includes(lecture?._id) ?
                                                <AntDesign name="star" size={18} color="#2BAA16" /> :
                                                <FontAwesome6 name="question" size={16} color={courseProgress?.includes(lecture?._id) ? "#2BAA16" : '#a8a8a8'} />
                                        }
                                    </View>
                                    {topic?.lectures?.length !== index + 1
                                        ? <View className={`w-[1.5px] ${courseProgress?.includes(lecture?._id) ? lecture?.item_type === 'quiz' ? 'bg-[#2baa16] h-12' : 'bg-[#2baa16] h-9' : lecture?.item_type === 'quiz' ? "bg-greycolor h-12" : 'bg-greycolor h-9'}`} />
                                        : isLastLectureQuiz && isLastLecture
                                            ? <View className={`w-[1.5px] ${isLastLecture && topic?.lectures[index]?.item_type === 'quiz' && courseProgress?.includes(lecture?._id) ? 'bg-[#2baa16] h-12' : "bg-greycolor h-12"}`} />
                                            : null
                                    }
                                </View>

                                {courseProgress?.includes(lecture?._id) ? (

                                    <View className='w-full space-y-2'>

                                        <TouchableOpacity activeOpacity={0.8} className='w-[80%] mt-[5px]' onPress={() => handleMakeCurrentLecture(lecture?._id)}>
                                            <Text className='text-secondary dark:text-white text-text15 tracking-wide' style={Global.text_medium}>
                                                {lecture?.item_type !== 'quiz' ? lecture?.title : 'Take a small quiz'}
                                            </Text>
                                        </TouchableOpacity>

                                        {lecture?.item_type === 'quiz' &&
                                            <View className='items-start'>
                                                <TouchableOpacity
                                                    activeOpacity={0.8}
                                                    onPress={handleQuizNavigation}
                                                    className='space-x-[5px] px-3 py-3 flex-row items-center justify-center rounded-full bg-primary'
                                                >
                                                    <Ionicons name='eye-outline' size={18} color='#2d2828' />
                                                    <Text className='underline text-text15 text-secondary' style={Global.text_medium}>View answers</Text>
                                                    <Text className='text-text14 text-secondary' style={Global.text_bold}>({courseQuizAnswers?.percentage_score ?? 0}/100)</Text>
                                                </TouchableOpacity>
                                            </View>
                                        }

                                    </View>
                                ) : (
                                    <View className='w-full space-y-2'>

                                        <View className='w-[80%] mt-[5px] flex-row items-center'>
                                            <Text className={`text-text15 tracking-wide text-greycolor`} style={Global.text_medium}>
                                                {lecture?.item_type !== 'quiz' ? lecture?.title : 'Take a small quiz'}
                                            </Text>
                                        </View>

                                        {lecture?.item_type === 'quiz' &&
                                            <View className='items-start'>
                                                <View className='space-x-[5px] px-3 py-3 flex-row items-center justify-center rounded-full bg-greycolor'>
                                                    <Ionicons name='eye-outline' size={18} color='#2d2828' />
                                                    <Text className='underline text-text15 text-secondary' style={Global.text_medium}>View answers</Text>
                                                    <Text className='text-text14 text-secondary' style={Global.text_bold}>({courseQuizAnswers?.percentage_score ?? 0}/100)</Text>
                                                </View>
                                            </View>
                                        }

                                    </View>
                                )}

                            </View>
                        ))}

                    </View>

                    <View>
                        {isLastLecture &&
                            <View className='flex-row items-end w-full mb-3'>

                                <View className='flex-col items-center w-[15%]'>
                                    <View className={`${isLastLectureQuiz ? 'h-3' : 'h-9'} w-[1.5px] ${certificate ? 'bg-[#2BAA16]' : 'bg-greycolor'}`} />
                                    <View className={`h-[30] w-[30] rounded-full bg-transparent items-center justify-center border-[1.5px] ${certificate ? 'border-[#2BAA16]' : 'border-greycolor'}`}>
                                        <Octicons name="download" size={20} color={certificate ? '#2BAA16' : "#a8a8a8"} />
                                    </View>
                                </View>

                                <TouchableOpacity activeOpacity={0.8} onPress={setCertificateLecture} className='w-[80%] mb-2 flex-row items-center'>
                                    <Text className={`text-text15 tracking-wide ${certificate ? 'text-secondary dark:text-white' : 'text-greycolor'}`} style={Global.text_medium}>
                                        Download Certificate
                                    </Text>
                                </TouchableOpacity>

                            </View>
                        }
                        <View className='px-primary'>
                            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                        </View>

                    </View>

                </View>
            }

        </View>
    )
}

export default Section
