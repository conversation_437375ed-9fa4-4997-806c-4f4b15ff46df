import { View, Text } from 'react-native'
import React, { useEffect, useState } from 'react'
import { ProgressBar } from 'react-native-paper'
import Global from '../../../../../globalStyle'
import FeedbackModal from '../../../../components/FeedbackModel';

interface CourseTrackerProps {
    currentCourse: any;
    courseProgress: string[];
}

const CourseTracker: React.FC<CourseTrackerProps> = ({ currentCourse, courseProgress }) => {

    const [lengthOfTracker, setLengthOfTracker] = useState(0);
    const [totalLectures, setTotalLectures] = useState(0);

    const getTotalDuration = (item: any[]) => {
        let total_mins = 0;
        item?.map((topic: any) => (
            total_mins += topic?.total_lecture_duration
        ))
        return `${total_mins === 60 ? '1 hr' : total_mins < 60 ? `${total_mins} mins` : `${total_mins % 60} hrs`}`;
    }

    useEffect(() => {
        if (currentCourse?.topics) {
            let totalLectures = 0;
            currentCourse.topics.forEach((topic: any) => {
                totalLectures += topic?.lectures?.length || 0;
            });

            setTotalLectures(totalLectures);

            if (totalLectures > 0) {
                setLengthOfTracker(1 / totalLectures);
            }
        }
    }, [currentCourse, courseProgress, totalLectures]);

    return (
        <View className='space-y-3'>

            <Text
                className='text-lg tracking-wide text-center text-secondary dark:text-white'
                style={Global.text_medium}
            >
                Course Completion Tracker
            </Text>

            <View className='h-[22px] p-[2] rounded-full bg-transparent border-[0.5px] border-secondary dark:border-white'>
                <ProgressBar
                    progress={lengthOfTracker * courseProgress?.length}
                    color='#fdd066'
                    style={{
                        height: '100%',
                        borderRadius: 25,
                        backgroundColor: 'transparent'
                    }}
                />
            </View>

            <View className='flex-row items-center justify-around px-2'>

                <Text
                    className='tracking-wide text-center text-text15 text-secondary dark:text-white'
                    style={Global.text_regular}
                >
                    {currentCourse?.topics?.length} sections
                </Text>

                <View className='w-1 h-1 bg-black rounded-full dark:bg-white' />

                <Text
                    className='tracking-wide text-center text-text15 text-secondary dark:text-white'
                    style={Global.text_regular}
                >
                    {totalLectures} lectures
                </Text>

                <View className='w-1 h-1 bg-black rounded-full dark:bg-white' />

                <Text
                    className='tracking-wide text-center text-text15 text-secondary dark:text-white'
                    style={Global.text_regular}
                >
                    {getTotalDuration(currentCourse?.topics)} total length
                </Text>

            </View>

        </View>
    )
}

export default CourseTracker