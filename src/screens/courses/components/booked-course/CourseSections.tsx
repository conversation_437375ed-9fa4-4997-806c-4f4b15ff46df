import React, { useMemo, useCallback, forwardRef, useContext, useState } from 'react';
import { Dimensions, Platform, Text, TouchableOpacity, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { ClientContext } from '../../../../context/ClientContext';
import Global from '../../../../../globalStyle';
import { Ionicons } from '@expo/vector-icons';
import Section from './Section';


const { width, height } = Dimensions.get('window');

interface CourseSectionProps {
    toggleCourseSection: () => void;
    courseSectionOpen: boolean;
    currentCourse: any;
    setCourseSectionOpen: (value: boolean) => void;
    courseProgress?: string[];
    setCurrentLecture: (value: any) => void;
    setLectureIndex: (value: number) => void;
    totalLectures: any[];
    getCourseQuizResult: (id: string) => void;
    currentLecture: any;
}

const CourseSections = forwardRef<BottomSheet, CourseSectionProps>(({
    currentCourse, toggleCourseSection, courseProgress, courseSectionOpen,
    setCourseSectionOpen, setCurrentLecture, setLectureIndex, totalLectures,
    getCourseQuizResult, currentLecture
}, ref) => {

    const { colorScheme, isAndroid } = useContext(ClientContext);

    const renderBackdrop = useCallback((props: any) => {
        return (
            <BottomSheetBackdrop
                appearsOnIndex={4}
                disappearsOnIndex={1}
                {...props}
            />
        );
    }, []);

    const handleSheetChange = useCallback((index: number) => {
        if (index === 0) {
            setCourseSectionOpen(false);
        } else {
            setCourseSectionOpen(true);
        }
    }, [setCourseSectionOpen]);

    const snapPoints = useMemo(() => [
        height * 0.08,
        height * 0.6
    ], []);

    const [openSectionIndex, setOpenSectionIndex] = useState<number | null>(0);

    const handleSectionToggle = useCallback((index: number) => {
        setOpenSectionIndex(openSectionIndex === index ? null : index);
    }, [openSectionIndex]);

    return (
        <BottomSheet
            snapPoints={snapPoints}
            ref={ref}
            enablePanDownToClose={false}
            onClose={toggleCourseSection}
            handleStyle={{ display: "none" }}
            backdropComponent={renderBackdrop}
            onChange={handleSheetChange}
        >

            <TouchableOpacity
                activeOpacity={0.8}
                onPress={toggleCourseSection}
                className='bg-primary h-[14%] flex-row items-center justify-center space-x-1 rounded-t-lg'
            >
                <Text className='mb-1 text-xl tracking-wider text-secondary' style={Global.text_bold}>Course Sections</Text>
                <Ionicons name={courseSectionOpen ? 'caret-down-outline' : 'caret-up-outline'} size={23} color="black" />

            </TouchableOpacity>

            <BottomSheetScrollView
                showsVerticalScrollIndicator={false}
                style={{
                    backgroundColor: colorScheme === 'dark' ? '#111' : '#fff'
                }}
            >
                <View className={`${!isAndroid ? (width >= 400 ? (courseSectionOpen ? 'mt-1' : 'mt-0') : 'mt-1') : 'mt-1'} mb-5`}>
                    {currentCourse?.topics?.map((topic: any, index: number) => (
                        <View key={index}>
                            <Section
                                topic={topic}
                                courseProgress={courseProgress}
                                setCurrentLecture={setCurrentLecture}
                                setLectureIndex={setLectureIndex}
                                totalLectures={totalLectures}
                                getCourseQuizResult={getCourseQuizResult}
                                isOpen={openSectionIndex === index}
                                onToggle={() => handleSectionToggle(index)}
                                currentLecture={currentLecture}
                                currentCourseLength={currentCourse?.topics?.length}
                                currentCourseTopicIndex={index}
                                currentCourse={currentCourse}
                                isLastLecture={currentCourse?.topics?.length === index + 1}
                            />
                            {openSectionIndex !== index && openSectionIndex !== index + 1 && (
                                <View className='px-primary'>
                                    <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                                </View>
                            )}
                        </View>
                    ))}
                </View>

            </BottomSheetScrollView>

        </BottomSheet>
    );
});

export default CourseSections;
