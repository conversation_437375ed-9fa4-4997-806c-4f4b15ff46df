import { View, Text, ActivityIndicator } from 'react-native'
import React, { useContext, useState } from 'react'
import Global from '../../../../../globalStyle'
import { capitalizeMode } from '../../../../helpers/getCapitalize';
import Button from '../../../../components/Button';
import { ClientContext } from '../../../../context/ClientContext';
import { StyleSheet } from 'react-native';
import { Entypo } from '@expo/vector-icons';
import { ClientAxiosInstance } from '../../../../lib/axiosInstance';
import { Image } from 'expo-image';

interface QuizSectionProps {
    currentCourse: any;
    courseProgress: any[];
    currentLecture: any;
    handleNext: (topic_id: string, lecture_id: string) => void;
    courseQuizAnswers: any;
    totalLectures: any[];
    lectureVideoQuizTime: any;
    getCourseQuizResult:(id:string)=>void;
}

const QuizSection: React.FC<QuizSectionProps> = ({ currentCourse, courseQuizAnswers, courseProgress, currentLecture, handleNext, totalLectures,getCourseQuizResult, lectureVideoQuizTime }) => {

    const { navigation } = useContext(ClientContext);

    const [quizLoading, setQuizLoading] = useState(false);

    const handleQuizAnswers = async (id:string) => {
        if (courseQuizAnswers)
            navigation.navigate('CourseQuizAnswers', { courseQuizAnswers: courseQuizAnswers });
        else{
            await getCourseQuizResult(id);
            navigation.navigate('CourseQuizAnswers', { courseQuizAnswers: courseQuizAnswers });
        }
    }

    const handleQuiz = async (id: string) => {
        try {
            setQuizLoading(true);
            const response = await ClientAxiosInstance.get(`/coursequiz/${id}`);
            const responseData = response.data.data;
            if (response.data.success) {
                navigation.navigate('CourseQuizRules', {
                    courseQuiz: responseData,
                    currentLecture: currentLecture,
                    currentCourse: currentCourse,
                    totalLectures: totalLectures,
                    lectureVideoQuizTime: lectureVideoQuizTime
                });
            }

        } catch (error) {
            console.log("Navigating QuizRules Error : ", error);
        } finally {
            setQuizLoading(false);
        }
    };

    return (
        <View className="px-primary space-y-5">

            <Text style={Global.text_medium} className="text-secondary dark:text-white text-lg">{capitalizeMode(currentLecture?.quiz_name)}</Text>

            {currentCourse.thumbnail_image_path &&
                <View className='h-[200px] w-full'>
                    <Image
                        source={{ uri: currentCourse.thumbnail_image_path }}
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                    <View className="absolute right-1 bottom-1">
                        {courseProgress.includes(currentLecture?._id) ?
                            <Button
                                title="View answers"
                                onPress={()=>handleQuizAnswers(currentLecture?._id)}
                                height={45}
                                paddingX={15}
                            />
                            : quizLoading ?
                                <View style={styles.button}>
                                    <Text style={styles.buttonText}>Start Evaluation Test</Text>
                                    <ActivityIndicator size={20} color='#2d2828' />
                                </View> :
                                <Button
                                    title="Start Evaluation Test"
                                    onPress={() => handleQuiz(currentLecture?.quiz_id)}
                                    height={45}
                                    paddingX={15}
                                />
                        }
                    </View>
                </View>
            }
            <View className="w-full items-center px-primary">
                {courseProgress.includes(currentLecture?._id) &&
                    <View className="mt-2">
                        <Button
                            icon
                            buttonIcon={<Entypo name="chevron-right" size={24} color="black" />}
                            iconPosition='right'
                            title='Next Lesson'
                            onPress={() => handleNext(currentLecture?.topic_id, currentLecture?._id)}
                            height={50}
                            paddingX={15}
                        />
                    </View>
                }
            </View>
        </View>
    )
}

export default QuizSection;

const styles = StyleSheet.create({
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FDD066',
        paddingHorizontal: 15,
        borderRadius: 50,
        height: 45,
    },
    buttonText: {
        color: '#2D2828',
        fontSize: 15,
        marginRight: 8,
        fontFamily: 'DMSans-Bold'
    },
});