import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../../globalStyle';
import Button from '../../../../components/Button';
import { Entypo } from '@expo/vector-icons';
import HTMLView from 'react-native-htmlview';
import { ClientContext } from '../../../../context/ClientContext';
import { styles } from '../../../style/styles';

interface ContentSliderProps {
    currentLecture: any;
    handleNext: (topic_id: string, lecture_id: string) => void;
}

const ContentSlider: React.FC<ContentSliderProps> = ({ currentLecture, handleNext }) => {

    const { colorScheme } = useContext(ClientContext);

    return (
        <View className='w-full space-y-6'>

            <View className='w-full space-y-2'>

                <Text
                    style={Global.text_bold}
                    className='text-xl text-secondary dark:text-white tracking-wide'
                >
                    {currentLecture?.title}
                </Text>

                <HTMLView
                    value={currentLecture.content}
                    stylesheet={styles(colorScheme)}
                />

            </View>

            <View className='w-full items-center'>
                <Button
                    icon
                    buttonIcon={<Entypo name="chevron-right" size={24} color="black" />}
                    iconPosition='right'
                    title='Next Lesson'
                    onPress={() => handleNext(currentLecture?.topic_id, currentLecture?._id)}
                    height={50}
                    paddingX={15}
                />
            </View>

        </View>
    )
}

export default ContentSlider