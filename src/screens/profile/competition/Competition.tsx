import React, { useMemo, useCallback, useContext, useState, useEffect, useRef } from 'react';
import { ScrollView, Text, View } from 'react-native';
import BottomSheetHeader from '../components/BottomSheetHeader';
import BottomSheetSubheader from '../components/BottomSheetSubheader';
import { ClientContext } from '../../../context/ClientContext';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import Loader from './Loader';
import CompetitionCard from './CompetitionCard';
import Global from '../../../../globalStyle';
import Pagination from '../components/Pagination';

interface CompetitionProps {
    closeModal: () => void;
    to: string;
}

const Competition: React.FC<CompetitionProps> = ({ closeModal, to }) => {

    const { isLarge } = useContext(ClientContext);

    const submenus = useMemo(() => ['Quiz', 'Moot Court', 'Essay Writing', 'Article Writing', 'Debate'], []);

    const [selectedSubMenu, setSelectedSubMenu] = useState(to||'Quiz');
    const [compData, setCompData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const [showPagination, setShowPagination] = useState(false);

    const comps = useMemo(() => {
        switch (selectedSubMenu) {
            case 'Quiz': return 'enrolled-quizzes';
            case 'Moot Court': return 'enrolled-mootcourts';
            case 'Essay Writing': return 'enrolled-essaywritings';
            case 'Article Writing': return 'enrolled-articlewritings';
            default: return 'enrolled-debates';
        }
    }, [selectedSubMenu]);

    const resetPagination = useCallback(() => {
        setShowPagination(false);
        setCurrentPage(1);
        setTotalPages(0);
        setCompData([]);
    }, []);

    const handleSubMenuChange = (submenu: string) => {
        setSelectedSubMenu(submenu);
        resetPagination();
    };

    const limit = isLarge ? 6 : 3;
    const getRegisteredComps = useCallback(async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/quiz/${comps}?page=${currentPage}&limit=${limit}`);
            const { total_count, data } = response.data;

            setCompData(data);
            setTotalPages(Math.ceil(total_count / limit));
            setShowPagination(total_count > limit);

        } catch (error) {
            console.error("Get registered Competitions:", error);
        } finally {
            setLoading(false);
        }
    }, [comps, currentPage, submenus]);

    useEffect(() => {
        getRegisteredComps();
    }, [selectedSubMenu, currentPage]);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    useEffect(() => {
        return resetPagination;
    }, [])

    return (
        <View className='flex-1'>

            <BottomSheetHeader name='Competition' onPress={closeModal} />

            <BottomSheetSubheader
                submenus={submenus}
                selectedSubMenu={selectedSubMenu}
                setSelectedSubMenu={handleSubMenuChange}
                handlePaginationChange={handlePaginationChange}
            />

            <ScrollView ref={scrollViewRef} nestedScrollEnabled showsVerticalScrollIndicator={false}>

                <View className={`space-y-3 px-primary`}>

                    <View className='space-y-[2%] mt-3'>
                        {loading ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {[1, 2, 3, 4, 5, 6].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))}
                                </View>
                            ) : (
                                [1, 2, 3,].map((index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <Loader />
                                    </View>
                                ))
                            )
                        ) : compData.length > 0 ? (
                            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                {compData.map((item, index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <CompetitionCard closeModal={closeModal} item={item} selectedSubMenu={selectedSubMenu} />
                                    </View>
                                ))}
                            </View>
                        ) : (
                            <View className='flex-row items-center justify-center w-full h-12'>
                                <Text className='text-text17 text-secondary dark:text-white text-center' style={Global.text_medium}>
                                    You haven't enrolled anything yet...
                                </Text>
                            </View>
                        )}
                    </View>

                    {showPagination && (
                        <View className='mb-10'>
                            <Pagination
                                setCurrentPage={setCurrentPage}
                                currentPage={currentPage}
                                totalPages={totalPages}
                                handlePaginationChange={handlePaginationChange}
                            />
                        </View>
                    )}

                </View>

            </ScrollView>

        </View>
    );
};

export default Competition;
