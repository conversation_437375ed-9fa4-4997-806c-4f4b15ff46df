import React, { useMemo, useCallback, forwardRef, useContext, useState, useEffect, useRef } from 'react';
import { ScrollView, Text, View } from 'react-native';

import BottomSheetHeader from '../components/BottomSheetHeader';
import { ClientContext } from '../../../context/ClientContext';
import BottomSheetSubheader from '../components/BottomSheetSubheader';
import Pagination from '../components/Pagination';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import PurchaseHistoryLoader from './PurchaseHistoryLoader';
import PurchaseHistoryCard from './PurchaseHistoryCard';
import Global from '../../../../globalStyle';

interface PurchaseHistoryProps {
    closeModal: () => void;
}

const PurchaseHistory: React.FC<PurchaseHistoryProps> = ({ closeModal }) => {

    const { clientId, isLarge } = useContext(ClientContext);

    const submenus = useMemo(() => ['All', 'Competitions', 'Events', 'Courses'], []);

    const [selectedSubMenu, setSelectedSubMenu] = useState('All');
    const [purchaseHistoryData, setPurchaseHistoryData] = useState<any[]>([]);

    const resetPagination = useCallback(() => {
        setPurchaseHistoryData([]);
        setCurrentPage(1);
        setTotalPages(0);
        setShowPagination(false);
    }, []);

    const handleSubMenuChange = useCallback((submenu: string) => {
        setSelectedSubMenu(submenu);
        resetPagination();
    }, [resetPagination]);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const limit = isLarge ? 6 : 3;
    const getPurchaseHistory = useCallback(async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/payment/activities/${clientId}/payments?type=${selectedSubMenu.toLowerCase()}&page=${currentPage}&limit=${limit}&sort=latest`);
            const { total_count } = response.data;

            setPurchaseHistoryData(response.data.data);
            setTotalPages(Math.ceil(total_count / limit));
            setShowPagination(total_count > limit);

        } catch (error) {
            console.log("Error fetching purchase data : ", error);
        } finally {
            setLoading(false);
        }
    }, [selectedSubMenu, currentPage]);

    useEffect(() => {
        getPurchaseHistory();
    }, [selectedSubMenu, currentPage]);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    useEffect(() => {
        return resetPagination;
    }, [])

    return (
        <View className='flex-1'>

            <BottomSheetHeader name='Purchase History' onPress={closeModal} />

            <BottomSheetSubheader
                submenus={submenus}
                selectedSubMenu={selectedSubMenu}
                setSelectedSubMenu={handleSubMenuChange}
                handlePaginationChange={handlePaginationChange}
            />

            <ScrollView ref={scrollViewRef} nestedScrollEnabled showsVerticalScrollIndicator={false}>

                <View className={`space-y-3 px-primary`}>

                    <View className='space-y-[2%] mt-3'>
                        {loading ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {[1, 2, 3, 4, 5, 6].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <PurchaseHistoryLoader />
                                        </View>
                                    ))}
                                </View>
                            ) : (
                                [1, 2, 3,].map((index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <PurchaseHistoryLoader />
                                    </View>
                                ))
                            )
                        ) : purchaseHistoryData?.length > 0 ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {purchaseHistoryData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <PurchaseHistoryCard item={item} />
                                        </View>
                                    ))}
                                </View>
                            ) :
                                purchaseHistoryData?.map((item, index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <PurchaseHistoryCard item={item} />
                                    </View>
                                ))
                        ) : (
                            <View className='flex-row items-center justify-center w-full h-12'>
                                <Text className='text-text17 text-secondary dark:text-white text-center' style={Global.text_medium}>
                                    You haven't purchased in anything yet...
                                </Text>
                            </View>
                        )}
                    </View>

                    {showPagination &&
                        <View className='mb-10'>
                            <Pagination
                                setCurrentPage={setCurrentPage}
                                currentPage={currentPage}
                                totalPages={totalPages}
                                handlePaginationChange={handlePaginationChange}
                            />
                        </View>
                    }

                </View>

            </ScrollView>

        </View>
    );
};

export default PurchaseHistory;