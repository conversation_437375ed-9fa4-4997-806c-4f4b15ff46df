import { View, Dimensions } from 'react-native'
import React from 'react'
import { Skeleton } from '@rneui/themed';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { ClientContext } from '../../../context/ClientContext';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

const PurchaseHistoryLoader = () => {

    const { isLarge } = React.useContext(ClientContext);

    return (
        <View className='w-full pt-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='px-2 space-y-3'>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Skeleton
                        LinearGradientComponent={LinearGradient}
                        animation='wave'
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5,
                        }}
                    />
                </View>

                <View className='flex-col items-start w-full space-y-2'>

                    <Skeleton
                        LinearGradientComponent={LinearGradient}
                        animation='wave'
                        style={{
                            height: 15,
                            borderRadius: 2,
                            width: '85%',
                        }}
                    />
                    <Skeleton
                        LinearGradientComponent={LinearGradient}
                        animation='wave'
                        style={{
                            height: 15,
                            borderRadius: 2,
                            width: '60%',
                        }}
                    />
                    <Skeleton
                        LinearGradientComponent={LinearGradient}
                        animation='wave'
                        style={{
                            height: 15,
                            borderRadius: 2,
                            width: '40%',
                        }}
                    />

                </View>

            </View>

            <View className='items-center w-full'>
                <Skeleton
                        LinearGradientComponent={LinearGradient}
                    animation='wave'
                    style={{
                        height: 50,
                        width: '100%',
                    }}
                />
            </View>

        </View>
    )
}

export default PurchaseHistoryLoader