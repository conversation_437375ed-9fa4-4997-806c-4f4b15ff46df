import { View, Text, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import Global from '../../../../globalStyle';
import { formatDateFilter } from '../../../helpers/dateTimeFormat';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { Image } from 'expo-image';
import Badge from '../../../components/Badge';
import { getWishListMenu } from '../../../helpers/getWishlistMenu';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { ActivityIndicator } from 'react-native';
import { handleCertificateDownload } from '../../../helpers/downloadCertificate';
import { Toast } from '../../../components/Toast';
import { ClientContext } from '../../../context/ClientContext';

interface PurchaseCardProps {
    item: any;
}

const PurchaseHistoryCard: React.FC<PurchaseCardProps> = ({ item }) => {

    const [isDownloading, setIsDownloading] = useState(false);
    const { isLarge } = React.useContext(ClientContext);


    const handleDownloadInvoice = async () => {
        try {
            setIsDownloading(true);
            const response = await ClientAxiosInstance.get(`/payment/invoice/${item?._id}/download`, {});
            const url = response.data.s3Url;

            await handleCertificateDownload(item?.Category + ' invoice', url);

        } catch (error) {
            console.log("Purchase Download invoice : ", error);
            Toast.show({
                type: 'error',
                message: 'Failed to download. Please try again later.',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        } finally {
            setIsDownloading(false);
        }
    };

    return (
        <View className='w-full space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='p-2 space-y-3'>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.Thumbnail ? { uri: item?.Thumbnail } : require('../../../assets/images/placeholder-thumbnail.png')}
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5,
                        }}
                    />
                </View>

                <View>
                    <Badge name={getWishListMenu(item?.Category)} />
                </View>

                <Text className='text-lg text-secondary dark:text-white' style={Global.text_bold} numberOfLines={2} ellipsizeMode='tail'>
                    {item?.Title}
                </Text>
                <Text className='text-xs tracking-wide uppercase text-slate-500 dark:text-white' style={Global.text_medium} numberOfLines={1} ellipsizeMode='tail'>
                    By {item?.PostedBy}{item?.Location && `, ${item?.Location}`}
                </Text>

                <Text className='text-base text-secondary dark:text-white' style={Global.text_bold}>
                    {`Purchase on ${formatDateFilter(new Date(item?.Date))}`}
                </Text>

            </View>

            <TouchableOpacity
                activeOpacity={0.5}
                className='justify-center item-center rounded-b-md bg-secondary dark:bg-secondary2'
                onPress={handleDownloadInvoice}
                disabled={isDownloading}
            >
                {isDownloading ? (
                    <View className='flex-row items-center justify-center'>
                        <ActivityIndicator className='p-5' size='small' color='white' />
                    </View>
                ) : (
                    <View className='flex-row items-center justify-center'>
                        <Text className='p-5 text-center text-primary text-text15' style={Global.text_bold}>
                            Download Invoice
                        </Text>
                    </View>
                )}
            </TouchableOpacity>

        </View>
    );
};

export default PurchaseHistoryCard;
