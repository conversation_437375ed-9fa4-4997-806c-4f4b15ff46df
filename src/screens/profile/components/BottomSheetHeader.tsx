import React from 'react';
import { View, Text, Pressable } from 'react-native';
import Global from '../../../../globalStyle';

import Ionicons from '@expo/vector-icons/Ionicons'

interface Props {
    onPress: () => void;
    name: string;
}

const BottomSheetHeader: React.FC<Props> = ({ onPress, name }) => {

    return (
        <View className="w-full h-[75px] flex-row items-center justify-between bg-darkcard">

            <View className='items-end justify-center h-full pl-primary'>
                <Ionicons name='close' size={30} color='#fff' style={{ display: 'none' }} />
            </View>

            <View className='pl-primary'>
                <Text className='text-xl tracking-wider text-white' style={Global.text_bold}>{name}</Text>
            </View>

            <Pressable pressRetentionOffset={40} className='items-end justify-center h-full pr-primary' onPress={onPress}>
                <Ionicons name='close' size={30} color='#fff' />
            </Pressable>

        </View>
    );
};

export default BottomSheetHeader;

