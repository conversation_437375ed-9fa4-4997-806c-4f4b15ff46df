import { View, Text, StyleSheet } from 'react-native'
import React, { useContext, useEffect } from 'react'

import { Controller, UseFormHandleSubmit } from 'react-hook-form';

import { TextInput } from 'react-native-paper';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';

import Button from '../../../components/Button';
import { Dropdown } from 'react-native-element-dropdown';

interface FormProps {
    control: any;
    errors: any;
    handleSubmit: UseFormHandleSubmit<any>;
    onSubmit: (value: any) => void;
}


const EditProfileInfo: React.FC<FormProps> = ({ control, errors, handleSubmit, onSubmit }) => {

    const { colorScheme, userData } = useContext(ClientContext);

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label} ({item.name})
                </Text>
            </View>
        );
    };

    return (
        <View className='pb-24 space-y-5'>

            <View className='space-y-2'>
                <Text className='tracking-wider text-text17 text-secondary dark:text-white' style={Global.text_medium}>First Name</Text>
                <View>
                    <Controller
                        control={control}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                contentStyle={{
                                    fontFamily: 'DMSans-Regular'
                                }}
                                style={{ borderColor: errors?.fname?.message ? 'red' : '#a8a8a8' }}
                                className='w-full px-1 tracking-wide bg-transparent text-text17'
                                placeholder='First Name'
                                placeholderTextColor='#a8a8a8'
                                inputMode='text'
                                onChangeText={onChange}
                                onBlur={onBlur}
                                value={value}
                                error={!!errors?.fname?.message}
                                mode='outlined'
                                activeOutlineColor='#a8a8a8'
                                outlineStyle={{
                                    borderWidth: 1.2,
                                    borderRadius: 8
                                }}
                                outlineColor='#a8a8a8'
                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                            />
                        )}
                        name="fname"
                        defaultValue={userData?.first_name}
                    />
                    {errors?.fname?.message && (
                        <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.fname?.message}</Text>
                    )}
                </View>
            </View>

            <View className='space-y-2'>
                <Text className='tracking-wider text-text17 text-secondary dark:text-white' style={Global.text_medium}>Last Name</Text>
                <View>
                    <Controller
                        control={control}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                contentStyle={{
                                    fontFamily: 'DMSans-Regular'
                                }}
                                className='w-full px-1 bg-transparent text-text17'
                                placeholder='Last Name'
                                placeholderTextColor='#a8a8a8'
                                inputMode='text'
                                onChangeText={onChange}
                                onBlur={onBlur}
                                value={value}
                                error={!!errors?.lname?.message}
                                mode='outlined'
                                activeOutlineColor='#a8a8a8'
                                outlineStyle={{
                                    borderWidth: 1.2,
                                    borderRadius: 8
                                }}
                                outlineColor='#a8a8a8'
                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                            />
                        )}
                        name="lname"
                        defaultValue={userData?.last_name}
                    />
                    {errors?.lname?.message && (
                        <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.lname?.message}</Text>
                    )}
                </View>
            </View>

            <View className='space-y-2'>
                <Text className='tracking-wider text-text17 text-secondary dark:text-white' style={Global.text_medium}>E-Mail</Text>
                <View>
                    <TextInput
                        contentStyle={{
                            fontFamily: 'DMSans-Regular'
                        }}
                        className='w-full bg-transparent dark:border-[1px] dark:border-[#a8a8a880] dark:rounded-lg text-text17 px-1 tracking-wide'
                        placeholder={userData?.email}
                        placeholderTextColor={'#a8a8a8'}
                        inputMode='email'
                        autoCapitalize='none'
                        mode='outlined'
                        activeOutlineColor='#a8a8a8'
                        disabled
                        outlineStyle={{
                            borderWidth: 1.2,
                            borderRadius: 8
                        }}
                        outlineColor='#a8a8a8'
                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                    />
                </View>
            </View>

            <View className='space-y-2'>

                <Text className='tracking-wider text-text17 text-secondary dark:text-white' style={Global.text_medium}>Phone Number</Text>
                <View>
                    <View className='flex-row items-center'>

                        <View className='w-full'>
                            <Controller
                                control={control}
                                name="country_code"
                                defaultValue='91'
                                render={({ field: { onChange, value } }) => (
                                    <Dropdown
                                        style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: '#a8a8a850' }]}
                                        selectedTextStyle={{
                                            color: colorScheme === 'light' ? '#000' : '#fff',
                                        }}
                                        data={[]}
                                        dropdownPosition='top'
                                        search
                                        searchField='name'
                                        searchPlaceholder='Search Country Code'
                                        inputSearchStyle={{
                                            borderRightWidth: 0,
                                            borderLeftWidth: 0,
                                            borderTopWidth: 0
                                        }}
                                        labelField="label"
                                        valueField="value"
                                        renderItem={renderItem}
                                        disable
                                        onChange={item => {
                                            onChange(item.value);
                                        }}
                                        itemContainerStyle={{
                                            borderBottomWidth: 0.5,
                                            borderBottomColor: '#a8a8a8',
                                            backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                        }}
                                        itemTextStyle={{
                                            color: colorScheme === 'light' ? '#000' : '#fff',
                                            fontSize: 17
                                        }}
                                        containerStyle={{
                                            borderRadius: 8,
                                            overflow: 'hidden',
                                            backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                        }}
                                        showsVerticalScrollIndicator={false}
                                        placeholder="(+91)"
                                        placeholderStyle={{
                                            color: '#a8a8a8'
                                        }}
                                        keyboardAvoiding
                                        fontFamily='DMSans-Regular'
                                        value={value}
                                    />
                                )}
                            />
                        </View>

                        <View className='w-[75%] absolute right-0'>
                            <TextInput
                                contentStyle={{
                                    fontFamily: 'DMSans-Regular'
                                }}
                                className='w-full bg-transparent border-r-[1px] border-[#a8a8a880] rounded-lg text-text17 tracking-wider'
                                placeholder={userData?.mobile}
                                placeholderTextColor={'#a8a8a8'}
                                inputMode='numeric'
                                maxLength={10}
                                mode='outlined'
                                activeOutlineColor='#a8a8a8'
                                disabled
                                outlineStyle={{
                                    borderTopWidth: 0,
                                    borderBottomWidth: 0,
                                    borderRightWidth: 1.2,
                                    borderLeftWidth: 0,
                                    borderTopRightRadius: 8,
                                    borderBottomRightRadius: 8,
                                    borderTopLeftRadius: 0,
                                    borderBottomLeftRadius: 0,
                                }}
                                outlineColor='#a8a8a8'
                                textColor={colorScheme === 'dark' ? '#ffffff' : 'black'}
                            />
                        </View>

                    </View>

                    {errors?.mobile?.message && (
                        <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.mobile?.message}</Text>
                    )}
                </View>

            </View>

            <View className='items-center'>
                <Button title='Submit Details' onPress={handleSubmit(onSubmit)} />
            </View>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: 56,
        borderColor: '#a8a8a8',
        borderLeftWidth: 1.2,
        borderTopWidth: 1.2,
        borderBottomWidth: 1.2,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: '75%',
        overflow: 'hidden',
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default EditProfileInfo