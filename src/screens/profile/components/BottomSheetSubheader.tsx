import { View, Text, TouchableOpacity, Dimensions, FlatList } from 'react-native'
import React, { useContext, useRef } from 'react';
import Global from '../../../../globalStyle';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface SubheaderProps {
    setSelectedSubMenu: (value: string) => void;
    selectedSubMenu: string;
    submenus: string[];
    handlePaginationChange: () => void;
}

const BottomSheetSubheader: React.FC<SubheaderProps> = ({ setSelectedSubMenu, handlePaginationChange, selectedSubMenu, submenus }) => {

    const ref = useRef<FlatList>(null);

    const handleSelectedMenu = (item: string) => {
        setSelectedSubMenu(item);
        handlePaginationChange();
    };

    const renderItem = ({ item, index }: { item: string, index: number }) => {
        if (!item) return null;
        else return (
            <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => handleSelectedMenu(item)}
                className='h-full items-center justify-center'
                style={{ marginLeft: leftMargin, marginRight: index === submenus.length - 1 ? leftMargin : 0 }}
            >
                <Text
                    style={Global.text_bold}
                    className={`text-secondary tracking-wide text-text17 ${selectedSubMenu === item && 'underline'}`}
                >
                    {item}
                </Text>
            </TouchableOpacity>
        )
    };

    return (
        <View className='h-[60px] w-full bg-cardLight'>
            <FlatList
                ref={ref}
                horizontal
                data={submenus}
                renderItem={renderItem}
                keyExtractor={(item) => item.toString()}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    )
}

export default BottomSheetSubheader