import { View, Text, TouchableOpacity } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Toast } from '../../../components/Toast';
import { ActivityIndicator } from 'react-native';
import { Image } from 'expo-image';

interface ProfileCardProps {
    openEditProfile: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ openEditProfile }) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { token, userData, setUserData } = useContext(ClientContext);
    const [loading, setLoading] = useState(false);

    const handleChangeProfilePicture = async () => {
        try {
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (permissionResult.granted === false) {
                alert('Permission to access camera roll is required!');
                return;
            }

            const imageResult = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [1, 1],
                quality: 1,
            });

            if (imageResult.canceled) {
                Toast.show({
                    type: 'warning',
                    message: 'No image selected!',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
                return;
            }

            if (imageResult.assets && imageResult.assets.length > 0) {

                const selectedImage = imageResult.assets[0];
                const userId = await AsyncStorage.getItem('LAWCUBE_USERID');

                const formData = new FormData();
                formData.append('user_profile', {
                    uri: selectedImage.uri,
                    type: 'image/jpeg',
                    name: 'profile_image.jpg',
                });

                setLoading(true);

                const response = await axios.post(
                    `${api_link}/auth/upload/${userId}`,
                    formData,
                    {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                setUserData({ ...userData, user_profile: response.data?.user?.user_profile });

                Toast.show({
                    type: 'success',
                    message: response.data?.message,
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });

            } else {
                Toast.show({
                    type: 'warning',
                    message: "No image Selected!",
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }

        } catch (error: any) {
            console.log('Error updating profile image:', error);
            Toast.show({
                type: 'error',
                message: error.response?.message || 'Error uploading image.',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        } finally {
            setLoading(false);
        }
    };

    const getRegistrationDate = (date: string): string => {
        const d = new Date(date);
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const year = d.getFullYear();
        return `${day}-${month}-${year}`;
    };

    const data = [
        {
            name: 'Name',
            value: `${userData?.first_name} ${userData?.last_name}`,
        },
        {
            name: 'Registration Date',
            value: getRegistrationDate(userData?.registration_date),
        },
        {
            name: 'E-mail',
            value: userData?.email,
        },
        {
            name: 'Phone Number',
            value: userData?.mobile,
        },
    ];

    return (
        <View className="w-full space-y-6 px-primary">

            <View className="items-center self-center justify-center w-40 h-40 overflow-hidden rounded-full bg-secondary">
                {loading ? (
                    <View className='absolute top-0 bottom-0 left-0 right-0 items-center justify-center bg-black/50'>
                        <ActivityIndicator size='large' color='#FDD066' />
                    </View>
                ) : userData?.user_profile ? (
                    <Image
                        source={{ uri: userData?.user_profile }}
                        style={{ height: '100%', width: '100%' }}
                        contentFit="cover"
                    />
                ) : (
                    <View className="items-center w-full h-3/4 justify-evenly">
                        <Image
                            source={require('../../../assets/images/placeholder-image.png')}
                            style={{ height: 80, width: 80 }}
                            contentFit="cover"
                        />
                        <TouchableOpacity activeOpacity={0.8} onPress={handleChangeProfilePicture}>
                            <Text className="underline text-slate-300" style={Global.text_medium}>Upload image</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>

            <View className="space-y-3">
                {data.map((item, index) => (
                    <View className="flex-row items-center w-full" key={index}>
                        <View className="flex-row items-center justify-between w-1/2">
                            <Text style={Global.text_bold} className="text-secondary w-[90%] dark:text-white text-text17 tracking-wide" numberOfLines={1} ellipsizeMode="tail">{item?.name}</Text>
                            <Text style={Global.text_bold} className="text-secondary dark:text-white text-text17">:</Text>
                        </View>
                        <Text style={Global.text_medium} className="w-1/2 pl-[6px] text-secondary dark:text-white text-text17" numberOfLines={1} ellipsizeMode="tail">{item?.value}</Text>
                    </View>
                ))}
            </View>

            <View className="w-full">
                <TouchableOpacity
                    onPress={openEditProfile}
                    activeOpacity={0.8}
                    className="bg-[#2F2F2F] h-16 rounded-md flex-row items-center justify-center space-x-2"
                >
                    <FontAwesome6 name="pen" size={17} color="#fdd066" />
                    <Text className="tracking-wide underline text-text16 text-primary" style={Global.text_medium}>Edit Details</Text>
                </TouchableOpacity>
            </View>

        </View>
    );
};

export default ProfileCard;
