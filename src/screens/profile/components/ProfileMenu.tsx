import { View, Text, TouchableOpacity, Modal } from 'react-native'
import React, { useContext, useState } from 'react'
import Global from '../../../../globalStyle'
import { Image } from 'expo-image'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'
import { ClientContext } from '../../../context/ClientContext'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Toast } from '../../../components/Toast'
import { LikeSaveContext } from '../../../context/LikeSaveContext'

interface ProfileMenuCardProps {
    openWishList: () => void;
    openEnrolled: () => void;
    openCompetition: () => void;
    openCareer: () => void;
    openPurchaseHistory: () => void;
}

const ProfileMenu: React.FC<ProfileMenuCardProps> = (props) => {

    const { colorScheme, navigation, setToken, setClientId } = useContext(ClientContext);
    const { setRegisteredItems } = useContext(LikeSaveContext);
    const [showModal, setShowModal] = useState(false);

    const menus = ['Enrolled', 'Competition', 'Career', 'Wishlist', 'Purchase History'];

    const handleMenuClick = (index: number) => {
        if (index === 0) props.openEnrolled();
        if (index === 1) props.openCompetition();
        if (index === 2) props.openCareer();
        if (index === 3) props.openWishList();
        if (index === 4) props.openPurchaseHistory();
    };

    const handleLogout = async () => {
        setToken(null);
        setClientId(null);
        setRegisteredItems(null);
        await AsyncStorage.removeItem('LAWCUBE_TOKEN');
        await AsyncStorage.removeItem('LAWCUBE_USERID');

        Toast.show({
            type: 'success',
            message: 'Logout successful',
            duration: 3000,
            position: 'bottom',
            animation: 'slide',
            icon: (
                <Image
                    source={require('../../../assets/icons/logo/logo-light-big.png')}
                    style={{ width: 24, height: 24 }}
                    contentFit='contain'
                />
            ),
        });
    };

    return (
        <View className='px-primary'>

            {menus.map((menu, index) => (
                <TouchableOpacity
                    key={index}
                    onPress={() => handleMenuClick(index)}
                    activeOpacity={0.8}
                    className='w-full border-b-[0.7px] border-[#a8a8a8] h-[70px] flex-row justify-between items-center'
                >
                    <Text className='text-text17 text-secondary dark:text-white' style={Global.text_bold}>{menu}</Text>
                    <SimpleLineIcons name='arrow-right' size={16} color={colorScheme === 'dark' ? 'white' : '#2d2828'} />
                </TouchableOpacity>
            ))}

            <TouchableOpacity
                onPress={() => navigation.navigate('ChangePassword')}
                activeOpacity={0.8}
                className='w-full border-b-[0.7px] border-[#a8a8a8] h-[70px] flex-row justify-between items-center'
            >
                <Text className='text-text17 text-secondary dark:text-white' style={Global.text_bold}>Change Password</Text>
                <SimpleLineIcons name='arrow-right' size={16} color={colorScheme === 'dark' ? 'white' : '#2d2828'} />
            </TouchableOpacity>

            <TouchableOpacity
                onPress={() => setShowModal(true)}
                activeOpacity={0.8}
                className='w-full h-[70px] flex-row justify-between items-center'
            >
                <Text className='text-text17 text-secondary dark:text-white' style={Global.text_bold}>Logout</Text>
                <SimpleLineIcons name='arrow-right' size={16} color={colorScheme === 'dark' ? 'white' : '#2d2828'} />
            </TouchableOpacity>

            <Modal
                animationType='slide'
                statusBarTranslucent={true}
                transparent={true}
                visible={showModal}
                onRequestClose={() => setShowModal(false)}>
                <View className='items-center justify-center flex-1 bg-opacity-50 bg-black/30'>
                    <View className='p-5 space-y-3 bg-white rounded-lg shadow-lg w-80 dark:bg-dark'>
                        <Text style={Global.text_bold} className='mb-4 text-lg text-center text-secondary dark:text-white'>
                            Are you sure you want to log out?
                        </Text>
                        <View className='flex-row justify-between px-4'>
                            <TouchableOpacity className='px-4 py-2 bg-gray-300 rounded' onPress={() => setShowModal(false)}>
                                <Text style={Global.text_medium} className='text-secondary text-text15'>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity className='px-4 py-2 bg-[#FF2020] rounded' onPress={handleLogout}>
                                <Text style={Global.text_medium} className='text-white text-text15'>Confirm</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>

        </View>
    )
}

export default ProfileMenu
