import { View, Text, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import React, { useContext, useState } from 'react';
import { Image } from 'expo-image'
import Feather from '@expo/vector-icons/Feather';
import Global from '../../../../globalStyle';

import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

import { ClientContext } from '../../../context/ClientContext';
import { Toast } from '../../../components/Toast';

const EditProfileCard = () => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { colorScheme, token, userData, setUserData } = useContext(ClientContext);
    const [loading, setLoading] = useState(false);

    const handleChangeProfilePicture = async () => {
        try {
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

            if (permissionResult.granted === false) {
                alert('Permission to access camera roll is required!');
                return;
            }

            const imageResult = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [1, 1],
                quality: 1,
            });

            if (imageResult.assets && imageResult.assets.length > 0) {
                const selectedImage = imageResult.assets[0];
                setLoading(true);

                const userId = await AsyncStorage.getItem('LAWCUBE_USERID');

                const formData = new FormData();
                formData.append('user_profile', {
                    uri: selectedImage.uri,
                    type: 'image/jpeg',
                    name: 'profile_image.jpg',
                });

                const response = await axios.post(
                    `${api_link}/auth/upload/${userId}`,
                    formData,
                    {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                Toast.show({
                    type: 'success',
                    message: response.data?.message,
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
                setLoading(false);
                setUserData({ ...userData, user_profile: response.data?.user?.user_profile });

            } else {
                Toast.show({
                    type: 'warning',
                    message: 'No image selected!',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }
        } catch (error: any) {
            console.log('Error updating profile image:', error.response.data);
            setLoading(false);
            Toast.show({
                type: 'error',
                message: 'Error updating profile image',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
        }
    };

    return (
        <View className='space-y-3'>

            <Text className='tracking-wide text-text17 text-secondary dark:text-white' style={Global.text_bold}>Profile Photo</Text>

            <View className='items-center w-full'>
                <View className='items-center self-center justify-center w-40 h-40 overflow-hidden rounded-full'>
                    {userData?.user_profile ? (
                        <Image
                            source={{ uri: userData?.user_profile }}
                            style={{ height: '100%', width: '100%' }}
                            contentFit='cover'
                        />
                    ) : (
                        <View className='items-center justify-center w-full h-full rounded-full bg-secondary dark:bg-dark'>
                            <Image
                                source={require('../../../assets/images/placeholder-image.png')}
                                style={{ height: '60%', width: '60%' }}
                                contentFit='cover'
                            />
                        </View>
                    )}
                    {loading && (
                        <View className='absolute top-0 bottom-0 left-0 right-0 items-center justify-center  bg-black/50'>
                            <ActivityIndicator size='large' color='#FDD066' />
                        </View>
                    )}
                </View>
            </View>

            <View className='items-center w-full'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-2' onPress={handleChangeProfilePicture}>
                    <Feather name='upload' size={20} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                    <Text className='underline text-secondary dark:text-white text-text16' style={Global.text_bold}>Upload Image</Text>
                </TouchableOpacity>
            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
});

export default EditProfileCard;
