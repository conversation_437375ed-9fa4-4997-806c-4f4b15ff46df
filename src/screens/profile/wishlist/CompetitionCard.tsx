import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'

import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import { getDate, getQuizDate, separateTimeFromDateTime } from '../../../helpers/dateTimeFormat'
import Badge from '../../../components/Badge'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { getWishListMenu } from '../../../helpers/getWishlistMenu'
import { capitalizeMode } from '../../../helpers/getCapitalize'
import { GlobalContext } from '../../../context/GlobalProvider'
import { ClientAxiosInstance } from '../../../lib/axiosInstance'

interface CompetitionCardProps {
    item: any;
    closeModal: () => void;
}

const CompetitionCard: React.FC<CompetitionCardProps> = ({ item, closeModal }) => {

    const { colorScheme, navigation, setCurrentCompChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, handleView } = useContext(LikeSaveContext);
    const { setLoading } = useContext(GlobalContext);
    let selectedSubMenu = getWishListMenu(item?.itemType);

    const handleQuiz = async () => {
        setCurrentCompChip(selectedSubMenu);
        closeModal();
        if (selectedSubMenu === 'Quiz') {
            const data = await getCurrentCompetition();
            navigation.navigate('CompetitionDetails', { currentCompetition: data });
        }
        else navigation.navigate('CompetitionDetails', { currentCompetition: item });
        handleView(item?._id, selectedSubMenu);
    }

    const getCurrentCompetition = async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/quiz/${item?._id}?live=true`);
            return response.data.data;

        } catch (error: any) {
            console.log('Competition details error : ', error.response.data);
            return {};
        } finally {
            setLoading(false);
        }
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleQuiz}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[52%]'><Badge height={30} name={selectedSubMenu} /></View>

                </View>

                <View className='flex-row items-start justify-between'>

                    <View className='w-[52%] space-y-2'>
                        <Text
                            className='text-lg text-secondary dark:text-white'
                            style={Global.text_bold}
                            numberOfLines={2}
                            ellipsizeMode='tail'
                        >
                            {item?.title}
                        </Text>
                        <Text
                            className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                            style={Global.text_medium}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            By {item?.posted_by}
                        </Text>
                    </View>

                    <View className='w-[45%] space-y-3'>

                        {(selectedSubMenu === 'Essay Writing' || selectedSubMenu === 'Article Writing') ? null : <View className='flex-row items-center space-x-2'>
                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>{selectedSubMenu === 'Quiz' ? getQuizDate(item?.date_time) : getDate(item?.date_time)}</Text>
                        </View>}

                        <View className='flex-row items-center space-x-2'>
                            {selectedSubMenu === 'Essay Writing' || selectedSubMenu === 'Article Writing' ?
                                <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} /> :
                                <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            }
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>
                                {selectedSubMenu === 'Essay Writing' || selectedSubMenu === 'Article Writing' ? getDate(item?.submission_date) : (item?.start_time || separateTimeFromDateTime(item?.date_time))}
                            </Text>
                        </View>

                        {selectedSubMenu !== 'Quiz' &&
                            item?.mode?.toLowerCase() === 'offline' ? (
                            <View className='flex-row items-center space-x-2'>
                                <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' style={Global.text_medium}>{capitalizeMode(item?.location)}</Text>
                            </View>
                        ) : selectedSubMenu === 'Essay Writing' || selectedSubMenu === 'Article Writing' ? (
                            <View className='flex-row items-center w-full space-x-2'>
                                <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>Online Submission</Text>
                            </View>
                        ) : selectedSubMenu !== 'Quiz' && <View className='flex-row items-center w-full space-x-2'>
                            <SimpleLineIcons name='globe' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>Online</Text>
                        </View>}

                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full h-10 mb-1'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, getWishListMenu(item.itemType))}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default CompetitionCard;