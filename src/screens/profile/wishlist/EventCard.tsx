import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'

import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'

import { ClientContext } from '../../../context/ClientContext'
import Badge from '../../../components/Badge'
import Global from '../../../../globalStyle'
import { getDate, separateTimeFromDateTime } from '../../../helpers/dateTimeFormat'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { getWishListMenu } from '../../../helpers/getWishlistMenu'
import { Image } from 'expo-image';

interface EventCardProps {
    item: any;
    closeModal: () => void;
}

const EventCard: React.FC<EventCardProps> = ({ item, closeModal }) => {

    const { colorScheme, navigation, setCurrentEventChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard } = useContext(LikeSaveContext);

    const handleEvent = () => {
        closeModal();
        setCurrentEventChip(getWishListMenu(item?.itemType));
        navigation.navigate('EventDetails', { currentEvent: item })
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleEvent}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />

                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[52%]'><Badge height={30} name={getWishListMenu(item?.itemType)} /></View>

                </View>

                <View className='flex-row items-start justify-between'>

                    <View className='w-[55%] space-y-2'>

                        <Text
                            className='text-lg text-secondary dark:text-white'
                            style={Global.text_bold}
                            numberOfLines={3}
                            ellipsizeMode='tail'
                        >
                            {item?.title}
                        </Text><Text
                            className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                            style={Global.text_medium}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            By {item?.posted_by}
                        </Text>

                    </View>

                    <View className='w-[42%] space-y-3'>

                        <View className='flex-row items-center space-x-2'>
                            <AntDesign name='calendar' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>{getDate(item?.date_time)}</Text>
                        </View>

                        <View className='flex-row items-center space-x-2'>
                            <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>
                                {separateTimeFromDateTime(item?.date_time)}
                            </Text>
                        </View>

                        <View className='flex-row items-center space-x-2'>
                            <Feather name='users' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>No. of Seats : {item?.number_of_seats}</Text>

                        </View>

                        {item?.location && <View className='flex-row items-center space-x-2'>
                            <SimpleLineIcons name='location-pin' size={19} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            <Text className='w-[75%] text-text14 text-secondary dark:text-white tracking-wide' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>{item?.location}</Text>
                        </View>}

                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full h-10 mb-1'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, '')}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default EventCard;