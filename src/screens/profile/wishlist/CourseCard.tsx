import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import Ionicons from '@expo/vector-icons/Ionicons'

import Badge from '../../../components/Badge'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import { LikeSaveContext } from '../../../context/LikeSaveContext'

import { Feather } from '@expo/vector-icons'

import { StarRatingDisplay } from 'react-native-star-rating-widget'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { getTotalDuration } from '../../../helpers/calculateTime'

interface CourseCardProps {
    item: any;
    closeModal: () => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ item, closeModal }) => {

    const { colorScheme, navigation, registeredCourses, setCurrentCourseChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, registeredItems, handleView } = useContext(LikeSaveContext);

    const handleCourse = () => {
        closeModal();
        if (isPaymentCompleted())
            navigation.navigate('BookedCourse', { currentCourse: item });
        else {
            setCurrentCourseChip(item?.course_level === 'Expert' ? 'Advanced' : item?.course_level);
            navigation.navigate('CourseDetails', { currentCourse: item });
            handleView(item?._id, item?.course_level);
        }
    }

    const isPaymentCompleted = () => {
        const isRegisteredNow = registeredItems?.has(item?._id);
        const isRegisteredPreviously = registeredCourses?.includes(item?._id);
        return isRegisteredNow || isRegisteredPreviously;
    }

    return (
        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleCourse}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                </View>

                <View>
                    <Badge name={item?.course_level === 'Expert' ? 'Advanced' : item?.course_level} />
                </View>

                <View className='w-full space-y-2'>
                    <Text
                        className='text-lg text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={2}
                        ellipsizeMode='tail'
                    >
                        {item?.title}
                    </Text>
                    <Text
                        className='text-xs tracking-wide uppercase text-slate-500 dark:text-white'
                        style={Global.text_medium}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        By {item?.posted_by}
                    </Text>
                </View>

                <View className='flex-row items-center justify-between'>

                    {item?.average_rating > 0 && <View className='flex-row items-center space-x-1'>
                        <Text
                            className='text-text15 text-slate-500 dark:text-white'
                            style={Global.text_medium}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            ({item?.average_rating?.toFixed(1)})
                        </Text>
                        <StarRatingDisplay
                            rating={item?.average_rating}
                            maxStars={5}
                            starSize={20}
                            color='#fdd066'
                            emptyColor={colorScheme === 'dark' ? '#fff' : '#64748b'}
                            style={{ padding: 0 }}
                        />
                    </View>}

                    <View className='flex-row items-center space-x-2'>
                        <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                        <Text className='tracking-wide text-text14 text-secondary dark:text-white' style={Global.text_medium}>
                            {getTotalDuration(item?.topics)}
                        </Text>
                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full mb-2'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(item._id, item?.course_level)}>
                    <Ionicons name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'} />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default CourseCard;
