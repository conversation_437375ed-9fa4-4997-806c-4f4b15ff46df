import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Global from '../../../../globalStyle';
import Badge from '../../../components/Badge';
import { ClientContext } from '../../../context/ClientContext';
import { Feather } from '@expo/vector-icons';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import { formatDate } from '../../../helpers/dateTimeFormat';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { getWishListMenu } from '../../../helpers/getWishlistMenu';
import { Image } from 'expo-image';

interface ReadingsCardProps {
    item: any;
    closeModal: () => void;
}

const ReadingsCard: React.FC<ReadingsCardProps> = ({ item, closeModal }) => {

    const { colorScheme, navigation, setCurrentReadingsChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, handleView } = useContext(LikeSaveContext);

    let currentReadingsChip = getWishListMenu(item?.itemType);

    const handleReadingsCard = () => {
        closeModal();
        setCurrentReadingsChip(currentReadingsChip)
        handleView(item?._id, currentReadingsChip);
        navigation.navigate('ReadingDetail', { item: item, selectedChipData: [item] });
    };

    function calculateReadTime(content: string): number {
        const plainText = content?.replace(/<\/?[^>]+(>|$)/g, "");
        const wordCount = plainText?.split(/\s+/)?.filter(Boolean)?.length;
        const wordsPerMinute = 150;
        const readTime = Math.ceil(wordCount / wordsPerMinute);
        return readTime;
    };

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='space-y-3' onPress={handleReadingsCard}>

                <View>
                    <Image className='sm:h-3/4'
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50),
                            width: '100%',
                            borderRadius: 6,
                        }}
                    />
                </View>

                <View>
                    <Badge name={currentReadingsChip} />
                </View>

                <Text
                    style={Global.text_bold}
                    className='text-text15 text-secondary dark:text-white'
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {item?.article_title || item?.news_title || item?.blog_title || item?.journal_title}
                </Text>

                <View className='flex-row items-center space-x-3'>

                    <View>
                        <Badge name={`${calculateReadTime(item?.news_content || item?.blog_content || item?.article_content || item?.journal_content)} Min Read`} />
                    </View>


                    <View className='flex-row items-center space-x-2'>
                        <Ionicons name="eye-outline" size={20} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                        <Text style={Global.text_medium} ellipsizeMode='tail' numberOfLines={1} className='text-text14 text-secondary dark:text-white'>{item?.views_count} Views</Text>
                    </View>

                </View>

                <View className='flex-row items-center justify-between w-full'>

                    <View className='w-[50%] flex-row items-center space-x-2 pr-1'>
                        {item?.author_image_path ? (
                            <Image source={{ uri: item?.author_image_path }} style={styles.authorImage} contentFit='cover' />
                        ) :
                            <View className='w-7 h-7 rounded-full items-center justify-center bg-[#a8a8a850]'>
                                <Feather name="user" size={17} color="black" />
                            </View>
                        }
                        <Text style={Global.text_bold} ellipsizeMode='tail' numberOfLines={1} className='text-black dark:text-white'>{capitalizeMode(item?.author_name)}</Text>
                    </View>

                    <View className='w-[50%] items-end pl-1'>
                        <Text style={Global.text_medium} ellipsizeMode='tail' numberOfLines={1} className='text-black dark:text-white'>{formatDate(item.createdAt)}</Text>
                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(item._id, currentReadingsChip)}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    authorImage: {
        width: 25,
        height: 25,
        borderRadius: 15,
    },
    saveButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
});

export default ReadingsCard;

