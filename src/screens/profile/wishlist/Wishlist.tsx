import React, { useMemo, useCallback, useContext, useState, useEffect, useRef } from 'react';
import { ScrollView, Text, View } from 'react-native';

import BottomSheetHeader from '../components/BottomSheetHeader';
import { ClientContext } from '../../../context/ClientContext';
import BottomSheetSubheader from '../components/BottomSheetSubheader';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import Pagination from '../components/Pagination';
import Global from '../../../../globalStyle';
import CompetitionCard from './CompetitionCard';
import EventCard from './EventCard';
import CompetitionLoader from './CompetitionLoader';
import EventLoader from './EventLoader';
import ReadingsLoader from './ReadingsLoader';
import ReadingsCard from './ReadingsCard';
import CareerLoader from './CareerLoader';
import CareerCard from './CareerCard';
import CourseLoader from './CourseLoader';
import CourseCard from './CourseCard';

interface WishlistProps {
    closeModal: () => void;
}

const Wishlist: React.FC<WishlistProps> = ({ closeModal }) => {

    const { isLarge } = useContext(ClientContext);

    const submenus = useMemo(() => ['Competitions', 'Events', 'Readings', 'Careers', 'Courses'], []);

    const [selectedSubMenu, setSelectedSubMenu] = useState('Competitions');
    const [wishlistData, setWishlistData] = useState<any[]>([]);

    const resetPagination = useCallback(() => {
        setWishlistData([]);
        setCurrentPage(1);
        setTotalPages(0);
        setShowPagination(false);
    }, []);

    const handleSubMenuChange = useCallback((submenu: string) => {
        setSelectedSubMenu(submenu);
        resetPagination();
    }, [resetPagination]);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const limit = isLarge ? 6 : 3;
    const getWishlistItems = useCallback(async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/auth/saveditem/${selectedSubMenu.toLowerCase()}?page=${currentPage}&limit=${limit}`);
            const { total_count } = response.data;

            setWishlistData(response.data[selectedSubMenu.toLowerCase()]);
            setTotalPages(Math.ceil(total_count / limit));
            setShowPagination(total_count > limit);

        } catch (error) {
            console.log("Get saved items : ", error);
        } finally {
            setLoading(false);
        }
    }, [selectedSubMenu, currentPage]);

    useEffect(() => {
        getWishlistItems();
    }, [selectedSubMenu, currentPage]);

    const renderDataCard = (item: any) => {
        switch (selectedSubMenu) {
            case 'Competitions':
                return <CompetitionCard closeModal={closeModal} item={item} />
            case 'Events':
                return <EventCard closeModal={closeModal} item={item} />
            case 'Readings':
                return <ReadingsCard closeModal={closeModal} item={item} />
            case 'Careers':
                return <CareerCard closeModal={closeModal} item={item} />
            case 'Courses':
                return <CourseCard closeModal={closeModal} item={item} />
            default:
                break;
        }
    }

    const renderLoaderCard = () => {
        switch (selectedSubMenu) {
            case 'Competitions':
                return <CompetitionLoader />
            case 'Events':
                return <EventLoader />
            case 'Readings':
                return <ReadingsLoader />
            case 'Careers':
                return <CareerLoader />
            case 'Courses':
                return <CourseLoader />
            default:
                break;
        }
    }

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    useEffect(() => {
        return resetPagination;
    }, [])

    return (
        <View className='flex-1'>

            <BottomSheetHeader name='Wishlist' onPress={closeModal} />

            <BottomSheetSubheader
                submenus={submenus}
                selectedSubMenu={selectedSubMenu}
                setSelectedSubMenu={handleSubMenuChange}
                handlePaginationChange={handlePaginationChange}
            />

            <ScrollView ref={scrollViewRef} nestedScrollEnabled showsVerticalScrollIndicator={false}>

                <View className={`space-y-3 px-primary`}>

                    <View className='space-y-[2%] mt-3'>
                        {loading ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {[1, 2, 3, 4, 5, 6].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            {renderLoaderCard()}
                                        </View>
                                    ))}
                                </View>
                            ) : (
                                [1, 2, 3,].map((index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        {renderLoaderCard()}
                                    </View>
                                ))
                            )
                        ) : wishlistData?.length > 0 ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {wishlistData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            {renderDataCard(item)}
                                        </View>
                                    ))}
                                </View>
                            ) : wishlistData?.map((item, index) => (
                                <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                    {renderDataCard(item)}
                                </View>
                            ))
                        ) : (
                            <View className='flex-row items-center justify-center w-full h-12'>
                                <Text className='text-text17 text-secondary dark:text-white text-center' style={Global.text_medium}>
                                    Your wishlist is currently empty...
                                </Text>
                            </View>
                        )}
                    </View>

                    {showPagination &&
                        <View className='mb-10'>
                            <Pagination
                                setCurrentPage={setCurrentPage}
                                currentPage={currentPage}
                                totalPages={totalPages}
                                handlePaginationChange={handlePaginationChange}
                            />
                        </View>
                    }

                </View>

            </ScrollView>

        </View>
    );
};

export default Wishlist;