import { View, Text, Dimensions } from 'react-native'
import React from 'react'
import { Skeleton } from '@rneui/themed'
import { LinearGradient } from 'expo-linear-gradient'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { ClientContext } from '../../../context/ClientContext'

const ReadingsLoader = () => {

    const { isLarge } = React.useContext(ClientContext);

    return (
        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='space-y-3'>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Skeleton animation='wave'
                        LinearGradientComponent={LinearGradient}
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                    />
                </View>

                <View className='w-[100%] space-y-2'>
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 15,
                        borderRadius: 2,
                        width: '85%'
                    }} />
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 15,
                        borderRadius: 2,
                        width: '60%'
                    }} />
                </View>

                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                    height: 25,
                    borderRadius: 25,
                    width: '30%'
                }} />

            </View>

            <View className='flex-row items-center justify-between w-full'>

                <View className='w-[60%] flex-row items-center space-x-2'>
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 27,
                        borderRadius: 25,
                        width: 28
                    }} />
                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 20,
                        borderRadius: 25,
                        width: '70%'
                    }} />
                </View>
                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                    height: 20,
                    borderRadius: 25,
                    width: '30%'
                }} />
            </View>

            <View className='items-center w-full'>
                <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                    height: 20,
                    borderRadius: 25,
                    width: '60%'
                }} />
            </View>

        </View>
    )
}

export default ReadingsLoader