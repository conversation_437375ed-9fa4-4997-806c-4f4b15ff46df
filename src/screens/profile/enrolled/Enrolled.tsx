import React, {
    useMemo,
    useCallback,
    forwardRef,
    useContext,
    useState,
    useEffect,
    useRef,
} from 'react';
import { ScrollView, Text, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import BottomSheetHeader from '../components/BottomSheetHeader';
import BottomSheetSubheader from '../components/BottomSheetSubheader';
import EventLoader from './EventLoader';
import CourseLoader from './CourseLoader';
import EventCard from './EventCard';
import CourseCard from './CourseCard';
import Pagination from '../components/Pagination';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';

interface EnrolledProps {
    closeModal: () => void;
    to: string;
}

const Enrolled: React.FC<EnrolledProps> = ({ closeModal, to }) => {

    const { isLarge } = useContext(ClientContext);

    const submenus = useMemo(() => ['Events', 'Courses'], []);
    const limit = useMemo(() => (isLarge ? 6 : 3), [isLarge]);

    const [selectedSubMenu, setSelectedSubMenu] = useState(to || 'Events');
    const [enrolledData, setEnrolledData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [showPagination, setShowPagination] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const scrollViewRef = useRef<ScrollView>(null);

    const resetPagination = useCallback(() => {
        setEnrolledData([]);
        setCurrentPage(1);
        setTotalPages(0);
        setShowPagination(false);
    }, []);

    const handleSubMenuChange = useCallback(
        (submenu: string) => {
            setSelectedSubMenu(submenu);
            resetPagination();
        },
        [resetPagination]
    );

    const fetchEnrolledData = useCallback(async () => {
        try {
            setLoading(true);
            const endpoint = selectedSubMenu.toLowerCase();
            const response = await ClientAxiosInstance.get(
                `/collegeevent/enrolled-${endpoint}?page=${currentPage}&limit=${limit}`
            );
            setEnrolledData(response.data.data);
            setTotalPages(Math.ceil(response.data.total_count / limit));
            setShowPagination(response.data.total_count > limit);

        } catch (err) {
            console.error('Error fetching enrolled data:', err);
        } finally {
            setLoading(false);
        }
    }, [selectedSubMenu, currentPage, limit]);

    useEffect(() => {
        fetchEnrolledData();
    }, [selectedSubMenu, currentPage, fetchEnrolledData]);

    useEffect(() => resetPagination, [resetPagination]);

    const handlePaginationChange = useCallback(() => {
        scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }, []);

    const renderLoader = useCallback(() => {
        const Loader = selectedSubMenu === 'Events' ? EventLoader : CourseLoader;
        return Array.from({ length: limit }).map((_, index) => (
            <View
                className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`}
                key={`loader-${index}`}
            >
                <Loader />
            </View>
        ));
    }, [limit, selectedSubMenu, isLarge]);

    const renderCards = useCallback(() => {
        const Card = selectedSubMenu === 'Events' ? EventCard : CourseCard;
        return enrolledData.map((item, index) => (
            <View
                className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`}
                key={`card-${index}`}
            >
                <Card item={item} closeModal={closeModal} />
            </View>
        ));
    }, [enrolledData, selectedSubMenu, isLarge]);

    return (
        <View className='flex-1'>

            <BottomSheetHeader name="Enrolled" onPress={closeModal} />

            <BottomSheetSubheader
                submenus={submenus}
                selectedSubMenu={selectedSubMenu}
                setSelectedSubMenu={handleSubMenuChange}
                handlePaginationChange={handlePaginationChange}
            />

            <ScrollView
                ref={scrollViewRef}
                nestedScrollEnabled
                showsVerticalScrollIndicator={false}
            >
                <View className={`space-y-3 px-primary`}>

                    <View className="space-y-[2%] mt-3">

                        <View className='flex-row flex-wrap justify-between '>
                            {loading ? renderLoader() : enrolledData.length > 0 ? renderCards() : (
                                <View className="flex-row items-center justify-center w-full h-12">
                                    <Text
                                        className="text-text17 text-secondary dark:text-white text-center"
                                        style={Global.text_medium}
                                    >
                                        You haven't enrolled anything yet...
                                    </Text>
                                </View>
                            )}
                        </View>

                    </View>

                    {showPagination && (
                        <View className='mb-10'>
                            <Pagination
                                setCurrentPage={setCurrentPage}
                                currentPage={currentPage}
                                totalPages={totalPages}
                                handlePaginationChange={handlePaginationChange}
                            />
                        </View>
                    )}

                </View>

            </ScrollView>

        </View>
    );
};

export default Enrolled;
