import { View, Dimensions } from 'react-native'
import React, { useContext } from 'react'
import { LinearGradient } from 'expo-linear-gradient';
import { Skeleton } from '@rneui/themed'
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { AntDesign, Feather } from '@expo/vector-icons';
import { ClientContext } from '../../../context/ClientContext';

const CourseLoader = () => {

    const { colorScheme,isLarge } = useContext(ClientContext);

    return (
        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='space-y-3'>

                <Skeleton animation='wave'
                    LinearGradientComponent={LinearGradient}
                    style={{
                        width: '100%',
                        height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50),
                        borderRadius: 5
                    }}
                />

                <View className='space-y-3'>

                    <Skeleton animation='wave'
                        LinearGradientComponent={LinearGradient} style={{
                            height: 15,
                            borderRadius: 2,
                            width: '60%'
                        }} />

                    <Skeleton animation='wave'
                        LinearGradientComponent={LinearGradient} style={{
                            height: 15,
                            borderRadius: 2,
                            width: '40%'
                        }} />

                </View>

                <View className='flex-row items-center justify-between'>

                    <View className='flex-row items-center space-x-2'>
                        <Skeleton animation='wave'
                            LinearGradientComponent={LinearGradient} style={{
                                height: 15,
                                borderRadius: 2,
                                width: '15%'
                            }} />
                        <View className='flex-row items-center space-x-3'>
                            <AntDesign name="staro" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                            <AntDesign name="staro" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                            <AntDesign name="staro" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                            <AntDesign name="staro" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                            <AntDesign name="staro" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                        </View>
                    </View>

                    <View className='flex-row items-center space-x-2'>
                        <Feather name='clock' size={20} color={colorScheme === 'dark' ? 'white' : 'black'} />
                        <Skeleton animation='wave'
                            LinearGradientComponent={LinearGradient} style={{
                                height: 15,
                                borderRadius: 2,
                                width: '30%'
                            }} />
                    </View>

                </View>

            </View>

            <View className='items-center w-full mb-3'>
                <Skeleton animation='wave'
                    LinearGradientComponent={LinearGradient} style={{
                        height: 20,
                        borderRadius: 25,
                        width: '60%'
                    }} />
            </View>

        </View>
    )
}

export default CourseLoader;