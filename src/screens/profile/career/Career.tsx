import React, { useMemo, useCallback, useContext, useState, useEffect, useRef } from 'react';
import { ScrollView, Text, View } from 'react-native';

import BottomSheetHeader from '../components/BottomSheetHeader';
import { ClientContext } from '../../../context/ClientContext';
import BottomSheetSubheader from '../components/BottomSheetSubheader';
import CareerCard from './CareerCard';
import Global from '../../../../globalStyle';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import Loader from './Loader';
import Pagination from '../components/Pagination';

interface CareerProps {
    closeModal: () => void;
}

const Career: React.FC<CareerProps> = ({ closeModal }) => {

    const { colorScheme, clientId, isLarge } = useContext(ClientContext);

    const submenus = useMemo(() => ['Jobs', 'Internships'], []);

    const [selectedSubMenu, setSelectedSubMenu] = useState('Jobs');

    const handleSubMenuChange = (submenu: string) => {
        setSelectedSubMenu(submenu);
        resetPagination();
    };

    const resetPagination = useCallback(() => {
        setShowPagination(false);
        setCurrentPage(1);
        setTotalPages(0);
        setCareerData([]);
    }, []);

    const [careerData, setCareerData] = useState<any[]>([]);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const career = selectedSubMenu === 'Jobs' ? 'userjob' : 'userinternship';
    const limit = isLarge ? 6 : 3;

    const getRegisteredCareers = useCallback(async () => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.get(`/${career}/${clientId}/registered-activities?page=${currentPage}&limit=${limit}`);
            const { total_count, data } = response.data;

            setCareerData(data);
            setTotalPages(Math.ceil(total_count / limit));
            setShowPagination(total_count > limit);

        } catch (error) {
            console.log("Get registered careers: ", error);
        } finally {
            setLoading(false);
        }
    }, [selectedSubMenu, currentPage]);

    useEffect(() => {
        getRegisteredCareers();
    }, [selectedSubMenu, currentPage]);

    const scrollViewRef = useRef<ScrollView>(null);
    const handlePaginationChange = () => {
        if (scrollViewRef.current) {
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }

    useEffect(() => {
        return resetPagination;
    }, [])

    return (
        <View className='flex-1'>

            <BottomSheetHeader name='Career' onPress={closeModal} />

            <BottomSheetSubheader
                submenus={submenus}
                selectedSubMenu={selectedSubMenu}
                setSelectedSubMenu={handleSubMenuChange}
                handlePaginationChange={handlePaginationChange}
            />

            <ScrollView ref={scrollViewRef} nestedScrollEnabled showsVerticalScrollIndicator={false} style={{ backgroundColor: colorScheme === 'dark' ? '#111' : '#fff' }}>

                <View className={`space-y-3 px-primary`}>

                    <View className='space-y-[2%] mt-3'>
                        {loading ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {[1, 2, 3, 4, 5, 6].map((index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <Loader />
                                        </View>
                                    ))}
                                </View>
                            ) : (
                                [1, 2, 3,].map((index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <Loader />
                                    </View>
                                ))
                            )
                        ) : careerData?.length > 0 ? (
                            isLarge ? (
                                <View style={{ flexDirection: isLarge ? 'row' : 'column', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    {careerData?.map((item, index) => (
                                        <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                            <CareerCard closeModal={closeModal} item={item} selectedSubMenu={selectedSubMenu} />
                                        </View>
                                    ))}
                                </View>
                            ) :
                                careerData?.map((item, index) => (
                                    <View className={`${isLarge ? 'w-[49%]' : 'w-full'} mb-[3%]`} key={index}>
                                        <CareerCard closeModal={closeModal} item={item} selectedSubMenu={selectedSubMenu} />
                                    </View>
                                ))
                        ) : (
                            <View className='flex-row items-center justify-center w-full h-12'>
                                <Text className='text-text17 text-secondary dark:text-white text-center' style={Global.text_medium}>
                                    You haven't applied for any {selectedSubMenu.toLowerCase()} yet...
                                </Text>
                            </View>
                        )}
                    </View>

                    {showPagination &&
                        <View className='mb-10'>
                            <Pagination
                                setCurrentPage={setCurrentPage}
                                currentPage={currentPage}
                                totalPages={totalPages}
                                handlePaginationChange={handlePaginationChange}
                            />
                        </View>
                    }

                </View>

            </ScrollView>

        </View>
    );
};

export default Career;