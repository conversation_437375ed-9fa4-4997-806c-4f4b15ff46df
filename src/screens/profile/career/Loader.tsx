import { View, Dimensions } from 'react-native'
import React from 'react'
import { LinearGradient } from 'expo-linear-gradient';
import { Skeleton } from '@rneui/themed'
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { ClientContext } from '../../../context/ClientContext';

const { width } = Dimensions.get('window');

const Loader = () => {

    const { isLarge } = React.useContext(ClientContext);

    return (
        <View className='w-full p-2 space-y-6 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='w-full space-y-4'>

                <Skeleton LinearGradientComponent={LinearGradient} animation='wave'
                    style={{
                        width: '100%',
                        height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50),
                        borderRadius: 5
                    }}
                />

                <View className='space-y-3'>

                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 15,
                        borderRadius: 2,
                        width: '60%'
                    }} />

                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 15,
                        borderRadius: 2,
                        width: '40%'
                    }} />

                    <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                        height: 12,
                        borderRadius: 2,
                        width: '20%'
                    }} />

                    <View className='flex-row items-center w-full'>

                        <View className='w-[32%]'>
                            <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                height: 10,
                                borderRadius: 2,
                                width: '70%'
                            }} />
                        </View>

                        <View className='w-[32%]'>
                            <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                height: 10,
                                borderRadius: 2,
                                width: '70%'
                            }} />
                        </View>

                        <View className='w-[32%]'>
                            <Skeleton LinearGradientComponent={LinearGradient} animation='wave' style={{
                                height: 10,
                                borderRadius: 2,
                                width: '90%'
                            }} />
                        </View>

                    </View>

                </View>

            </View>

        </View>
    )
}

export default Loader;