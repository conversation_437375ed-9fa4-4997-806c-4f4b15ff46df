import { View, Text, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'

import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../../../context/ClientContext'
import Global from '../../../../globalStyle'

import moment from 'moment'
import Badge from '../../../components/Badge'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { getRegistrationEndTime, isRegistrationExpired } from '../../../helpers/dateTimeFormat'

interface CareerCardProps {
    item: any;
    selectedSubMenu: string;
    closeModal:()=>void;
}

const CareerCard: React.FC<CareerCardProps> = ({ item, selectedSubMenu,closeModal }) => {

    const { colorScheme, navigation, registeredCareers, setCurrentCareerChip, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, registeredItems } = useContext(LikeSaveContext);

    const chip = selectedSubMenu === 'Jobs' ? 'Job Listing' : 'Internship';

    const handleCareerDetails = () => {
        closeModal();
        setCurrentCareerChip(chip);
        navigation.navigate('CareerDetails', { selectedCareer: item });
    };

    const getTotalApplicants = (applicants: number) => {
        return applicants > 1000 ? `${Math?.floor(applicants / 1000)}K+` : applicants?.toString();
    };

    const isRegisteredCareer = (id: string) => {
        const isAlreadyRegistered = registeredCareers[0][selectedSubMenu]?.includes(id);
        return isAlreadyRegistered || registeredItems?.has(id);
    }

    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='w-full space-y-4' onPress={handleCareerDetails}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                        blurRadius={isRegisteredCareer(item?._id) ? 2 : isRegistrationExpired(item?.application_deadline) ? 2 : 0}
                    />
                    <View className='absolute bottom-1 left-1'>
                        {isRegisteredCareer(item?._id)
                            ? <Badge name='Applied' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                            : isRegistrationExpired(item?.application_deadline)
                                ? <Badge name='Application Ended' backgroundColor='#FF2020' borderColor='#fff' color='#fff' />
                                : null
                        }
                    </View>
                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[45%]'>
                        <Badge name={selectedSubMenu} />
                    </View>

                    <View className='w-[50%] h-full items-end'>
                        <Text
                            className='leading-6 text-secondary dark:text-white text-wrap'
                            style={Global.text_medium}
                        >
                            {getRegistrationEndTime(item?.application_deadline) === 'na'
                                ? 'Application Ended'
                                : `App. ends on : ${getRegistrationEndTime(item?.application_deadline).slice(0, 6)}`
                            }
                        </Text>
                    </View>

                </View>

                <View className='space-y-3'>

                    <Text
                        className='text-xl tracking-wider text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={2}
                        ellipsizeMode='tail'
                    >
                        {item?.job_position || item?.internship_position}
                    </Text>

                    <Text
                        className='tracking-wide text-text16 text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        {item?.company_name}
                    </Text>

                    <Text
                        className='tracking-wider text-text15 text-secondary dark:text-white'
                        style={Global.text_regular}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        {item?.location}
                    </Text>

                    <View className='flex-row items-center w-full'>

                        <Text
                            className='w-[32%] text-text14 text-secondary dark:text-white tracking-wider pr-1'
                            style={Global.text_regular}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {moment(item?.createdAt).isBefore(
                                moment().subtract(7, "days")
                            )
                                ? moment(item?.createdAt).format("DD MMM, YYYY")
                                : moment(item?.createdAt).fromNow()
                            }
                        </Text>

                        <Text
                            className='w-[32%] text-text14 text-secondary dark:text-white tracking-wider pr-1'
                            style={Global.text_regular}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {item?.job_type || item?.internship_type}
                        </Text>

                        <Text
                            className='w-[32%] text-text14 text-secondary dark:text-white tracking-wider pr-1'
                            style={Global.text_regular}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {getTotalApplicants(item?.total_applicants)} Applicants
                        </Text>

                    </View>

                </View>

            </TouchableOpacity>

            <View className='items-center w-full h-10 mb-1'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, chip)}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default CareerCard
