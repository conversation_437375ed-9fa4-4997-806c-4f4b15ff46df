import React, { useContext } from 'react';
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native';
import BottomSheetHeader from '../components/BottomSheetHeader';
import EditProfileCard from '../components/EditProfileCard';
import EditProfileInfo from '../components/EditProfileInfo';
import { ClientContext } from '../../../context/ClientContext';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ClientAxiosInstance } from '../../../lib/axiosInstance';
import { Toast } from '../../../components/Toast';
import { capitalizeMode } from '../../../helpers/getCapitalize';
import { Image } from 'expo-image';

interface FormValues {
    fname: string;
    lname: string;
}

interface EditProfileProps {
    closeModal: () => void;
}

const EditProfile: React.FC<EditProfileProps> = ({ closeModal }) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { colorScheme, clientId, setUserData, userData } = useContext(ClientContext);

    const schema = yup.object().shape({
        fname: yup
            .string()
            .required('First Name is required')
            .matches(/^[a-zA-Z ]+$/, 'First Name must contain only letters')
            .min(3, 'First Name must be at least 3 characters')
            .max(50, 'First Name must be at most 50 characters'),
        lname: yup
            .string()
            .required('Last Name is required')
            .matches(/^[a-zA-Z ]+$/, 'Last Name must contain only letters')
            .min(1, 'Last Name must be at least 1 character')
            .max(25, 'Last Name must be at most 25 characters'),
    });

    const {
        control,
        handleSubmit,
        formState: { errors }
    } = useForm<FormValues>({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: any) => {
        try {
            const response = await ClientAxiosInstance.put(`${api_link}/auth/${clientId}`, {
                first_name: capitalizeMode(data?.fname),
                last_name: capitalizeMode(data?.lname)
            });

            if (response.data.success) {

                setUserData({ ...userData, first_name: response.data?.data?.first_name, last_name: response.data?.data?.last_name });

                closeModal();

                Toast.show({
                    type: 'success',
                    message: 'Profile updated successfully',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../../../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }

        } catch (error) {
            console.log("Edit Profile : ", error);
        }
    };

    return (
        <View className='flex-1'>

            <BottomSheetHeader name='Edit Details' onPress={closeModal} />

            <View className='h-1' style={{ backgroundColor: colorScheme === 'dark' ? '#111' : '#fff' }} />

            <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }} >

                <ScrollView showsVerticalScrollIndicator={false}>
                    <View className='w-full p-3 space-y-8 rounded-md bg-cardLight dark:bg-darkcard'>
                        <View><EditProfileCard /></View>
                        <View><EditProfileInfo control={control} errors={errors} handleSubmit={handleSubmit} onSubmit={onSubmit} /></View>
                    </View>
                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    );
};

export default EditProfile;
