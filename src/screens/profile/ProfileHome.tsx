import React, { useContext, useEffect, useState } from 'react';
import { Modal, ScrollView, Text, View } from 'react-native';

import CustomStatusBar from '../../components/CustomStatusBar';
import Header from '../../components/Header';
import Global from '../../../globalStyle';
import ProfileCard from './components/ProfileCard';
import ProfileMenu from './components/ProfileMenu';
import { ClientContext } from '../../context/ClientContext';
import Competition from './competition/Competition';
import EditProfile from './edit-profile/EditProfile';
import Enrolled from './enrolled/Enrolled';
import Career from './career/Career';
import PurchaseHistory from './purchase-history/PurchaseHistory';
import Wishlist from './wishlist/Wishlist';

const ProfileHome = ({ route }: any) => {

    const { isAndroid } = useContext(ClientContext);
    const [activeModal, setActiveModal] = useState<string | null>(null);

    useEffect(() => {
        if (route?.params) {
            const { from } = route.params;
            if (from === 'Competition') setActiveModal('competition');
            if (from === 'Enrolled') setActiveModal('enrolled');
        }
    }, [route]);

    const closeModal = () => setActiveModal(null);

    return (
        <View className="flex-1 bg-white dark:bg-dark">

            <CustomStatusBar />

            <Header search name="Dashboard" index={4} />

            <ScrollView
                showsVerticalScrollIndicator={false}
                className="flex-1"
            >
                <View className={`space-y-4 ${isAndroid ? 'mb-20' : 'mb-24'}`}>

                    <Text
                        style={Global.text_bold}
                        className="mt-6 text-xl tracking-wider text-center text-secondary dark:text-white"
                    >
                        My Profile
                    </Text>

                    <View className="my-2">
                        <ProfileCard openEditProfile={() => setActiveModal('editProfile')} />
                    </View>

                    <View>
                        <ProfileMenu
                            openWishList={() => setActiveModal('wishlist')}
                            openEnrolled={() => setActiveModal('enrolled')}
                            openCompetition={() => setActiveModal('competition')}
                            openPurchaseHistory={() => setActiveModal('purchaseHistory')}
                            openCareer={() => setActiveModal('career')}
                        />
                    </View>

                </View>

            </ScrollView>

            <Modal
                visible={!!activeModal}
                animationType="slide"
                transparent
                statusBarTranslucent
                onRequestClose={closeModal}
            >
                <View className="flex-1 pt-24 bg-black/5">
                    <View className="flex-1 w-full overflow-hidden bg-white dark:bg-dark rounded-t-xl">
                        {activeModal === 'editProfile' && <EditProfile closeModal={closeModal} />}
                        {activeModal === 'wishlist' && <Wishlist closeModal={closeModal} />}
                        {activeModal === 'enrolled' && (
                            <Enrolled closeModal={closeModal} to={route?.params?.to} />
                        )}
                        {activeModal === 'competition' && (
                            <Competition closeModal={closeModal} to={route?.params?.to} />
                        )}
                        {activeModal === 'career' && <Career closeModal={closeModal} />}
                        {activeModal === 'purchaseHistory' && (
                            <PurchaseHistory closeModal={closeModal} />
                        )}
                    </View>
                </View>

            </Modal>

        </View>
    );
};

export default ProfileHome;
