import { View, ScrollView } from 'react-native'
import React, { useContext } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar'
import Header from '../../../components/Header'
import JobCard from '../components/JobCard'
import JobInformation from '../components/JobInformation'
import JobWarning from '../components/JobWarning'
import LawCubeCard from '../components/LawCubeCard'
import { ClientContext } from '../../../context/ClientContext'


const CareerDetails = ({ route }: any) => {

    const { currentCareerChip } = useContext(ClientContext);
    const selectedCareer = route?.params?.selectedCareer || {};

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name={currentCareerChip.includes('Job') ? 'Job Details' : 'Internship Details'} index={-1} />

            <ScrollView showsVerticalScrollIndicator={false} className='w-full h-full'>

                <View className='h-full w-full px-primary space-y-6 mb-8'>

                    <View><JobCard selectedCareer={selectedCareer} /></View>

                    <View><JobInformation selectedCareer={selectedCareer} /></View>

                    <View><JobWarning /></View>

                    <View><LawCubeCard /></View>

                </View>

            </ScrollView>

        </View>
    )
}

export default CareerDetails