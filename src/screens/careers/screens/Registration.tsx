import { View, Text, ScrollView, KeyboardAvoidingView } from 'react-native'
import React, { useContext, useState } from 'react'
    ;
import CustomStatusBar from '../../../components/CustomStatusBar';
import Header from '../../../components/Header';
import RegistrationCard from '../components/RegistrationCard';
import Global from '../../../../globalStyle';
import RegistrationForm from '../components/RegistrationForm';
import Confirmation from '../components/Confirmation';
import { ClientContext } from '../../../context/ClientContext';

const Registration = ({ route }: any) => {

    const selectedCareer = route?.params?.selectedCareer || {};
    const [showConfirmation, setShowConfirmation] = useState(false);
    const { isAndroid } = useContext(ClientContext);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search name='Registration' index={-1} />

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >

            <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps='handled' className='flex-1'>

                <View className='py-4 px-primary'>

                    <View className='space-y-2 rounded-md bg-cardLight dark:bg-darkcard'>

                        <View className='items-center py-4 bg-primary rounded-t-md'>
                            <Text className='text-xl tracking-wider text-secondary' style={Global.text_bold}>Job Registration</Text>
                        </View>

                        <View className='px-2'>
                            <RegistrationCard selectedCareer={selectedCareer} />
                        </View>

                        <View className='px-2 pb-4'>
                            <RegistrationForm
                                setShowConfirmation={setShowConfirmation}
                                selectedCareer={selectedCareer}
                            />
                        </View>

                    </View>

                </View>

                <Confirmation
                    showConfirmation={showConfirmation}
                    setShowConfirmation={setShowConfirmation}
                    selectedCareer={selectedCareer}
                />

            </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default Registration