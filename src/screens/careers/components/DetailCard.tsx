import { View, Text, TouchableOpacity, Dimensions } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../../../context/ClientContext'
import Global from '../../../../globalStyle'
import moment from 'moment'
import Button from '../../../components/Button'
import Badge from '../../../components/Badge'
import { widthPercentageToDP } from 'react-native-responsive-screen'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { getRegistrationEndTime, isExpired, isRegistrationExpired } from '../../../helpers/dateTimeFormat'

interface DetailCardProps {
    confirmationData?: any;
    showConfirmation?: boolean;
    data?: any;
}

const { width } = Dimensions.get('window');

const DetailCard: React.FC<DetailCardProps> = ({ confirmationData, showConfirmation, data }) => {

    const { colorScheme, navigation, currentCareerChip, registeredCareers, isLarge } = useContext(ClientContext);
    const { isCardSaved, saveCard, registeredItems } = useContext(LikeSaveContext);

    const item = confirmationData ? confirmationData : data;

    const handleCareerDetails = () => {
        navigation.navigate('CareerDetails', { selectedCareer: item });
    };

    const exploreMoreJobs = () => {
        navigation.navigate('Career');
    };

    const getTotalApplicants = (applicants: number) => {
        return applicants > 1000 ? `${Math.floor(applicants / 1000)}K+` : applicants.toString();
    };

    const isRegisteredCareer = () => {
        const isAlreadyRegistered = registeredCareers[0][currentCareerChip]?.includes(item?._id);
        return isAlreadyRegistered || registeredItems?.has(item?._id);
    }



    return (
        <View className='w-full p-2 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <TouchableOpacity activeOpacity={0.8} className='w-full space-y-4' onPress={showConfirmation ? undefined : handleCareerDetails}>

                <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                    <Image
                        source={item?.thumbnail_image_path ?
                            { uri: item?.thumbnail_image_path } :
                            require('../../../assets/images/placeholder-thumbnail.png')
                        }
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 5
                        }}
                        blurRadius={isRegisteredCareer() ? 2 : isRegistrationExpired(item?.application_deadline) ? 2 : 0}
                    />
                    <View className='absolute bottom-1 left-1'>
                        {isRegisteredCareer()
                            ? <Badge name='Applied' backgroundColor='#2EB418' borderColor='#fff' color='#fff' />
                            : isExpired(item?.application_deadline)
                                ? <Badge name='Application Ended' backgroundColor='#FF2020' borderColor='#fff' color='#fff' />
                                : null
                        }
                    </View>
                </View>

                <View className='flex-row items-start justify-between w-full'>

                    <View className='w-[45%]'>
                        <Badge height={30} name={currentCareerChip.includes('Job') ? 'Jobs' : 'Internships'} />
                    </View>

                    <View className='w-[50%] h-full items-end'>
                        <Text
                            className='leading-6 text-secondary dark:text-white text-wrap'
                            style={Global.text_medium}
                        >
                            {getRegistrationEndTime(item?.application_deadline, true) === 'na'
                                ? 'Application Ended'
                                : `App. ends on : ${getRegistrationEndTime(item?.application_deadline, true).slice(0, 6)}`
                            }
                        </Text>
                    </View>

                </View>

                <View className='space-y-3'>

                    <Text
                        className='text-xl tracking-wider text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={2}
                        ellipsizeMode='tail'
                    >
                        {item?.job_position || item?.internship_position}
                    </Text>

                    <Text
                        className='tracking-wide text-text16 text-secondary dark:text-white'
                        style={Global.text_bold}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        {item?.company_name}
                    </Text>

                    <Text
                        className='tracking-wider text-text15 text-secondary dark:text-white'
                        style={Global.text_regular}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                    >
                        {item?.location}
                    </Text>

                    <View className='flex-row items-center justify-between w-full'>

                        <Text
                            className='tracking-wider text-text14 text-secondary dark:text-white'
                            style={[Global.text_regular, { maxWidth: '30%' }]}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {moment(item?.createdAt).isBefore(
                                moment().subtract(7, "days")
                            )
                                ? moment(item?.createdAt).format("DD MMM, YYYY")
                                : moment(item?.createdAt).fromNow()
                            }
                        </Text>

                        <Text
                            className='tracking-wider text-text14 text-secondary dark:text-white'
                            style={[Global.text_regular, { maxWidth: '30%' }]}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {item?.job_type || item?.internship_type}
                        </Text>

                        <Text
                            className='tracking-wider text-text14 text-secondary dark:text-white'
                            style={[Global.text_regular, { maxWidth: '30%' }]}
                            numberOfLines={1}
                            ellipsizeMode='tail'
                        >
                            {showConfirmation ? getTotalApplicants(item?.total_applicants) + 1 : getTotalApplicants(item?.total_applicants)} Applicants
                        </Text>

                    </View>

                </View>

            </TouchableOpacity>

            {!showConfirmation && <View className='items-center w-full h-10 mb-1'>
                <TouchableOpacity activeOpacity={0.8} className='flex-row items-center h-full space-x-1' onPress={() => saveCard(item._id, currentCareerChip)}>
                    <Ionicons
                        name={isCardSaved(item?._id) ? 'heart' : 'heart-outline'}
                        size={25}
                        color={isCardSaved(item?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                    />
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{isCardSaved(item?._id) ? 'Saved' : 'Save for later'}</Text>
                </TouchableOpacity>
            </View>}

            {showConfirmation && <View className='mb-3'>
                <Button
                    title={`Explore more ${currentCareerChip.includes('Job') ? 'Jobs' : 'Internships'}`}
                    onPress={exploreMoreJobs}
                    height={57}
                />
            </View>}

        </View>
    )
}

export default DetailCard