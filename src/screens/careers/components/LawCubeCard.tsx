import { View, Text, Linking } from 'react-native'
import React from 'react'
import Global from '../../../../globalStyle'
import { Image } from 'expo-image';
import Button from '../../../components/Button'

const LawCubeCard = () => {

    const joinWhatsapp = () => {
        Linking.openURL('https://chat.whatsapp.com/')
    }

    return (
        <View className='bg-[#1E6113] rounded-md px-3 py-4 space-y-5'>

            <Text style={Global.text_bold} className='text-white text-text16 tracking-wide text-center uppercase'>
                Lets stay updated with latest jobs, internships and legal opportunities
            </Text>

            <View className='w-full items-center'>
                <Image
                    source={require('../../../assets/icons/logo/logo-dark-big.png')}
                    style={{ height: 100, width: 100 }}
                    contentFit='contain'
                />
            </View>

            <Text style={Global.text_regular} className='text-white text-center text-text16 tracking-wide'>
                Join Lawcube Whatsapp Group Today
            </Text>

            <View className='w-full items-center'>
                <Button
                    title='Join Group'
                    onPress={joinWhatsapp}
                    bgColor='transparent'
                    borderColor='#fff'
                    color='#fff'
                    height={50}
                    paddingX={15}
                />
            </View>

        </View>
    )
}

export default LawCubeCard