import { View, Text, Modal, StatusBar } from 'react-native'
import React, { useContext } from 'react';

import FontAwesome5 from '@expo/vector-icons/FontAwesome6'
import Global from '../../../../globalStyle';
import DetailCard from './DetailCard';

interface ConfirmationProps {
    showConfirmation: boolean;
    setShowConfirmation: (val: boolean) => void;
    selectedCareer: any;
}

const Confirmation: React.FC<ConfirmationProps> = ({ showConfirmation, setShowConfirmation, selectedCareer }) => {

    const handleClose = () => {
        setShowConfirmation(false);
    }

    return (
        <Modal statusBarTranslucent animationType='fade' visible={showConfirmation} onRequestClose={handleClose}>

            <View className='items-center justify-center w-full h-full py-5 space-y-2 bg-white dark:bg-dark px-primary'>

                <View className='flex-col items-center w-full space-y-2'>

                    <View className='h-12 w-12 bg-[#2baa16] rounded-full items-center justify-center'>
                        <FontAwesome5 name='check' size={30} color='white' />
                    </View>

                    <View className='w-[70%]'>
                        <Text
                            className='text-xl text-center text-secondary dark:text-white'
                            style={Global.text_bold}
                        >
                            Thank you for applying in this career
                        </Text>
                    </View>

                    <Text className='text-lg text-secondary dark:text-white' style={Global.text_regular}>Please do share this with your friends</Text>

                </View>

                <View className='w-full'>
                    <DetailCard showConfirmation={showConfirmation} confirmationData={selectedCareer} />
                </View>

            </View>

        </Modal>
    )
}

export default Confirmation