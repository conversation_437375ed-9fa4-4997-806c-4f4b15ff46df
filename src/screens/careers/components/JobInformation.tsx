import { View, Text, Linking } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import HTMLView from 'react-native-htmlview';
import { styles } from '../../style/styles';
import { ClientContext } from '../../../context/ClientContext';
import { CareerContext } from '../../../context/CareerContext';

interface JobInformationProps {
    selectedCareer: any;
}

const JobInformation: React.FC<JobInformationProps> = ({ selectedCareer }) => {

    const { colorScheme, currentCareerChip } = useContext(ClientContext);

    return (

        <View className='w-full space-y-6'>

            <View className='space-y-2'>

                <Text
                    className='text-xl tracking-wide text-secondary dark:text-white'
                    style={Global.text_bold}
                >
                    Details of the Job
                </Text>

                <HTMLView
                    value={selectedCareer?.job_description || selectedCareer?.internship_description}
                    stylesheet={styles(colorScheme)}
                    onLinkPress={(url)=>{
                        Linking.openURL(url)
                    }}
                />

            </View>

            {(selectedCareer?.internship_salary_range?.min || selectedCareer?.internship_salary_range?.max) &&
                <View className='space-y-2'>

                    <Text
                        className='text-xl tracking-wide text-secondary dark:text-white'
                        style={Global.text_bold}
                    >
                        Salary / Stipend
                    </Text>

                    <Text
                        className='text-text16 tracking-wider text-secondary dark:text-white'
                        style={Global.text_regular}
                    >
                        &#8377;{selectedCareer?.internship_salary_range?.min}
                        &nbsp;-&nbsp;
                        &#8377;{selectedCareer?.internship_salary_range?.max}
                    </Text>

                </View>}

            {(selectedCareer?.job_salary_range?.min || selectedCareer?.job_salary_range?.max) &&
                <View className='space-y-2'>

                    <Text
                        className='text-xl tracking-wide text-secondary dark:text-white'
                        style={Global.text_bold}
                    >
                        Salary / Stipend
                    </Text>

                    <Text
                        className='text-text16 tracking-wider text-secondary dark:text-white'
                        style={Global.text_regular}
                    >
                        &#8377;{selectedCareer?.job_salary_range?.min}
                        &nbsp;-&nbsp;
                        &#8377;{selectedCareer?.job_salary_range?.max}
                    </Text>

                </View>
            }

            <View className='space-y-2'>

                <Text
                    className='text-xl tracking-wide text-secondary dark:text-white'
                    style={Global.text_bold}
                >
                    Job Type
                </Text>

                <Text
                    className='text-text16 tracking-wider text-secondary dark:text-white'
                    style={Global.text_regular}
                >
                    {selectedCareer?.job_type || selectedCareer?.internship_type}
                </Text>

            </View>

            {currentCareerChip !== 'Internship' && <View className='space-y-2'>

                <Text
                    className='text-xl tracking-wide text-secondary dark:text-white'
                    style={Global.text_bold}
                >
                    Years of Experience
                </Text>

                <Text
                    className='text-text16 tracking-wider text-secondary dark:text-white'
                    style={Global.text_regular}
                >
                    {selectedCareer?.year_of_experience === 0 ? 'Fresher+' : `${selectedCareer?.year_of_experience} Years`}
                </Text>

            </View>}

        </View>
    )
}

export default JobInformation