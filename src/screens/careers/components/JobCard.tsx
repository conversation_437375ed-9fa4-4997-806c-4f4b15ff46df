import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import Global from '../../../../globalStyle'
import Button from '../../../components/Button'
import { ClientContext } from '../../../context/ClientContext'

import Ionicons from '@expo/vector-icons/Ionicons'
import moment from 'moment'
import { LikeSaveContext } from '../../../context/LikeSaveContext'
import { isExpired } from '../../../helpers/dateTimeFormat'

interface JobCardProps {
    selectedCareer: any;
}

const JobCard: React.FC<JobCardProps> = ({ selectedCareer }) => {

    const { colorScheme, navigation, currentCareerChip, registeredCareers } = useContext(ClientContext);
    const { isCardSaved, saveCard, registeredItems } = useContext(LikeSaveContext);

    const isApplied = registeredCareers[0][currentCareerChip].includes(selectedCareer?._id) || registeredItems?.has(selectedCareer?._id);

    const handleApplyNow = () => {
        navigation.navigate('Registration', { selectedCareer: selectedCareer })
    }

    const getTotalApplicants = (applicants: number) => {
        if (applicants > 1000) return `${Math.floor(applicants / 1000)}K+`
        else return applicants?.toString();
    }

    return (
        <View className='w-full px-3 pt-2 pb-4 mt-4 space-y-4 rounded-md bg-cardLight dark:bg-darkcard'>

            <View className='space-y-2'>
                <Text
                    className='text-lg tracking-wider text-secondary dark:text-white'
                    style={Global.text_bold}
                    numberOfLines={2}
                    ellipsizeMode='tail'
                >
                    {selectedCareer?.job_position || selectedCareer?.internship_position}
                </Text>

                <Text
                    className='leading-7 tracking-wide text-text16 text-secondary dark:text-white'
                    style={Global.text_bold}
                >
                    {selectedCareer?.company_name}&nbsp;&nbsp;<Text style={Global.text_regular}>{selectedCareer?.location}</Text>
                </Text>
            </View>

            <View className='flex-row items-center justify-between w-full'>

                <Text
                    className='text-text14 text-secondary dark:text-white tracking-wider pr-1'
                    style={[Global.text_regular, { maxWidth: '33%' }]}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {moment(selectedCareer?.createdAt).isBefore(moment().subtract(7, "days"))
                        ? moment(selectedCareer?.createdAt).format("DD MMM, YYYY") :
                        moment(selectedCareer?.createdAt).fromNow()}
                </Text>

                <Text
                    className='text-text14 text-secondary dark:text-white tracking-wider pr-1'
                    style={[Global.text_regular, { maxWidth: '33%' }]}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {selectedCareer?.job_type || selectedCareer?.internship_type}
                </Text>

                <Text
                    className='text-text14 text-secondary dark:text-white tracking-wider pr-1'
                    style={[Global.text_regular, { maxWidth: '33%' }]}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {getTotalApplicants(selectedCareer?.total_applicants)} Applicants
                </Text>

            </View>

            <View className='flex-row items-center justify-between'>

                <View className='items-start w-2/5'>
                    {isExpired(selectedCareer?.application_deadline) ? (
                        <View className='bg-red-500 rounded-full h-[45] w-full items-center justify-center'>
                            <Text className='tracking-wide text-white text-text14' style={Global.text_bold}>Deadline Ended</Text>
                        </View>
                    ) : isApplied ? (
                        <View className='bg-primary rounded-full h-[45] px-5 items-center justify-center'>
                            <Text className='tracking-wide text-secondary text-text14' style={Global.text_bold}>Applied</Text>
                        </View>
                    ) : (
                        <Button
                            title='Apply Now'
                            onPress={handleApplyNow}
                            height={45}
                            paddingX={15}
                        />
                    )}
                </View>

                <View className='items-center w-3/5'>
                    <TouchableOpacity activeOpacity={0.8} className='flex-row items-center space-x-1' onPress={() => saveCard(selectedCareer._id, currentCareerChip)}>
                        <Ionicons
                            name={isCardSaved(selectedCareer?._id) ? 'heart' : 'heart-outline'}
                            size={25}
                            color={isCardSaved(selectedCareer?._id) ? 'red' : colorScheme === 'dark' ? 'white' : 'gray'}
                        />

                        <Text className='text-text16 text-secondary dark:text-white' style={Global.text_bold}>{ isCardSaved(selectedCareer?._id) ? 'Saved' : 'Save for later'}</Text>

                    </TouchableOpacity>
                </View>

            </View>

        </View>
    )
}

export default JobCard