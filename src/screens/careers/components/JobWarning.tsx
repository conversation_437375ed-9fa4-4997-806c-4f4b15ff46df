import { View, Text } from 'react-native'
import React from 'react'
import Global from '../../../../globalStyle'

import AntDesign from '@expo/vector-icons/AntDesign'

const JobWarning = () => {
    return (
        <View className='flex-row items-center justify-around px-2 py-3 rounded-md bg-primary'>

            <View className='items-center w-1/3'>
                <AntDesign name='warning' size={65} color='#000' />
            </View>

            <View className='w-3/5 space-y-5'>

                <Text style={Global.text_bold} className='tracking-wide text-secondary text-text16'>
                    Lexlegends has NO control on listed items above as it's directly from customer.
                </Text>
                <Text style={Global.text_bold} className='tracking-wide text-secondary text-text16'>
                    Incase of any discrepancy please <NAME_EMAIL>
                </Text>

            </View>

        </View>
    )
}

export default JobWarning