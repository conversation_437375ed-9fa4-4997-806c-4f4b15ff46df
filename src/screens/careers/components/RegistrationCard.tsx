import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import Global from '../../../../globalStyle';
import { ClientContext } from '../../../context/ClientContext';

interface RegistrationCardProps {
    selectedCareer: any;
}

const RegistrationCard: React.FC<RegistrationCardProps> = ({ selectedCareer }) => {

    const { isLarge } = useContext(ClientContext);

    return (
        <View className='bg-white w-full flex-row items-center justify-between py-1 px-1 rounded-md' style={{ height: isLarge ? 150 : 100 }}>

            <View className='h-full w-[45%]'>
                <Image
                    source={selectedCareer?.thumbnail_image_path ?
                        { uri: selectedCareer?.thumbnail_image_path } :
                        require('../../../assets/images/placeholder-thumbnail.png')
                    }
                    style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: 5
                    }}
                />
            </View>

            <View className='h-full w-[53%] flex-col justify-between'>

                <Text
                    className='text-secondary text-text15'
                    style={Global.text_bold}
                    numberOfLines={2}
                    ellipsizeMode='tail'
                >
                    {selectedCareer?.internship_position || selectedCareer?.job_position}
                </Text>

                <Text
                    className='text-secondary text-text14'
                    style={Global.text_medium}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {selectedCareer?.company_name}
                </Text>

                <Text
                    className='text-secondary text-text13'
                    style={Global.text_regular}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                >
                    {selectedCareer?.location}
                </Text>

            </View>

        </View>
    )
}

export default RegistrationCard