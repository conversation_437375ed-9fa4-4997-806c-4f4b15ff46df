import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { TextInput } from 'react-native-paper';
import { Dropdown } from 'react-native-element-dropdown';
import Feather from '@expo/vector-icons/Feather';
import * as DocumentPicker from 'expo-document-picker';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import Button from '../../../components/Button';
import axios from 'axios';
import { GlobalContext } from '../../../context/GlobalProvider';
import { LikeSaveContext } from '../../../context/LikeSaveContext';
import { eduData, expData } from '../../../helpers/careerData';

interface RegistrationFormProps {
    setShowConfirmation: (value: boolean) => void;
    selectedCareer: any;
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({ setShowConfirmation, selectedCareer }) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { colorScheme, token, clientId, currentCareerChip } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);
    const { setRegisteredItems } = useContext(LikeSaveContext);

    const [resumeName, setResumeName] = useState('');

    const schema = yup.object().shape({
        name: yup
            .string()
            .required('Name is required')
            .matches(/^[a-zA-Z ]+$/, 'Name must contain only letters')
            .min(3, 'Name must be at least 3 characters')
            .max(50, 'Name must be at most 50 characters'),
        email: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        mobile: yup
            .string()
            .required('Phone number is required')
            .matches(/^[0-9]+$/, 'Phone number must contain only digits')
            .min(10, 'Phone number must be at least 10 digits'),
        education: yup
            .string()
            .required('Highest Education is required')
            .oneOf(eduData.map(item => item.value), 'Please select a valid education'),
        experience: yup
            .string()
            .when(['education'], (education) => {
                if (education.includes('Postgraduate')) {
                    return yup.string().required('Experience is required');
                } else {
                    return yup.string().notRequired();
                }
            }),
        practice: yup
            .string()
            .required('Area of Practice is required')
            .matches(/^[a-zA-Z, _ -]+$/, 'Area of Practice must contain only letters')
            .min(3, 'Area of Practice must be at least 3 characters')
            .max(150, 'Area of Practice must be atmost 150 characters'),
        user_resume: yup
            .mixed()
            .required('Resume is required'),
        country_code: yup.string()
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        resetField,
        watch,
        setError
    } = useForm({
        resolver: yupResolver(schema),
    });

    const selectedEducation = watch('education');

    useEffect(() => {
        if (selectedEducation === 'Postgraduate') {
            setValue('experience', '');
        }
    }, [selectedEducation, setValue]);

    const formChip = currentCareerChip === 'Job Listing' ? 'userjob' : 'userinternship';
    const formCareerId = currentCareerChip === 'Job Listing' ? 'job_id' : 'internship_id';

    const onSubmit = async (data: any) => {
        try {
            setLoading(true);
            if (!resumeName) {
                setError('user_resume', { type: 'manual', message: 'Resume is required' });
                return;
            }
            const formData = new FormData();

            const currentDate = new Date();
            const applieddate = currentDate.toISOString();
            data.applied_date = applieddate;

            formData.append('name', data.name);
            formData.append('mail_id', data.email);
            formData.append('highest_education', data.education);
            formData.append('years_of_experience', data.experience);
            formData.append('area_of_practice', data.practice);
            formData.append('phone_number', data.mobile);
            formData.append('applied_date', data.applied_date);
            formData.append('user_id', `${clientId}`);
            formData.append(formCareerId, selectedCareer._id);
            formData.append('user_resume[]', {
                name: data.user_resume.assets[0].name,
                uri: data.user_resume.assets[0].uri,
                type: data.user_resume.assets[0].mimeType,
            });

            await axios.post(`${api_link}/${formChip}/create`,
                formData,
                {
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            
            setRegisteredItems(prevItems => {
                const updatedSet = new Set(prevItems);
                updatedSet.add(selectedCareer?._id);
                return updatedSet;
            });
            setShowConfirmation(true);

        } catch (error) {
            console.log("Job Registration : ", error);
        }
        finally {
            setLoading(false);
        }
    };

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : 'white',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className={`tracking-wide text-text16 ${colorScheme === 'light' ? 'text-secondary' : 'text-white'}`} style={Global.text_regular}>
                    {item.label} {item.name && `(${item.name})`}
                </Text>
            </View>
        );
    };

    const handleResume = useCallback(async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            });
            if (res?.assets) {
                setResumeName(res?.assets[0]?.name);
                setValue('user_resume', res);
                setError('user_resume', { type: 'manual', message: '' });
            }

        } catch (err) {
            console.log(err);
        }
    }, []);

    const handleDeleteResume = () => {
        setResumeName('');
        resetField('user_resume')
    };

    return (
        <View className='space-y-6'>

            <View className='mt-2 space-y-4'>

                <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    className='w-full px-1 tracking-wide bg-transparent text-text16'
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    placeholder='Name'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.name?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="name"
                            defaultValue=""
                        />
                        {errors?.name?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.name?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Mail Id</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text16'
                                    placeholder='Mail Id'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='email'
                                    onChangeText={item => (
                                        onChange(item.toLowerCase())
                                    )}
                                    onBlur={onBlur}
                                    autoCapitalize='none'
                                    value={value}
                                    error={!!errors?.email?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="email"
                            defaultValue=""
                        />
                        {errors?.email?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.email?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Highest Education</Text>
                    <View>
                        <Controller
                            control={control}
                            name="education"
                            defaultValue=""
                            rules={{ required: 'Education is required' }}
                            render={({ field: { onChange, value } }) => (
                                <Dropdown
                                    style={
                                        [styles.dropdown, { borderColor: errors?.education?.message ? 'red' : '#a8a8a8', paddingLeft: 20 }]
                                    }
                                    selectedTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                        fontSize: 17,
                                        letterSpacing: 0.4
                                    }}
                                    data={eduData}
                                    search={false}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Pick One"
                                    renderItem={renderItem}
                                    onChange={item => {
                                        onChange(item.value);
                                    }}
                                    itemContainerStyle={{
                                        borderBottomWidth: 0.5,
                                        borderBottomColor: '#a8a8a8',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    itemTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                        fontSize: 17
                                    }}
                                    containerStyle={{
                                        borderRadius: 8,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    showsVerticalScrollIndicator={false}
                                    placeholderStyle={{
                                        color: '#a8a8a8',
                                    }}
                                    keyboardAvoiding
                                    dropdownPosition='auto'
                                    fontFamily='DMSans-Regular'
                                    value={value}
                                />
                            )}
                        />
                        {errors?.education?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.education?.message}</Text>
                        )}
                    </View>
                </View>

                {selectedEducation === 'Postgraduate' && <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Years of Experience</Text>
                    <View>
                        <Controller
                            control={control}
                            name="experience"
                            defaultValue=""
                            rules={{ required: 'Experience is required' }}
                            render={({ field: { onChange, value } }) => (
                                <Dropdown
                                    style={
                                        [styles.dropdown, { borderColor: errors?.experience?.message ? 'red' : '#a8a8a8', paddingLeft: 20 }]
                                    }
                                    selectedTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                        fontSize: 17
                                    }}
                                    data={expData}
                                    search={false}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Pick One"
                                    renderItem={renderItem}
                                    onChange={item => {
                                        onChange(item.value);
                                    }}
                                    itemContainerStyle={{
                                        borderBottomWidth: 0.5,
                                        borderBottomColor: '#a8a8a8',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    itemTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                        fontSize: 17
                                    }}
                                    containerStyle={{
                                        borderRadius: 8,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : 'white',
                                    }}
                                    showsVerticalScrollIndicator={false}
                                    placeholderStyle={{
                                        color: '#a8a8a8',
                                    }}
                                    keyboardAvoiding
                                    dropdownPosition='auto'
                                    fontFamily='DMSans-Regular'
                                    value={value}
                                />
                            )}
                        />
                        {errors?.experience?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.experience?.message}</Text>
                        )}
                    </View>
                </View>}

                <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Area of Practice</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text16'
                                    placeholder='Area of Practice'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.practice?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="practice"
                            defaultValue=""
                        />
                        {errors?.practice?.message && (
                            <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.practice?.message}</Text>
                        )}
                    </View>
                </View>

                <View className='space-y-1'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Phone Number</Text>

                    <View className='flex-row items-center'>

                        <View className='w-full'>
                            <Controller
                                control={control}
                                name="country_code"
                                defaultValue='91'
                                render={({ field: { onChange, value } }) => (
                                    <Dropdown
                                        style={{
                                            height: 56,
                                            borderColor: errors.mobile?.message ? "red" : '#a8a8a8',
                                            borderLeftWidth: 1.2,
                                            borderTopWidth: 1.2,
                                            borderBottomWidth: 1.2,
                                            borderRadius: 8,
                                            paddingLeft: 20,
                                            paddingRight: '75%',
                                            overflow: 'hidden',
                                        }}
                                        selectedTextStyle={{
                                            color: colorScheme === 'light' ? '#000' : '#fff',
                                        }}
                                        data={[]}
                                        search
                                        disable
                                        searchPlaceholder='Search Country Code'
                                        inputSearchStyle={{
                                            borderRightWidth: 0,
                                            borderLeftWidth: 0,
                                            borderTopWidth: 0
                                        }}
                                        labelField="label"
                                        valueField="value"
                                        placeholder="(+91)"
                                        renderItem={renderItem}
                                        onChange={item => {
                                            onChange(item.value);
                                        }}
                                        itemContainerStyle={{
                                            borderBottomWidth: 0.5,
                                            borderBottomColor: '#a8a8a8',
                                            backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                                        }}
                                        itemTextStyle={{
                                            color: colorScheme === 'light' ? '#000' : '#fff',
                                            fontSize: 17
                                        }}
                                        containerStyle={{
                                            borderRadius: 8,
                                            overflow: 'hidden',
                                            backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                                        }}
                                        showsVerticalScrollIndicator={false}
                                        placeholderStyle={{
                                            color: colorScheme === 'dark' ? '#fff' : '#2d2828',
                                        }}
                                        keyboardAvoiding
                                        dropdownPosition='top'
                                        fontFamily='DMSans-Regular'
                                        value={value}

                                    />
                                )}
                            />
                        </View>

                        <View className='w-[75%] absolute right-0'>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{
                                            fontFamily: 'DMSans-Regular'
                                        }}
                                        className='w-full tracking-wider bg-transparent text-text16'
                                        placeholder='Phone Number'
                                        placeholderTextColor='#a8a8a8'
                                        inputMode='numeric'
                                        maxLength={10}
                                        onChangeText={onChange}
                                        onBlur={onBlur}
                                        value={value}
                                        error={!!errors?.mobile?.message}
                                        mode='outlined'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{
                                            borderTopWidth: 1.2,
                                            borderBottomWidth: 1.2,
                                            borderRightWidth: 1.2,
                                            borderLeftWidth: 0,
                                            borderTopRightRadius: 8,
                                            borderBottomRightRadius: 8,
                                            borderTopLeftRadius: 0,
                                            borderBottomLeftRadius: 0,
                                        }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                    />
                                )}
                                name="mobile"
                                defaultValue=''
                            />

                        </View>

                    </View>
                    {errors?.mobile?.message && (
                        <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.mobile?.message}</Text>
                    )}
                </View>

                <View className='space-y-2'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Resume</Text>

                    {resumeName ? (
                        <View className='flex-row items-center'>
                            <View className='w-[60%] pr-1'>
                                <Text
                                    style={Global.text_regular}
                                    className='text-text16 text-secondary dark:text-white'
                                    numberOfLines={1}
                                    ellipsizeMode='tail'
                                >
                                    ({resumeName})
                                </Text>
                            </View>
                            <TouchableOpacity
                                activeOpacity={0.8}
                                className='w-[40%] flex-row items-center justify-end space-x-1'
                                onPress={handleDeleteResume}
                            >
                                <FontAwesome6 name="trash" size={18} color="#ef4444" />
                                <Text style={Global.text_medium} className='text-red-500 underline text-text16'>
                                    Remove
                                </Text>
                            </TouchableOpacity>
                        </View>
                    ) : (
                        <TouchableOpacity
                            activeOpacity={0.8}
                            className='flex-row items-center space-x-1'
                            onPress={handleResume}
                        >
                            <Feather name="upload" size={19} color={colorScheme === 'dark' ? '#fff' : "#000"} />
                            <Text style={Global.text_medium} className='ml-2 tracking-wider underline text-text16 text-secondary dark:text-white'>
                                Upload File
                            </Text>
                        </TouchableOpacity>
                    )}
                    {errors?.user_resume?.message && (
                        <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors?.user_resume?.message}</Text>
                    )}
                </View>

            </View>

            <View className='items-center w-full'>
                <Button title='Submit Details' onPress={handleSubmit(onSubmit)} />
            </View>

        </View>
    );
}

export default RegistrationForm;

const styles = StyleSheet.create({
    item: {
        paddingVertical: 15,
        paddingHorizontal: 12,
        borderBottomWidth: 0.5,
    },
    dropdown: {
        height: 57,
        borderColor: '#a8a8a8',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 8,
    }
});
