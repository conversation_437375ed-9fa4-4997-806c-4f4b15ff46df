import React, { useContext, useEffect, useRef, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, Dimensions } from 'react-native';
import { ClientContext } from '../../../context/ClientContext';
import Global from '../../../../globalStyle';
import { CareerContext } from '../../../context/CareerContext';
import JobListingSelectedIcon from '../../../assets/icons/career-icon/JobListingSelectedIcon';
import { IternshipIconLight, IternshipIcon } from '../../../assets/icons/career-icon/InternshipIcon';
import InternshipSelectionIcon from '../../../assets/icons/career-icon/InternshipSelectedIcon';
import { JobListingIcon, JobListingIconLight } from '../../../assets/icons/career-icon/JobListingIcon';

const carouselWidth = Dimensions.get('window').width;
const leftMargin = carouselWidth * 0.04;

interface CareerChipProps {
    currentTopic: string;
    handlePaginationChange: () => void;
}

const CareerChip: React.FC<CareerChipProps> = ({ currentTopic, handlePaginationChange }) => {

    const { colorScheme, setCurrentCareerChip } = useContext(ClientContext);
    const { selectedChip, setSelectedChip } = useContext(CareerContext);

    const defaultData = [
        {
            id: 1,
            title: 'Job Listing',
            icon: selectedChip === 'Job Listing'
                ? colorScheme === 'dark' ? <JobListingIcon /> : <JobListingSelectedIcon />
                : colorScheme === 'dark'
                    ? <JobListingIconLight />
                    : <JobListingIcon />,
        },
        {
            id: 2,
            title: 'Internship',
            icon: selectedChip === 'Internship'
                ? colorScheme === 'dark' ? <IternshipIcon /> : <InternshipSelectionIcon />
                : colorScheme === 'dark'
                    ? <IternshipIconLight />
                    : <IternshipIcon />,
        },
    ];

    const [data, setData] = useState(defaultData);
    const flatListRef = useRef<FlatList<any>>(null);

    useEffect(() => {
        const updatedData = defaultData.map(item => ({
            ...item,
            icon: item.title === selectedChip
                ? (item.title === 'Job Listing' ? (colorScheme === 'dark' ? <JobListingIcon /> : <JobListingSelectedIcon />) : (colorScheme === 'dark' ? <IternshipIcon /> : <InternshipSelectionIcon />))
                : (colorScheme === 'dark'
                    ? (item.title === 'Job Listing' ? <JobListingIconLight /> : <IternshipIconLight />)
                    : (item.title === 'Job Listing' ? <JobListingIcon /> : <IternshipIcon />))
        }));
        setData(updatedData);
    }, [selectedChip, colorScheme]);

    useEffect(() => {
        if (currentTopic) {
            setCurrentCareerChip(currentTopic);
            setSelectedChip(currentTopic);
        }
    }, [currentTopic, setCurrentCareerChip, setSelectedChip]);

    const handleChipSelection = (chipId: number) => {
        const selectedChip = defaultData.find(item => item.id === chipId);
        if (selectedChip) {
            setSelectedChip(selectedChip.title);
            setCurrentCareerChip(selectedChip.title);
            flatListRef.current?.scrollToIndex({ index: 0, animated: true });
            handlePaginationChange();
        }
    };

    const renderItem = ({ item, index }: { item: any, index: number }) => (
        <View style={{ paddingLeft: index === 0 ? leftMargin : 10, paddingRight: index === data.length - 1 ? leftMargin : 0 }}>
            <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => handleChipSelection(item.id)}
                style={{
                    borderRadius: 25,
                }}
                className={`h-10 flex-row items-center justify-center space-x-2 border-[0.8px] border-secondary px-[10px] ${selectedChip === item.title ? '#2d2828 bg-secondary dark:bg-primary' : 'bg-transparent dark:border-white'}`}
            >
                <View>
                    {item.icon}
                </View>
                <Text
                    style={Global.text_bold}
                    className={`${selectedChip === item.title ? 'text-primary dark:text-secondary' : 'text-secondary dark:text-white'} tracking-wide text-text14`}
                >
                    {item.title}
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <View style={{ width: '100%' }}>
            <FlatList
                ref={flatListRef}
                horizontal
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                showsHorizontalScrollIndicator={false}

            />
        </View>
    );
};

export default CareerChip;
