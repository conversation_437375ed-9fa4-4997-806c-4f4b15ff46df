import { useContext, useState } from "react";
import { ClientContext } from "../../../context/ClientContext";

import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import { Keyboard, StyleSheet, Text, View } from "react-native";
import Global from "../../../../globalStyle";
import { TextInput } from "react-native-paper";
import Button from "../../../components/Button";
import { Dropdown } from "react-native-element-dropdown";
import { FormAxiosInstance } from "../../../lib/axiosInstance";
import { Toast } from "../../../components/Toast";
import { Image } from "expo-image";
import { GlobalContext } from "../../../context/GlobalProvider";

const ContactForm = () => {

    const { colorScheme, clientId, isAndroid } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const schema = yup.object().shape({
        name: yup
            .string()
            .required('Name is required')
            .min(3, 'Name must be at least 3 characters')
            .max(50, 'Name must be at most 50 characters'),
        mail_id: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        phone_number: yup
            .string()
            .required('Phone number is required')
            .matches(/^[0-9]+$/, 'Phone number must contain only digits')
            .min(10, 'Phone number must be 10 digits'),
        description: yup
            .string()
            .required('Description is required')
            .min(5, 'Description must be at least 5 characters')
            .max(200, 'Description cannot exceed 200 characters'),
        country_code: yup.string()
    });

    const { control, handleSubmit, formState: { errors }, watch, setValue, reset } = useForm({
        resolver: yupResolver(schema),
    });

    const descriptionValue = watch('description', '');

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true);

            const response = await FormAxiosInstance.post('/usercontact/create', {
                ...data,
                user_id: clientId,
            });

            Toast.show({
                type: 'success',
                message: 'Details submitted successfully',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../../../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

            if (response.data.success) reset();

        } catch (error) {
            console.log("Help and support error : ", error);
        } finally {
            setLoading(false);
        }
    };

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label} ({item.name})
                </Text>
            </View>
        );
    };

    return (
        <View className='w-full'>

            <View className='pb-5 space-y-3 rounded-lg bg-cardLight dark:bg-darkcard'>

                <View className='items-center justify-center rounded-tl-lg rounded-tr-lg bg-primary'>
                    <Text style={Global.text_bold} className='p-4 text-xl text-secondary'>Send us a message</Text>
                </View>

                <View className='px-3 space-y-3'>

                    <View className='space-y-2'>
                        <Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Name</Text>
                        <View>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{ fontFamily: 'DMSans-Regular' }}
                                        className='w-full tracking-wide bg-transparent text-text17'
                                        placeholder='Name'
                                        placeholderTextColor='#a8a8a8'
                                        onChangeText={onChange}
                                        onBlur={onBlur}
                                        value={value}
                                        error={!!errors?.name}
                                        mode='outlined'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                    />
                                )}
                                name="name"
                                defaultValue=""
                            />
                            {errors.name && <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors.name.message}</Text>}
                        </View>
                    </View>

                    <View className='space-y-2'>
                        <Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Mail Id</Text>
                        <View>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{ fontFamily: 'DMSans-Regular' }}
                                        className='w-full tracking-wide bg-transparent text-text17'
                                        placeholder='Mail Id'
                                        placeholderTextColor='#a8a8a8'
                                        inputMode='email'
                                        onChangeText={item => (
                                            onChange(item.toLowerCase())
                                        )}
                                        onBlur={onBlur}
                                        value={value}
                                        autoCapitalize="none"
                                        error={!!errors?.mail_id}
                                        mode='outlined'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                    />
                                )}
                                name="mail_id"
                                defaultValue=""
                            />
                            {errors.mail_id && <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors.mail_id.message}</Text>}
                        </View>
                    </View>

                    <View className='space-y-3'>
                        <View className='space-y-1'>
                            <Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Description</Text>
                        </View>
                        <View>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{ fontFamily: 'DMSans-Regular', marginVertical: isAndroid ? 10 : 0 }}
                                        className='w-full tracking-wide bg-transparent text-text17'
                                        placeholder='Description'
                                        placeholderTextColor='#a8a8a8'
                                        onChangeText={(text) => {
                                            if (text.length <= 200) {
                                                onChange(text);
                                                setValue('description', text);
                                            }
                                        }}
                                        onBlur={onBlur}
                                        value={value}
                                        error={!!errors?.description}
                                        mode='outlined'
                                        style={{ textAlignVertical: 'top', minHeight: 100 }}
                                        multiline
                                        textAlignVertical='top'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{ borderWidth: 1.2, borderRadius: 8 }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        maxLength={200}
                                    />
                                )}
                                name="description"
                                defaultValue=""
                            />
                            <Text style={Global.text_regular} className='self-end mt-1 text-gray-500'>
                                ({descriptionValue.length}/200)
                            </Text>
                            {errors.description && <Text style={Global.text_medium} className='text-red-500 -top-4'>* {errors.description.message}</Text>}
                        </View>
                    </View>

                    <View className='space-y-2'>
                        <Text className='tracking-wide text-secondary text-text16 dark:text-white' style={Global.text_bold}>Phone Number</Text>
                        <View className="w-full">
                            <View className='flex-row items-center'>
                                <View className='w-full'>
                                    <Controller
                                        control={control}
                                        name="country_code"
                                        defaultValue='+91'
                                        render={({ field: { onChange, value } }) => (
                                            <Dropdown
                                                style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: errors?.phone_number?.message ? 'red' : '#a8a8a8' }]}
                                                selectedTextStyle={{ color: colorScheme === 'light' ? '#000' : '#fff' }}
                                                data={[]}
                                                search
                                                searchPlaceholder='Search Country Code'
                                                inputSearchStyle={{ borderRightWidth: 0, borderLeftWidth: 0, borderTopWidth: 0 }}
                                                labelField="label"
                                                valueField="value"
                                                disable
                                                placeholder="(+91)"
                                                renderItem={renderItem}
                                                onChange={item => {
                                                    onChange(item.value);
                                                }}
                                                itemContainerStyle={{
                                                    borderBottomWidth: 0.5,
                                                    borderBottomColor: '#a8a8a8',
                                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                                }}
                                                itemTextStyle={{ color: colorScheme === 'light' ? '#000' : '#fff', fontSize: 17 }}
                                                containerStyle={{
                                                    borderRadius: 8,
                                                    overflow: 'hidden',
                                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                                }}
                                                showsVerticalScrollIndicator={false}
                                                placeholderStyle={{ color: colorScheme === 'dark' ? '#fff' : '#2d2828' }}
                                                keyboardAvoiding
                                                dropdownPosition='auto'
                                                fontFamily='DMSans-Regular'
                                                value={value}
                                            />
                                        )}
                                    />
                                </View>
                                <View className='w-[75%] absolute right-0'>
                                    <Controller
                                        control={control}
                                        render={({ field: { onChange, onBlur, value } }) => (
                                            <TextInput
                                                contentStyle={{ fontFamily: 'DMSans-Regular' }}
                                                className='w-full tracking-wider bg-transparent text-text17'
                                                placeholder='Phone Number'
                                                placeholderTextColor='#a8a8a8'
                                                inputMode='numeric'
                                                maxLength={10}
                                                onChangeText={onChange}
                                                onBlur={onBlur}
                                                value={value}
                                                error={!!errors?.phone_number?.message}
                                                mode='outlined'
                                                activeOutlineColor='#a8a8a8'
                                                outlineStyle={{
                                                    borderTopWidth: 1.2,
                                                    borderBottomWidth: 1.2,
                                                    borderRightWidth: 1.2,
                                                    borderLeftWidth: 0,
                                                    borderTopRightRadius: 8,
                                                    borderBottomRightRadius: 8,
                                                    borderBottomLeftRadius: 0,
                                                }}
                                                outlineColor='#a8a8a8'
                                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                            />
                                        )}
                                        name="phone_number"
                                        defaultValue=''
                                    />
                                </View>
                            </View>
                            {errors.phone_number && <Text style={Global.text_medium} className='text-red-500 text-sm'>* {errors.phone_number.message}</Text>}
                        </View>
                    </View>

                    <View className='self-center'>
                        <Button onPress={handleSubmit(onSubmit)} title='Submit Details' color='#000' />
                    </View>

                </View>

            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    dropdown: {
        height: 56,
        borderColor: '#a8a8a8',
        borderLeftWidth: 1.2,
        borderTopWidth: 1.2,
        borderBottomWidth: 1.2,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: '75%',
        overflow: 'hidden',
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default ContactForm;