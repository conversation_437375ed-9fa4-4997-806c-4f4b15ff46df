import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'

import { Discussion2, Discussion3 } from '../../../assets/icons/bottom-tab/DiscussionIcon'
import EventIcon from '../../../assets/icons/menu-icon/EventIcon'
import CompetitionIcon from '../../../assets/icons/menu-icon/CompetitionIcon'
import CareersIcon from '../../../assets/icons/menu-icon/CareersIcon'
import ReadingsIcon from '../../../assets/icons/menu-icon/ReadingsIcon'
import CoursesIcon from '../../../assets/icons/menu-icon/CoursesIcon'
import CareersIconDark from '../../../assets/icons/menu-icon/CareersIconDark'
import EventIconDark from '../../../assets/icons/menu-icon/EventIconDark'
import ReadingsIconDark from '../../../assets/icons/menu-icon/ReadingsIconDark'
import CoursesIconDark from '../../../assets/icons/menu-icon/CoursesIconDark'

const MenuList = () => {

    const { navigation, colorScheme } = useContext(ClientContext);

    const list = [
        {
            title: 'Competition',
            icon: <View className='right-[6px]'>{colorScheme === 'dark' ? <CareersIconDark /> : <CompetitionIcon />}</View>,
            screen: 'Competition'
        },
        {
            title: 'Events',
            icon: <View className='right-[6px]'>{colorScheme === 'dark' ? <EventIconDark /> : <EventIcon />}</View>,
            screen: 'Events'
        },
        {
            title: 'Readings',
            icon: <View className='right-[6px]'>{colorScheme === 'dark' ? <ReadingsIconDark /> : <ReadingsIcon />}</View>,
            screen: 'Readings'
        },
        {
            title: 'Careers',
            icon: <View className='right-[6px]'>{colorScheme === 'dark' ? <CareersIconDark /> : <CareersIcon />}</View>,
            screen: 'Career'
        },
        {
            title: 'Courses',
            icon: <View className='right-[6px]'>{colorScheme === 'dark' ? <CoursesIconDark /> : <CoursesIcon />}</View>,
            screen: 'Courses'
        },
        {
            title: 'Discussion Forum',
            icon: <View>{colorScheme === 'dark' ? <Discussion2 /> : <Discussion3 />}</View>,
            screen: 'Discussion'
        },

    ]

    return (
        <View>
            {list.map((item, index) => (
                <View key={index}>
                    <TouchableOpacity
                        onPress={() => navigation.navigate(item.screen)}
                        activeOpacity={0.8}
                        className='h-16 flex-row items-center px-primary'
                    >
                        <View className='w-10'>{item.icon}</View>
                        <Text style={Global.text_bold} className='text-text16 tracking-wider text-secondary dark:text-white'>{item.title}</Text>
                    </TouchableOpacity>
                    <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />
                </View>
            ))}
        </View>
    )
}

export default MenuList