import { Platform, ScrollView, Share, Text, TouchableOpacity, View, Modal } from 'react-native'
import { Switch } from 'react-native-paper'
import React, { useContext, useState } from 'react'

import Ionicons from '@expo/vector-icons/Ionicons'
import Feather from '@expo/vector-icons/Feather'

import Global from '../../../globalStyle';
import { ClientContext } from '../../context/ClientContext';
import MenuList from './components/MenuList';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomStatusBar from '../../components/CustomStatusBar';
import { Toast } from '../../components/Toast'
import { Image } from 'expo-image'
import { LikeSaveContext } from '../../context/LikeSaveContext'
import { ClientAxiosInstance } from '../../lib/axiosInstance'

const INVITE_LINK = Platform.OS === 'ios' ? '' : 'https://play.google.com/store/apps/details?id=com.lawcube.in';

const ConfirmationModal = ({ visible, onClose, onConfirm, message }: any) => {
    return (
        <Modal
            animationType='slide'
            statusBarTranslucent={true}
            transparent={true}
            visible={visible}
            onRequestClose={onClose}>
            <View className='items-center justify-center flex-1 bg-opacity-50 bg-black/30'>
                <View className='p-5 space-y-3 bg-white rounded-lg shadow-lg w-80 dark:bg-dark'>
                    <Text style={Global.text_bold} className='mb-4 text-lg text-center text-secondary dark:text-white'>
                        {message}
                    </Text>
                    <View className='flex-row justify-between px-4'>
                        <TouchableOpacity className='px-4 py-2 bg-gray-300 rounded' onPress={onClose}>
                            <Text style={Global.text_medium} className='text-secondary text-text15'>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity className='px-4 py-2 bg-[#FF2020] rounded' onPress={onConfirm}>
                            <Text style={Global.text_medium} className='text-white text-text15'>Confirm</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const Menu = () => {

    const { navigation, colorScheme, toggleColorScheme, setClientId, setToken, clientId } = useContext(ClientContext);
    const { setRegisteredItems } = useContext(LikeSaveContext);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalMessage, setModalMessage] = useState('');

    const [logoutDel, setLogoutDel] = useState('logout')

    const handleMenuBack = () => {
        navigation.goBack();
    }

    const onConfirm = () => {
        setModalVisible(false);
        if (logoutDel === 'logout') {
            handleLogout();
        } else {
            handleDeleteAccount();
        }
    }

    const handleDeleteAccount = async () => {
        await ClientAxiosInstance.delete(`/auth/${clientId}`)
        setToken(null);
        setClientId(null);
        setRegisteredItems(null);
        await AsyncStorage.removeItem('LAWCUBE_TOKEN');
        await AsyncStorage.removeItem('LAWCUBE_USERID');
    };

    const handleLogout = async () => {
        setToken(null);
        setClientId(null);
        setRegisteredItems(null);
        await AsyncStorage.removeItem('LAWCUBE_TOKEN');
        await AsyncStorage.removeItem('LAWCUBE_USERID');

        Toast.show({
            type: 'success',
            message: 'Logout successful',
            duration: 3000,
            position: 'bottom',
            animation: 'slide',
            icon: (
                <Image
                    source={require('../../assets/icons/logo/logo-light-big.png')}
                    style={{ width: 24, height: 24 }}
                    contentFit='contain'
                />
            ),
        });
    };

    const showConfirmation = (message: any, action: any) => {
        setModalMessage(message);
        setLogoutDel(action);
        setModalVisible(true);
    };

    const inviteFriends = () => {
        try {
            const options = {
                message: `Lawcube: Legal news, blogs, MCQs & moot courts for professionals & students. ${INVITE_LINK}`,
                title: 'Lawcube',
                subject: `Lawcube: Legal news, blogs, MCQs & moot courts for professionals & students.`,
            };

            Share.share(options)
                .then((res) => {
                    console.log('Shared successfully!', res);
                })
                .catch((err) => {
                    if (err) {
                        console.log('Error sharing : ', err);
                    }
                });

        } catch (error) {
            console.log("Invite friends error : ", error);
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar backgroundColor='#FDD066' barStyle='dark-content' />

            <View className='h-[50px] w-full flex-row justify-between items-center bg-primary px-primary'>
                <Text className='text-lg tracking-wide text-secondary' style={Global.text_bold}>Menu</Text>
                <TouchableOpacity activeOpacity={0.8} onPress={handleMenuBack} className='items-end justify-center w-8 h-8'>
                    <Ionicons name='close' size={30} color='black' />
                </TouchableOpacity>
            </View>

            <ScrollView showsVerticalScrollIndicator={false} className='w-full h-full'>

                <View className='mb-4'>

                    <View>
                        <MenuList />
                    </View>

                    <View className='flex-row items-center justify-between h-16 mt-3 px-primary'>
                        <Text style={Global.text_medium} className='tracking-wider text-secondary dark:text-white text-text16'>Dark Theme</Text>
                        <Switch
                            trackColor={{ true: '#a8a8a880', false: '#a8a8a880' }}
                            thumbColor={colorScheme === 'dark' ? '#fdd066' : '#f4f3f4'}
                            value={colorScheme === 'dark'}
                            onValueChange={toggleColorScheme}
                        />
                    </View>

                    <TouchableOpacity activeOpacity={0.8} className="flex-row items-center justify-between h-16 px-primary" onPress={() => navigation.navigate('HelpSupport')}>
                        <Text
                            style={Global.text_medium}
                            className="tracking-wider text-secondary dark:text-white text-text16">
                            Help and Support
                        </Text>
                        <Feather name="chevron-right" size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} className="flex-row items-center justify-between h-16 px-primary" onPress={() => navigation.navigate('PrivacyPolicies')} >
                        <Text
                            style={Global.text_medium}
                            className="tracking-wider text-secondary dark:text-white text-text16">
                            Privacy Policies
                        </Text>
                        <Feather name="chevron-right" size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} className="flex-row items-center justify-between h-16 px-primary" onPress={() => navigation.navigate('TermsConditions')} >
                        <Text
                            style={Global.text_medium}
                            className="tracking-wider text-secondary dark:text-white text-text16">
                            Terms & Condition
                        </Text>
                        <Feather name="chevron-right" size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} className="flex-row items-center justify-between h-16 px-primary" onPress={() => navigation.navigate('RefundPolicies')} >
                        <Text
                            style={Global.text_medium}
                            className="tracking-wider text-secondary dark:text-white text-text16">
                            Refund Policies
                        </Text>
                        <Feather name="chevron-right" size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} className="flex-row items-center justify-between h-16 px-primary" onPress={inviteFriends}>
                        <Text
                            style={Global.text_medium}
                            className="tracking-wider text-secondary dark:text-white text-text16">
                            Invite Friends
                        </Text>
                        <Feather name="chevron-right" size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} onPress={() => showConfirmation('Are you sure you want to delete your account?', 'delete')} className='flex-row items-center justify-between h-16 px-primary'>
                        <Text style={Global.text_medium} className='tracking-wider text-secondary dark:text-white text-text16'>Delete My Account</Text>
                        <Feather name='chevron-right' size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                    <TouchableOpacity activeOpacity={0.8} onPress={() => showConfirmation('Are you sure you want to logout?', 'logout')} className='flex-row items-center justify-between h-16 px-primary'>
                        <Text style={Global.text_medium} className='tracking-wider text-secondary dark:text-white text-text16'>Logout</Text>
                        <Feather name='chevron-right' size={25} color={colorScheme === 'light' ? '#2f2f2f' : '#fff'} />
                    </TouchableOpacity>

                </View>

            </ScrollView>

            <ConfirmationModal visible={modalVisible} onClose={() => setModalVisible(false)} onConfirm={onConfirm} message={modalMessage} />

        </View>
    )
}

export default Menu