import { View, Text, ScrollView, Pressable, TouchableOpacity } from 'react-native'
import React, { useContext, useRef } from 'react'
import { Image } from 'expo-image'
import CustomStatusBar from '../../../components/CustomStatusBar'
import Global from '../../../../globalStyle'
import { ClientContext } from '../../../context/ClientContext'
import { AntDesign, MaterialIcons } from '@expo/vector-icons'

const RefundPolicies = () => {

    const { navigation, colorScheme } = useContext(ClientContext);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../../../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>

                <View className='space-y-7 px-primary'>

                    <View className='flex-row items-center justify-between mt-5'>

                        <Pressable onPress={() => navigation.goBack()} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-secondary dark:border-cardLight'>
                            <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? "#F8F6F3" : "#2D2828"} />
                        </Pressable>

                        <View className='w-10/12'>
                            <Text className="text-center text-text20 text-secondary dark:text-white" style={Global.lora_bold}>Refund and Cancellation Policy</Text>
                        </View>

                        <Pressable pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center'>
                            <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? "#F8F6F3" : "#2D2828"} style={{ display: 'none' }} />
                        </Pressable>

                    </View>

                    <View className='space-y-4'>
                        <Text style={Global.text_medium} className='tracking-wider text-text17 text-secondary dark:text-white'>
                            This refund and cancellation policy outlines how you can cancel or seek a refund for a product / service that you have purchased through the Platform. Under this policy :
                        </Text>
                        <Text style={Global.text_regular} className='tracking-wide text-text16 text-secondary dark:text-white'>
                            1. In case of any refunds approved by LEX LEGENDS LAW ASSOCIATION, it will take 7 days for the refund to be processed to you.
                        </Text>
                    </View>

                </View>

            </ScrollView>

        </View>
    )
}

export default RefundPolicies;