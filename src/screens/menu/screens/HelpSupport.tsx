import { KeyboardAvoidingView, Linking, Platform, Pressable, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'
import CustomStatusBar from '../../../components/CustomStatusBar'
import Header from '../../../components/Header'
import Global from '../../../../globalStyle'
import { AntDesign, FontAwesome5 } from '@expo/vector-icons'
import ContactForm from '../components/ContactForm'
import { Image } from 'expo-image'
import { ClientContext } from '../../../context/ClientContext'
import { widthPercentageToDP } from 'react-native-responsive-screen'

const HelpSupport = () => {

    const { isAndroid, isLarge, colorScheme, navigation } = useContext(ClientContext);

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <Header search index={0} />

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >

                <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='flex-1'>

                    <View className='mb-8 space-y-8 px-primary'>

                        <View className='flex-row items-center justify-between mt-5'>

                            <Pressable onPress={() => navigation.goBack()} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-secondary dark:border-cardLight'>
                                <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? "#F8F6F3" : "#2D2828"} />
                            </Pressable>

                            <Text className="text-center text-text20 text-secondary dark:text-white" style={Global.lora_bold}>Help and Support</Text>

                            <Pressable pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center'>
                                <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? "#F8F6F3" : "#2D2828"} style={{ display: 'none' }} />
                            </Pressable>

                        </View>

                        <View style={{ height: isLarge ? widthPercentageToDP(30) : widthPercentageToDP(50) }}>
                            <Image
                                source={require('../../../assets/images/lexlegends.jpg')}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 6
                                }}
                                contentFit='cover'
                            />
                        </View>

                        <View className='flex-row items-start w-full'>

                            <View className='w-[12%]'>
                                <View className='items-center justify-center w-8 rounded-sm h-7 bg-primary'>
                                    <FontAwesome5 name="user-alt" size={20} color="white" />
                                </View>
                            </View>

                            <View className='w-[88%] space-y-3'>

                                <Text className='text-lg text-secondary dark:text-white' style={Global.text_bold}>Contact Details</Text>

                                <View className='w-full px-1 space-y-4'>

                                    <Text style={Global.text_regular} className='tracking-wide text-secondary dark:text-white text-text16'>
                                        Address :<Text style={Global.text_regular} className='tracking-wide text-secondary dark:text-white text-text16'>&nbsp;No.106E, Ground Floor, Mudichur Main Road, Tambaram, Chennai - 600045</Text>
                                    </Text>

                                    <View className='flex-row items-center'>
                                        <Text style={Global.text_regular} className='tracking-wide text-secondary dark:text-white text-text16'>
                                            Number :
                                        </Text>
                                        <Text>&nbsp;</Text>
                                        <TouchableOpacity activeOpacity={0.8} onPress={() => Linking.openURL(`tel:9884667086`)}>
                                            <Text style={Global.text_regular} className='tracking-wide underline text-secondary dark:text-white text-text16'>9884 66 70 86</Text>
                                        </TouchableOpacity>
                                    </View>

                                    <View className='flex-row items-center'>
                                        <Text style={Global.text_regular} className='tracking-wide text-secondary dark:text-white text-text16'>
                                            Email :
                                        </Text>
                                        <Text>&nbsp;</Text>
                                        <TouchableOpacity activeOpacity={0.8} onPress={() => Linking.openURL(`mailto:<EMAIL>`)}>
                                            <Text style={Global.text_regular} className='tracking-wide underline text-secondary dark:text-white text-text16'><EMAIL></Text>
                                        </TouchableOpacity>
                                    </View>

                                </View>

                            </View>

                        </View>

                        <View>
                            <ContactForm />
                        </View>

                    </View>

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default HelpSupport