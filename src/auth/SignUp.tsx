import { View, Text, <PERSON><PERSON>View, BackHandler, TouchableOpacity, KeyboardAvoidingView, Keyboard, } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import Global from '../../globalStyle';
import { Image } from 'expo-image';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Ionicons } from '@expo/vector-icons';
import FormOne from './signup/FormOne';
import FormTwo from './signup/FormTwo';
import axios from 'axios';
import { GlobalContext } from '../context/GlobalProvider';
import { ClientContext } from '../context/ClientContext';
import CustomStatusBar from '../components/CustomStatusBar';
import { capitalizeMode } from '../helpers/getCapitalize';
import moment from 'moment';

interface FormValues {
    email: string;
    mobile: string;
    password: string;
}

const SignUp = () => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { setLoading } = useContext(GlobalContext);
    const { navigation, colorScheme, isAndroid } = useContext(ClientContext);

    const [formOneValues, setFormOneValues] = useState<any>();

    const schema = yup.object().shape({
        email: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        mobile: yup
            .string()
            .required('Phone number is required')
            .matches(/^[0-9]+$/, 'Phone number must contain only digits')
            .min(10, 'Phone number must be 10 digits'),
        password: yup
            .string()
            .required('Password is required')
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_\-\.#])[A-Za-z\d@$!%*?&_\-\.#]{8,}$/,
                'Password must contain at least 8 characters, including uppercase, lowercase, number, and a special character'
            )
            .min(8, 'Password must be at least 8 characters'),
        agreeToTerms: yup.boolean().oneOf([true], 'You must agree to the terms'),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setError,
    } = useForm<FormValues>({
        resolver: yupResolver(schema),
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        try {
            Keyboard.dismiss();
            setLoading(true);

            const values = { ...formOneValues, ...data }
            const formattedDob = moment(values.dob).format('YYYY-MM-DD');

            const allData = { ...values, dob:formattedDob };

            const response = await axios.post(`${api_link}/auth/web/create`, {
                first_name: capitalizeMode(values.fname),
                last_name: capitalizeMode(values.lname),
                dob: formattedDob,
                occupation: values.occupation,
                email: values.email.toLowerCase(),
                mobile: values.mobile,
                password: values.password,
            })

            navigation.navigate('OtpVerify', { allData:allData,fromForgotPassword: false, userID: response.data.userID });

        } catch (error: any) {
            if (error.response) {
                const responseData = error.response.data.message;
                if (responseData === 'User already exists with the same email!')
                    setError('email', { type: 'manual', message: responseData });
                else if (responseData === 'User already exists with the same mobile!')
                    setError('mobile', { type: 'manual', message: responseData });
            }
        } finally {
            setLoading(false);
        }
    }

    const [formState, setFormState] = useState(1);

    useEffect(() => {
        const backAction = () => {
            if (formState === 2) {
                setFormState(1);
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [formState]);

    const handleBack = () => {
        if (formState === 2) {
            setFormState(1);
        } else {
            navigation.navigate('SignIn');
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >
                <ScrollView showsVerticalScrollIndicator={false} bounces={false} keyboardShouldPersistTaps="handled" className='w-full h-full pb-5 space-y-4 px-primary'>

                    <TouchableOpacity onPress={handleBack} className='absolute z-50 top-4'>
                        <Ionicons name='arrow-back-circle-outline' size={35} color={colorScheme === 'dark' ? "#fff" : "#2D2828"} />
                    </TouchableOpacity>

                    <Text className='mt-4 text-xl tracking-wider text-center text-secondary dark:text-white' style={Global.text_bold}>Sign Up</Text>

                    {formState === 1 ?
                        <FormOne
                            setFormState={setFormState}
                            formOneValues={formOneValues}
                            setFormOneValues={setFormOneValues}
                        /> :
                        <FormTwo
                            control={control}
                            errors={errors}
                            handleSubmit={handleSubmit}
                            onSubmit={onSubmit}
                        />
                    }

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default SignUp;
