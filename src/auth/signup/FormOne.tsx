import { View, Text, TouchableOpacity, StyleSheet, Modal, Pressable } from 'react-native'
import React, { useContext, useState } from 'react'
import Global from '../../../globalStyle';
import { Dropdown } from 'react-native-element-dropdown';
import { Controller, useForm } from 'react-hook-form';
import { TextInput } from 'react-native-paper';
import { ClientContext } from '../../context/ClientContext';
import Animated, { FadeInUp } from 'react-native-reanimated';
import dayjs from 'dayjs';
import DateTimePicker from 'react-native-ui-datepicker';
import { yupResolver } from '@hookform/resolvers/yup';

import * as yup from 'yup';

const data = [
    { label: 'Lawyer', value: 'Lawyer' },
    { label: 'Student', value: 'Student' },
    { label: 'Others', value: 'Others' }
]

interface FormProps {
    setFormState: React.Dispatch<React.SetStateAction<number>>;
    formOneValues: any;
    setFormOneValues: (val: any) => void;
}

interface FormValues {
    fname: string;
    lname: string;
    dob: Date;
    occupation: string;
}

const FormOne: React.FC<FormProps> = ({ setFormState, formOneValues, setFormOneValues }) => {

    const { colorScheme } = useContext(ClientContext);
    const [showDatePicker, setShowDatePicker] = useState(false);

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : 'white',
                        borderBottomColor: '#a8a8a8'
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item?.label}
                </Text>
            </View>
        );
    };

    const schema = yup.object().shape({
        fname: yup
            .string()
            .required('First Name is required')
            .matches(/^[a-zA-Z ]+$/, 'First Name must contain only letters')
            .min(3, 'First Name must be at least 3 characters')
            .max(50, 'First Name must be atmost 50 characters'),
        lname: yup
            .string()
            .required('Last Name is required')
            .matches(/^[a-zA-Z ]+$/, 'Last Name must contain only letters')
            .min(1, 'Last Name must be at least 1 character')
            .max(25, 'Last Name must be atmost 25 characters'),
        dob: yup
            .date()
            .required('Date of birth is required')
            .test('age', 'You must be at least 17 years old', function (value) {
                const today = new Date();
                const birthDate = new Date(value);
                let age = today.getFullYear() - birthDate.getFullYear();
                const m = today.getMonth() - birthDate.getMonth();
                if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                return age >= 17;
            }),
        occupation: yup
            .string()
            .required('Occupation is required')
            .oneOf(['Lawyer', 'Student', 'Others'], 'Select an valid occupation')
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
    } = useForm<FormValues>({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: any) => {
        try {
            setFormOneValues({ ...formOneValues, ...data });
            setFormState(2);
        } catch (error: any) {
            console.log(error);
        }
    }

    return (
        <View className='mt-4'>

            <Text className='text-base tracking-wide text-secondary dark:text-white' style={Global.text_regular}>
                Comprehensive range of specialized courses. Elevate your legal expertise with Lex Legends' comprehensive range of specialized courses.
            </Text>

            <View className='mt-4 space-y-5'>

                <Animated.View entering={FadeInUp.delay(500).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>First Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='First Name'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.fname?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="fname"
                            defaultValue={formOneValues?.fname}
                        />
                        {errors?.fname?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.fname?.message}</Text>
                        )}
                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(400).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Last Name</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 bg-transparent text-text17'
                                    placeholder='Last Name'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='text'
                                    onChangeText={onChange}
                                    onBlur={onBlur}
                                    value={value}
                                    error={!!errors?.lname?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="lname"
                            defaultValue={formOneValues?.lname}
                        />
                        {errors?.lname?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.lname?.message}</Text>
                        )}
                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(300).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>
                        Date of Birth
                    </Text>
                    <View>
                        <Controller
                            control={control}
                            name="dob"
                            defaultValue={formOneValues?.dob}
                            render={({ field: { onChange, value } }) => (
                                <>
                                    <Pressable
                                        onPress={() => setShowDatePicker(true)}
                                        className={`w-full bg-transparent text-text17 px-1 p-4 rounded-lg border-[1px] ${errors?.dob?.message ? 'border-red-500' : 'border-[#a8a8a8]'}`}

                                    >
                                        <Text className={`'w-full text-[#a8a8a8] text-text17 px-1' ${value ? `${colorScheme === 'dark' ? 'text-white' : 'text-black'}` : 'text-[#a8a8a8]'}`} style={Global.text_regular}>
                                            {value ? dayjs(value).format('DD-MM-YYYY') : 'Select Date'}
                                        </Text>
                                    </Pressable>
                                    {errors?.dob?.message && (
                                        <Text style={Global.text_medium} className='text-sm text-red-500'>
                                            * {errors?.dob?.message}
                                        </Text>
                                    )}

                                    <Modal
                                        visible={showDatePicker}
                                        transparent
                                        statusBarTranslucent
                                        animationType='fade'
                                        onRequestClose={() => setShowDatePicker(false)}
                                    >
                                        <View className='items-center justify-center flex-1 bg-black/80'>
                                            <View className={`rounded-lg p-2 max-w-md mx-10 ${colorScheme === 'dark' ? 'bg-[#4f4d48]' : 'bg-[#fffcf4]'} `}>
                                                <DateTimePicker
                                                    mode="single"
                                                    date={value || new Date()}
                                                    onChange={(params) => {
                                                        const dob = dayjs(params.date).format('YYYY-MM-DD');
                                                        setShowDatePicker(false);
                                                        onChange(dob);
                                                    }}
                                                    maxDate={new Date()}
                                                    headerButtonsPosition='right'
                                                    todayContainerStyle={{ borderRadius: 10, borderColor: '#f8dd9f', borderWidth: 1 }}
                                                    dayContainerStyle={{ borderRadius: 10, borderColor: '#f8dd9f' }}
                                                    calendarTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-Bold' }}
                                                    selectedTextStyle={styles.selectedText}
                                                    selectedItemColor="#f8dd9f"
                                                    headerTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-ExtraBold' }}
                                                    headerButtonColor={`${colorScheme === 'dark' ? '#f8dd9f' : "#4f4d48"}`}
                                                    headerButtonStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#f8dd9f'}`, borderRadius: 7, padding: 6 }}
                                                    monthContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#fffcf4'}` }}
                                                    yearContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#fffcf4'}` }}
                                                    weekDaysContainerStyle={styles.weekDaysContainer}
                                                    weekDaysTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-Bold' }}
                                                    headerContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#615f5b' : '#faf4e3'}`, borderRadius: 10 }}
                                                />
                                            </View>
                                            <TouchableOpacity onPress={() => setShowDatePicker(false)} className=' top-5 border border-[#f8dd9f] self-center rounded-full' activeOpacity={0.5}>
                                                <Text style={Global.text_medium} className='px-10 py-3 text-center text-[#f8dd9f]'>Close</Text>
                                            </TouchableOpacity>
                                        </View>
                                    </Modal>
                                </>
                            )}
                        />
                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Occupation</Text>
                    <View>
                        <Controller
                            control={control}
                            name="occupation"
                            defaultValue={formOneValues?.occupation}
                            rules={{ required: 'Occupation is required' }}
                            render={({ field: { onChange, value } }) => (
                                <Dropdown
                                    style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: errors?.occupation?.message ? 'red' : '#a8a8a8' }]}
                                    selectedTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                    }}
                                    data={data}
                                    search={false}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Select"
                                    renderItem={renderItem}
                                    onChange={item => {
                                        onChange(item.value);
                                    }}
                                    itemContainerStyle={{
                                        borderBottomWidth: 0.5,
                                        borderBottomColor: '#a8a8a8',
                                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : 'white',
                                    }}
                                    itemTextStyle={{
                                        color: colorScheme === 'light' ? '#000' : 'white',
                                        fontSize: 17
                                    }}
                                    containerStyle={{
                                        borderRadius: 6,
                                        overflow: 'hidden',
                                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : 'white',
                                        borderColor: '#a8a8a8'
                                    }}
                                    showsVerticalScrollIndicator={false}
                                    placeholderStyle={{
                                        color: '#a8a8a8',
                                    }}
                                    keyboardAvoiding
                                    dropdownPosition='top'
                                    fontFamily='DMSans-Regular'
                                    value={value}
                                />
                            )}
                        />
                        {errors?.occupation?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.occupation?.message}</Text>
                        )}
                    </View>
                </Animated.View>

            </View>

            <Animated.View entering={FadeInUp.delay(100).duration(800).springify()} className='items-center mt-5 mb-5'>
                <TouchableOpacity
                    onPress={handleSubmit(onSubmit)}
                    activeOpacity={0.8}
                    className='bg-primary h-[50px]  rounded-full items-center justify-center px-5'
                >
                    <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Continue</Text>
                </TouchableOpacity>
            </Animated.View>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: 55,
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    },
    selectedText: {
        color: '#2d2828',
        fontFamily: 'DMSans-ExtraBold',
        fontWeight: '700',
    },
    weekDaysContainer: {
        borderBottomColor: '#dedede'
    },
});

export default FormOne