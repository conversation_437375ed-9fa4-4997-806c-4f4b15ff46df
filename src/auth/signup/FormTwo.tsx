import { View, Text, TouchableOpacity, Modal } from 'react-native'
import React, { useContext, useState } from 'react'
import Global from '../../../globalStyle'
import { Controller, UseFormHandleSubmit } from 'react-hook-form';
import { TextInput, Checkbox } from 'react-native-paper';

import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../../context/ClientContext';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { StyleSheet } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import TermsConditions from '../TermsConditions';

interface FormProps {
    control: any;
    errors: any;
    handleSubmit: UseFormHandleSubmit<any>;
    onSubmit: (value: any) => void;
}

const FormTwo: React.FC<FormProps> = ({ control, errors, handleSubmit, onSubmit }) => {

    const { colorScheme } = useContext(ClientContext);
    const [showPassword, setShowPassword] = useState(false);
    const [isModalVisible, setModalVisible] = useState(false);

    const termsAndConditions = () => {
        setModalVisible(true);
    };

    const closeModal = () => {
        setModalVisible(false);
    };

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label} ({item.name})
                </Text>
            </View>
        );
    };

    return (
        <View className='mt-5 space-y-5'>

            <View className='space-y-4'>

                <Animated.View entering={FadeInUp.delay(500).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>E-Mail</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='E-Mail'
                                    placeholderTextColor='#a8a8a8'
                                    inputMode='email'
                                    autoCapitalize='none'
                                    onChangeText={item => (
                                        onChange(item.toLowerCase())
                                    )}
                                    onBlur={onBlur}
                                    value={value.toLowerCase()}
                                    error={!!errors?.email?.message}
                                    mode='outlined'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name="email"
                            defaultValue=""
                        />
                        {errors?.email?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.email?.message}</Text>
                        )}
                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(400).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Mobile Number</Text>

                    <View>
                        <View className='flex-row items-center'>

                            <View>
                                <Controller
                                    control={control}
                                    name="country_code"
                                    defaultValue='91'
                                    render={({ field: { onChange, value } }) => (
                                        <Dropdown
                                            style={[styles.dropdown, { backgroundColor: 'transparent', borderColor: errors.mobile?.message ? "red" : '#a8a8a8', }]}
                                            selectedTextStyle={{
                                                color: colorScheme === 'light' ? '#000' : '#fff',
                                            }}
                                            data={[]}
                                            disable
                                            search
                                            searchPlaceholder='Search Country Code'
                                            inputSearchStyle={{
                                                borderRightWidth: 0,
                                                borderLeftWidth: 0,
                                                borderTopWidth: 0
                                            }}
                                            labelField="label"
                                            valueField="value"
                                            placeholder="(+91)"
                                            renderItem={renderItem}
                                            onChange={item => {
                                                onChange(item.value);
                                            }}
                                            itemContainerStyle={{
                                                borderBottomWidth: 0.5,
                                                borderBottomColor: '#a8a8a8',
                                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                            }}
                                            itemTextStyle={{
                                                color: colorScheme === 'light' ? '#000' : '#fff',
                                                fontSize: 17
                                            }}
                                            containerStyle={{
                                                borderRadius: 8,
                                                overflow: 'hidden',
                                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                            }}
                                            showsVerticalScrollIndicator={false}
                                            placeholderStyle={{
                                                color: colorScheme === 'dark' ? '#fff' : '#2d2828',
                                            }}
                                            keyboardAvoiding
                                            dropdownPosition='auto'
                                            fontFamily='DMSans-Regular'
                                            value={value}
                                        />
                                    )}
                                />
                            </View>

                            <View className='w-[82%] absolute right-0'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full pl-4 tracking-wider bg-transparent text-text17'
                                            placeholder='Mobile Number'
                                            placeholderTextColor='#a8a8a8'
                                            inputMode='numeric'
                                            maxLength={10}
                                            onChangeText={onChange}
                                            onBlur={onBlur}
                                            value={value}
                                            error={!!errors?.mobile?.message}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderTopWidth: 1.2,
                                                borderBottomWidth: 1.2,
                                                borderRightWidth: 1.2,
                                                borderLeftWidth: 0,
                                                borderTopRightRadius: 8,
                                                borderBottomRightRadius: 8,
                                                borderTopLeftRadius: 0,
                                                borderBottomLeftRadius: 0,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="mobile"
                                    defaultValue=''
                                />
                            </View>

                        </View>

                        {errors?.mobile?.message && (
                            <Text style={Global.text_medium} className='text-sm text-red-500'>* {errors?.mobile?.message}</Text>
                        )}
                    </View>

                </Animated.View>

                <Animated.View entering={FadeInUp.delay(300).duration(800).springify()} className='space-y-2'>
                    <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Password</Text>
                    <View>
                        <View className='flex-row items-center justify-between'>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{
                                            fontFamily: 'DMSans-Regular'
                                        }}
                                        className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                        placeholder='Password'
                                        placeholderTextColor='#a8a8a8'
                                        secureTextEntry={!showPassword}
                                        onChangeText={onChange}
                                        onBlur={onBlur}
                                        value={value}
                                        error={!!errors?.password?.message}
                                        mode='outlined'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{
                                            borderWidth: 1.2,
                                            borderRadius: 8,
                                        }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                    />
                                )}
                                name="password"
                                defaultValue=""
                            />
                            <TouchableOpacity activeOpacity={0.8} onPress={() => setShowPassword(!showPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                <Ionicons name={showPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            </TouchableOpacity>
                        </View>
                        {errors.password && (
                            <View>
                                {errors.password.types?.['has-lowercase'] && <Text className='text-red-500 '>* {errors.password.types['has-lowercase']}</Text>}
                                {errors.password.types?.['has-uppercase'] && <Text className='text-red-500 '>* {errors.password.types['has-uppercase']}</Text>}
                                {errors.password.types?.['has-number'] && <Text className='text-red-500 '>* {errors.password.types['has-number']}</Text>}
                                {errors.password.types?.['has-special-char'] && <Text className='text-red-500 '>* {errors.password.types['has-special-char']}</Text>}
                                {errors.password.message && <Text className='text-red-500 '>* {errors.password.message}</Text>}
                            </View>
                        )}

                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(300).duration(800).springify()} className='flex-row items-center space-x-2'>
                    <Controller
                        control={control}
                        name="agreeToTerms"
                        defaultValue={false}
                        render={({ field: { onChange, value } }) => (
                            <Checkbox.Android
                                status={value ? 'checked' : 'unchecked'}
                                onPress={() => onChange(!value)}
                                color='#FDD066'
                                underlayColor='transparent'
                            />
                        )}
                    />
                    <Text style={Global.text_medium} className='text-base text-secondary dark:text-white'>I agree to all the <Text className='underline ' onPress={termsAndConditions}>Terms & Condition</Text></Text>
                </Animated.View>

                {errors?.agreeToTerms?.message && (
                    <Text style={Global.text_medium} className='text-sm text-red-500 -top-5'>* {errors?.agreeToTerms?.message}</Text>
                )}

            </View>

            <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='items-center'>
                <TouchableOpacity
                    onPress={handleSubmit(onSubmit)}
                    activeOpacity={0.8}
                    className='bg-primary h-[50px]  rounded-full items-center justify-center px-5 mb-5'
                >
                    <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Continue</Text>
                </TouchableOpacity>
            </Animated.View>

            <Modal
                visible={isModalVisible}
                animationType="slide"
                onRequestClose={closeModal}
                statusBarTranslucent
                transparent={true}
            >
                <View className='justify-end flex-1 bg-black/50'>
                    <View className='bg-white dark:bg-dark h-[90%] rounded-tr-xl rounded-tl-xl'>
                        <TouchableOpacity onPress={closeModal} className='self-end ' >
                            <Text style={Global.text_regular} className="text-primary px-[14px] underline py-2 text-[15px]">Close</Text>
                        </TouchableOpacity>
                        <TermsConditions />
                    </View>
                </View>
            </Modal>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: 56,
        borderColor: '#a8a8a8',
        borderLeftWidth: 1.2,
        borderTopWidth: 1.2,
        borderBottomWidth: 1.2,
        borderTopLeftRadius: 8,
        borderBottomLeftRadius: 8,
        paddingLeft: 16,
        paddingRight: '70%',
        overflow: 'hidden',
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default FormTwo