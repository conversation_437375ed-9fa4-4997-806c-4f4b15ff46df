import { View, Text, ScrollView, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native'
import React, { useContext, useState } from 'react'
import { Image } from 'expo-image';
import Global from '../../globalStyle'

import { Controller, useForm } from 'react-hook-form'
import { TextInput } from 'react-native-paper'

import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../context/ClientContext'
import CustomStatusBar from '../components/CustomStatusBar'
import { GlobalContext } from '../context/GlobalProvider'
import Animated, { FadeInUp } from 'react-native-reanimated'
import { ClientAxiosInstance } from '../lib/axiosInstance'
import { Toast } from '../components/Toast'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { LikeSaveContext } from '../context/LikeSaveContext'

const ChangePassword = () => {

    const { navigation, colorScheme, setToken, setClientId } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);
    const { setRegisteredItems } = useContext(LikeSaveContext);

    const [apiResError, setApiResError] = useState('');

    const [showOldPassword, setShowOldPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const schema = yup.object().shape({
        oldpassword: yup
            .string()
            .required('Old Password is required'),
        newpassword: yup
            .string()
            .required('New Password is required')
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_\-\.#])[A-Za-z\d@$!%*?&_\-\.#]{8,}$/,
                'Password must contain at least 8 characters, including uppercase, lowercase, number, and a special character'
            )
            .min(8, 'Password must be at least 8 characters'),
        confirmpassword: yup
            .string()
            .oneOf([yup.ref('newpassword')], 'Passwords must match')
            .required('Confirm password is required')
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm({
        resolver: yupResolver(schema),
    });

    const handleLogout = async () => {

        setToken(null);
        setClientId(null);
        setRegisteredItems(null);
        await AsyncStorage.removeItem('LAWCUBE_TOKEN');
        await AsyncStorage.removeItem('LAWCUBE_USERID');

        Toast.show({
            type: 'success',
            message: 'Logout successful',
            duration: 3000,
            position: 'bottom',
            animation: 'slide',
            icon: (
                <Image
                    source={require('../assets/icons/logo/logo-light-big.png')}
                    style={{ width: 24, height: 24 }}
                    contentFit='contain'
                />
            ),
        });
    };

    const onSubmit = async (data: any) => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.post(`/auth/changepassword`, {
                old_password: data?.oldpassword?.trim(),
                new_password: data?.newpassword?.trim()
            })
            Toast.show({
                type: 'success',
                message: 'Password updated',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
            handleLogout();

        } catch (error: any) {
            console.log(error.response.data.message)
            setApiResError(error.response.data.message);

        } finally {
            setLoading(false);
        }
    }

    const handleCancel = () => {
        reset();
        navigation.goBack();
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }}>

                <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='w-full h-full space-y-6 px-primary'>

                    <View className='flex-row items-center justify-between w-full mt-5'>

                        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()} className='h-8 w-8 items-center justify-center rounded-full border-[1px] border-secondary dark:border-white'>
                            <Ionicons name="arrow-back" size={22} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                        </TouchableOpacity>

                        <Animated.Text entering={FadeInUp.delay(200).duration(800).springify()} className='text-xl tracking-wider text-secondary dark:text-white' style={Global.text_bold}>Change Password</Animated.Text>

                        <View className='w-8 h-8'>
                            <Ionicons name="arrow-back-circle-outline" size={34} color={colorScheme === 'dark' ? '#fff' : '#000'} style={{ display: 'none' }} />
                        </View>

                    </View>

                    <Animated.View entering={FadeInUp.delay(400).duration(800).springify()} className='space-y-2'>
                        <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Old Password</Text>
                        <View>
                            <View className='flex-row items-center justify-between'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                            placeholder='Password'
                                            placeholderTextColor='#a8a8a8'
                                            secureTextEntry={!showOldPassword}
                                            onChangeText={(value) => {
                                                onChange(value);
                                                setApiResError('');
                                            }}
                                            onBlur={onBlur}
                                            error={!!errors.oldpassword?.message}
                                            value={value}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderWidth: 1.2,
                                                borderRadius: 8,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="oldpassword"
                                    defaultValue=""
                                />
                                <TouchableOpacity activeOpacity={0.8} onPress={() => setShowOldPassword(!showOldPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                    <Ionicons name={showOldPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                </TouchableOpacity>
                            </View>
                            {(errors?.oldpassword?.message || apiResError === 'Incorrect old password!') && (
                                <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.oldpassword?.message || apiResError}</Text>
                            )}
                        </View>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(350).duration(800).springify()} className='space-y-2'>
                        <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>New Password</Text>
                        <View>
                            <View className='flex-row items-center justify-between'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                            placeholder='Password'
                                            placeholderTextColor='#a8a8a8'
                                            secureTextEntry={!showNewPassword}
                                            onChangeText={(value) => {
                                                onChange(value);
                                                setApiResError('');
                                            }}
                                            onBlur={onBlur}
                                            error={!!errors.newpassword?.message}
                                            value={value}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderWidth: 1.2,
                                                borderRadius: 8,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="newpassword"
                                    defaultValue=""
                                />
                                <TouchableOpacity activeOpacity={0.8} onPress={() => setShowNewPassword(!showNewPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                    <Ionicons name={showNewPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                </TouchableOpacity>
                            </View>
                            {(errors?.oldpassword?.message || apiResError === 'New password cannot be the same as the old password') && (
                                <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.oldpassword?.message || apiResError}</Text>
                            )}
                        </View>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(300).duration(800).springify()} className='space-y-2'>
                        <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Confirm New Password</Text>
                        <View>
                            <View className='flex-row items-center justify-between'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                            placeholder='Password'
                                            placeholderTextColor='#a8a8a8'
                                            secureTextEntry={!showConfirmPassword}
                                            onChangeText={(value) => {
                                                onChange(value);
                                                setApiResError('');
                                            }}
                                            onBlur={onBlur}
                                            error={!!errors.confirmpassword?.message}
                                            value={value}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderWidth: 1.2,
                                                borderRadius: 8,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="confirmpassword"
                                    defaultValue=""
                                />
                                <TouchableOpacity activeOpacity={0.8} onPress={() => setShowConfirmPassword(!showConfirmPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                    <Ionicons name={showConfirmPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                </TouchableOpacity>
                            </View>
                            {errors?.confirmpassword?.message && (
                                <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.confirmpassword?.message}</Text>
                            )}
                        </View>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='mt-5'>
                        <TouchableOpacity
                            onPress={handleSubmit(onSubmit)}
                            activeOpacity={0.8}
                            className='bg-primary h-[57px] rounded-full items-center justify-center px-5'
                        >
                            <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Confirm</Text>
                        </TouchableOpacity>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='my-5'>
                        <TouchableOpacity
                            onPress={handleCancel}
                            activeOpacity={0.8}
                            className='bg-transparent border-[1px] border-red-500 h-[57px] rounded-full items-center justify-center px-5'
                        >
                            <Text className='tracking-wide text-red-500 text-text16' style={Global.text_bold}>Cancel</Text>
                        </TouchableOpacity>
                    </Animated.View>

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default ChangePassword