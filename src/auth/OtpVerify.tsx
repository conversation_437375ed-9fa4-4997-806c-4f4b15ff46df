import { View, Text, StyleSheet, TouchableOpacity, TextInput, Keyboard } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { ScrollView } from 'react-native'
import Global from '../../globalStyle'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import axios from 'axios'
import { ClientContext } from '../context/ClientContext'
import { GlobalContext } from '../context/GlobalProvider'
import CustomStatusBar from '../components/CustomStatusBar'
import Animated, { FadeInUp } from 'react-native-reanimated'
import Ionicons from '@expo/vector-icons/Ionicons'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Toast } from '../components/Toast'
import { capitalizeMode } from '../helpers/getCapitalize'
import { Image } from 'expo-image';

const OtpVerify = ({ route }: any) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { navigation, colorScheme, setToken, setClientId } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const forgotPassword = route.params?.fromForgotPassword || false;
    const userId = route.params?.userId || '';
    const [userID, setUserID] = useState(route.params?.userID);
    const allData = route.params?.allData || {};

    const [timer, setTimer] = useState(30);
    const [showResendButton, setShowResendButton] = useState(false);

    const schema = yup.object().shape({
        otp: yup.string().required('OTP is required').length(4, 'OTP must be 4 digits'),
    });

    const {
        control,
        formState: { errors },
        handleSubmit,
        setValue,
        setError
    } = useForm({
        resolver: yupResolver(schema),
        defaultValues: { otp: '' },
    });

    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (timer > 0) {
            interval = setInterval(() => {
                setTimer(timer - 1);
            }, 1000);
        } else {
            setShowResendButton(true);
        }
        return () => clearInterval(interval);
    }, [timer]);

    const resetTimer = () => {
        setTimer(20);
        if (!forgotPassword) resendSignupOTP();
        else resendForgotOTP();
        setShowResendButton(false);
    };

    const [otp, setOtp] = useState(['', '', '', '']);
    const inputRefs: React.RefObject<TextInput>[] = Array(4).fill(0).map((_, i) => useRef(null));

    const handleKeyPress = (index: number, key: any) => {
        if (key === 'Backspace' && index > 0) {
            const newOtp = [...otp];
            newOtp[index] = '';
            setOtp(newOtp);
            inputRefs[index - 1]?.current?.focus();
        }
    };

    const handleChangeText = (index: number, text: string) => {
        const newOtp = [...otp];
        if (text.length > 1) {
            const digits = text.split('');
            digits.forEach((digit, idx) => {
                if (index + idx < 4) {
                    newOtp[index + idx] = digit;
                }
            });
            setOtp(newOtp);
            setValue('otp', newOtp.join(''));
            if (index + digits.length - 1 < 4) {
                inputRefs[index + digits.length - 1]?.current?.focus();
            }
        } else {
            newOtp[index] = text;
            setOtp(newOtp);
            setValue('otp', newOtp.join(''));

            if (text.length > 0 && index < 3) {
                inputRefs[index + 1]?.current?.focus();
            } else if (text.length === 0 && index > 0) {
                inputRefs[index - 1]?.current?.focus();
            }
        }
    };

    const setLoginTrue = async (token: string, userId: string) => {
        try {
            await AsyncStorage.setItem('LAWCUBE_TOKEN', token);
            await AsyncStorage.setItem('LAWCUBE_USERID', userId);
        } catch (error) {
            console.log("AsyncStorage Token : ", error);
        }
    };

    const resendForgotOTP = async () => {
        try {
            setLoading(true);
            await axios.post(`${api_link}/auth/forgetpassword`, {
                email_or_mobile: userId?.trim()
            })

        } catch (error) {
            console.log('Resend forgot password otp error : ', error);
        } finally {
            setLoading(false);
        }
    }

    const resendSignupOTP = async () => {
        try {
            setLoading(true);
            const response = await axios.post(`${api_link}/auth/web/create`, {
                first_name: capitalizeMode(allData?.fname),
                last_name: capitalizeMode(allData?.lname),
                dob: allData?.formattedDob,
                occupation: allData?.occupation,
                email: allData?.email.toLowerCase(),
                mobile: allData?.mobile,
                password: allData?.password,
            });
            setUserID(response.data.userID);

        } catch (error) {
            console.log('Resend signup otp error : ', error);
        } finally {
            setLoading(false);
        }
    }

    const onSubmit = async () => {
        try {
            Keyboard.dismiss();
            setLoading(true);
            const otpNumber = parseInt(otp.join(''), 10);
            const response = await axios.post(
                forgotPassword ? `${api_link}/auth/verify` : `${api_link}/auth/web/otp/verify`,
                forgotPassword ? { otp: otpNumber, email_or_mobile: userId } : { otp: otpNumber, userID: userID }
            );

            if (forgotPassword) {
                navigation.navigate('ResetPassword', { userId: userId });
                Toast.show({
                    type: 'success',
                    message: 'OTP verification successful',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }
            else {
                const token = response.data.token;
                setToken(token);

                const userId = response.data.userId;
                setClientId(userId);

                await setLoginTrue(token, userId);

                Toast.show({
                    type: 'success',
                    message: 'Signup successful',
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
            }

        } catch (error: any) {
            if (error.response) {
                const responseData = error.response.data.error;
                setError("otp", { type: 'manual', message: responseData });
            }
        } finally {
            setLoading(false);
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='w-full h-full space-y-8 px-primary'>

                <View className='flex-row items-center justify-between w-full mt-5'>

                    <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()} className='h-8 w-8 items-center justify-center rounded-full border-[1px] border-secondary dark:border-white'>
                        <Ionicons name="arrow-back" size={22} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                    </TouchableOpacity>

                    <Animated.Text
                        entering={FadeInUp.delay(200).duration(800).springify()}
                        className='text-xl tracking-wider text-secondary dark:text-white'
                        style={Global.text_medium}
                    >
                        Verification
                    </Animated.Text>

                    <View className='w-8 h-8'>
                        <Ionicons name="arrow-back-circle-outline" size={34} color={colorScheme === 'dark' ? '#fff' : '#000'} style={{ display: 'none' }} />
                    </View>

                </View>

                <View className='space-y-3'>

                    <Animated.Text entering={FadeInUp.delay(200).duration(800).springify()} className='tracking-wide text-center text-text17 text-secondary dark:text-white' style={Global.text_regular}>
                        Enter the <Text style={Global.text_bold}>OTP</Text> which is sent to your email
                    </Animated.Text>

                </View>

                <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='flex-row w-full h-16 py-1 overflow-hidden border-secondary dark:border-slate-100 justify-evenly'>
                    <Controller
                        control={control}
                        name="otp"
                        render={() => (
                            <View className='flex-row w-full h-full justify-evenly'>
                                {[...Array(4)].map((_, index) => (
                                    <TextInput
                                        style={{
                                            fontFamily: 'DMSans-Regular'
                                        }}
                                        key={index}
                                        autoCapitalize='none'
                                        maxLength={4}
                                        autoComplete="sms-otp"
                                        textContentType="oneTimeCode"
                                        keyboardType='numeric'
                                        className='h-full py-2 text-xl text-center border rounded-md w-14 border-secondary dark:border-gray-400 text-secondary dark:text-white'
                                        ref={inputRefs[index]}
                                        onKeyPress={({ nativeEvent }) => handleKeyPress(index, nativeEvent.key)}
                                        onChangeText={(text) => handleChangeText(index, text)}
                                        value={otp[index]}
                                    />
                                ))}
                            </View>
                        )}
                    />
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='w-full'>
                    {timer > 0 && (
                        <Text style={[Global.text_regular, Global.text_bold]} className="text-base font-medium text-center text-gray-600">
                            Resend OTP ({timer} sec)
                        </Text>
                    )}
                    {showResendButton && (
                        <Text
                            className="text-base font-bold text-center underline text-primary"
                            onPress={resetTimer}>
                            Resend OTP
                        </Text>
                    )}
                </Animated.View>

                {errors?.otp?.message && (
                    <View className='w-full py-5 bg-[#F9E4E4] items-center rounded-md'>
                        <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors.otp.message}</Text>
                    </View>
                )}

                <View className='items-center mt-5'>
                    <TouchableOpacity
                        onPress={handleSubmit(onSubmit)}
                        activeOpacity={0.8}
                        className='bg-primary h-[50px]  rounded-full items-center justify-center px-5 mb-5'
                    >
                        <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Verify</Text>
                    </TouchableOpacity>
                </View>

            </ScrollView>

        </View>
    )
}

const styles = StyleSheet.create({
    borderStyleBase: {
        width: 30,
        height: 45
    },

    borderStyleHighLighted: {
        borderColor: "#03DAC6",
    },

    underlineStyleBase: {
        width: 30,
        height: 45,
        borderWidth: 0,
        borderBottomWidth: 1,
    },

    underlineStyleHighLighted: {
        borderColor: "#03DAC6",
    },
});

export default OtpVerify