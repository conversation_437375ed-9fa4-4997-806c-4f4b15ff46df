import { View, Text, TouchableOpacity, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native'
import React, { useContext, useState } from 'react'
import { Image } from 'expo-image';
import Global from '../../globalStyle'

import Ionicons from '@expo/vector-icons/Ionicons'
import { Controller, useForm } from 'react-hook-form'
import { TextInput } from 'react-native-paper'
import axios from 'axios'
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as yup from 'yup'

import { yupResolver } from '@hookform/resolvers/yup'
import { ClientContext } from '../context/ClientContext'
import { GlobalContext } from '../context/GlobalProvider'
import CustomStatusBar from '../components/CustomStatusBar'

import Animated, { FadeInUp } from 'react-native-reanimated'
import Button from '../components/Button'
import { Toast } from '../components/Toast'


const SignIn = () => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { navigation, colorScheme, setToken, setClientId, isAndroid } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const [showPassword, setShowPassword] = useState(false);

    const schema = yup.object().shape({
        userId: yup
            .string()
            .required('E-Mail is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
        password: yup
            .string()
            .required('Password is required')
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setError
    } = useForm({
        resolver: yupResolver(schema),
    });

    const setLoginTrue = async (token: string, userId: string) => {
        try {
            await AsyncStorage.setItem('LAWCUBE_TOKEN', token);
            await AsyncStorage.setItem('LAWCUBE_USERID', userId);
        } catch (error) {
            console.log("AsyncStorage Token : ", error);
        }
    };

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true);
            const response = await axios.post(`${api_link}/auth/web/signin`, {
                email_or_mobile: data.userId?.trim()?.toLowerCase(),
                password: data.password?.trim()
            });

            const token = response.data.token;
            setToken(token);

            const userId = response.data.userId;
            setClientId(userId);

            await setLoginTrue(token, userId);

            Toast.show({
                type: 'success',
                message: 'Signin successful',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

        } catch (error: any) {
            if (error.response) {
                const responseData = error.response.data.message;
                console.log("Signin error : ", error.response.data);
                if (responseData === 'Incorrect password!' || responseData === 'User not found!') {
                    setError('userId', { type: 'manual', message: 'Invalid email or password!' });
                    setError('password', { type: 'manual', message: 'Invalid email or password!' });
                }
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >
                <ScrollView nestedScrollEnabled showsVerticalScrollIndicator={false} bounces={false} keyboardShouldPersistTaps="handled" className='w-full h-full space-y-6 px-primary'>

                    <Text className='mt-5 text-xl tracking-wider text-center text-secondary dark:text-white' style={Global.text_bold}>Log In</Text>

                    <Animated.View entering={FadeInUp.delay(400).duration(800).springify()} className='space-y-2'>
                        <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>E-Mail</Text>
                        <View>
                            <Controller
                                control={control}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        contentStyle={{
                                            fontFamily: 'DMSans-Regular'
                                        }}
                                        className='w-full px-1 tracking-wide bg-transparent text-text17'
                                        placeholder='E-Mail'
                                        placeholderTextColor='#a8a8a8'
                                        inputMode='email'
                                        autoCapitalize='none'
                                        onChangeText={item => (
                                            onChange(item.toLowerCase())
                                        )}
                                        onBlur={onBlur}
                                        error={!!errors?.userId?.message}
                                        value={value.toLowerCase()}
                                        mode='outlined'
                                        activeOutlineColor='#a8a8a8'
                                        outlineStyle={{
                                            borderWidth: 1.2,
                                            borderRadius: 8
                                        }}
                                        outlineColor='#a8a8a8'
                                        textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                    />
                                )}
                                name={'userId'}
                                defaultValue=""
                            />
                        </View>
                    </Animated.View>

                    <View className='space-y-3'>

                        <Animated.View entering={FadeInUp.delay(300).duration(800).springify()} className='space-y-2'>
                            <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Password</Text>
                            <View className='flex-row items-center justify-between'>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, onBlur, value } }) => (
                                        <TextInput
                                            contentStyle={{
                                                fontFamily: 'DMSans-Regular'
                                            }}
                                            className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                            placeholder='Password'
                                            placeholderTextColor='#a8a8a8'
                                            secureTextEntry={!showPassword}
                                            onChangeText={onChange}
                                            onBlur={onBlur}
                                            error={!!errors?.password?.message}
                                            value={value}
                                            mode='outlined'
                                            activeOutlineColor='#a8a8a8'
                                            outlineStyle={{
                                                borderWidth: 1.2,
                                                borderRadius: 8,
                                            }}
                                            outlineColor='#a8a8a8'
                                            textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                        />
                                    )}
                                    name="password"
                                    defaultValue=""
                                />
                                <TouchableOpacity activeOpacity={0.8} onPress={() => setShowPassword(!showPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                    <Ionicons name={showPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                </TouchableOpacity>
                            </View>
                        </Animated.View>

                        {(errors.userId?.message || errors.password?.message) &&
                            <View>
                                <Text className='text-sm text-red-500' style={Global.text_medium}>* {errors.userId?.message || errors.password?.message}</Text>
                            </View>
                        }

                    </View>

                    <Animated.View entering={FadeInUp.delay(200).duration(800).springify()}>
                        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.navigate('ForgotPassword')}>
                            <Text className='tracking-wide underline text-secondary dark:text-white text-text16' style={Global.text_medium}>Forgot Password ?</Text>
                        </TouchableOpacity>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='flex-row items-center space-x-2'>
                        <Text className='text-text16 text-secondary dark:text-white' style={Global.text_medium}>Don't have an account ?</Text>
                        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.navigate('SignUp')}>
                            <Text className='tracking-wide text-text16 text-secondary dark:text-white' style={Global.text_bold}>Sign Up</Text>
                        </TouchableOpacity>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(100).duration(800).springify()} className='items-center mt-5 mb-5'>
                        <Button
                            title='Continue'
                            onPress={handleSubmit(onSubmit)}
                            height={50}
                            paddingX={25}
                        />
                    </Animated.View>

                </ScrollView>
            </KeyboardAvoidingView>

        </View>
    )
}

export default SignIn