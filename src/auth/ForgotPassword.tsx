import { View, Text, TouchableOpacity, Keyboard } from 'react-native'
import React, { useContext } from 'react'
import { Image } from 'expo-image';
import Global from '../../globalStyle'

import { Controller, useForm } from 'react-hook-form'
import { TextInput } from 'react-native-paper'
import axios from 'axios'

import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../context/ClientContext'
import CustomStatusBar from '../components/CustomStatusBar'
import { GlobalContext } from '../context/GlobalProvider'
import Animated, { FadeInUp } from 'react-native-reanimated'

const ForgotPassword = () => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { navigation, colorScheme } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const schema = yup.object().shape({
        userId: yup
            .string()
            .required('Email is required')
            .transform((value) => (value ? value.toLowerCase() : value))
            .matches(
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                'Invalid email format'
            ),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setError
    } = useForm({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true);

            const response = await axios.post(`${api_link}/auth/forgetpassword`, {
                email_or_mobile: data.userId?.trim()
            })
            console.log(response.data.message);
            navigation.navigate('OtpVerify', { fromForgotPassword: true, userId: data.userId });

        } catch (error: any) {
            if ('User not found!' === error.response.data.message) {
                setError('userId', {
                    type: 'manual',
                    message: error.response.data.message
                });
            }
            else {
                console.log("Forgot Password : ", error.response.data.error);
            }
        } finally {
            setLoading(false);
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <View className='w-full h-full space-y-6 px-primary'>

                <View className='flex-row items-center justify-between w-full mt-5'>

                    <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()} className='h-8 w-8 items-center justify-center rounded-full border-[1px] border-secondary dark:border-white'>
                        <Ionicons name="arrow-back" size={22} color={colorScheme === 'dark' ? '#fff' : '#000'} />
                    </TouchableOpacity>

                    <Animated.Text entering={FadeInUp.delay(200).duration(800).springify()} className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_bold}>Forgot Password</Animated.Text>

                    <View className='w-8 h-8'>
                        <Ionicons name="arrow-back-circle-outline" size={34} color={colorScheme === 'dark' ? '#fff' : '#000'} style={{ display: 'none' }} />
                    </View>

                </View>

                <Animated.View entering={FadeInUp.delay(400).duration(800).springify()} className='space-y-2'>
                    <Text className='tracking-wider text-text16 text-secondary dark:text-white' style={Global.text_medium}>Enter your email</Text>
                    <View>
                        <Controller
                            control={control}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    contentStyle={{
                                        fontFamily: 'DMSans-Regular'
                                    }}
                                    className='w-full px-1 tracking-wide bg-transparent text-text17'
                                    placeholder='Enter your email'
                                    placeholderTextColor='#a8a8a8'
                                    onChangeText={item => onChange(item.toLowerCase())}
                                    inputMode='email'
                                    onBlur={onBlur}
                                    error={!!errors?.userId?.message}
                                    value={value}
                                    mode='outlined'
                                    autoCapitalize='none'
                                    activeOutlineColor='#a8a8a8'
                                    outlineStyle={{
                                        borderWidth: 1.2,
                                        borderRadius: 8
                                    }}
                                    outlineColor='#a8a8a8'
                                    textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                />
                            )}
                            name={'userId'}
                            defaultValue=""
                        />
                        {errors?.userId?.message && (
                            <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.userId?.message}</Text>
                        )}
                    </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(250).duration(800).springify()} className='flex-row items-center space-x-2'>
                    <Text className='text-text16 text-secondary dark:text-white' style={Global.text_medium}>Don't have an account ?</Text>
                    <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.navigate('SignUp')}>
                        <Text className='tracking-wide text-text16 text-secondary dark:text-white' style={Global.text_bold}>Sign Up</Text>
                    </TouchableOpacity>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(200).duration(800).springify()} className='items-center mt-5'>
                    <TouchableOpacity
                        onPress={handleSubmit(onSubmit)}
                        activeOpacity={0.8}
                        className='bg-primary h-[50px] rounded-full items-center justify-center px-5'
                    >
                        <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Verify</Text>
                    </TouchableOpacity>
                </Animated.View>

            </View>

        </View>
    )
}

export default ForgotPassword;