import { View, Text, TouchableOpacity, ScrollView, Pressable, Keyboard, KeyboardAvoidingView } from 'react-native'
import React, { useContext, useState } from 'react'
import { Image } from 'expo-image';
import Global from '../../globalStyle'

import { Controller, useForm } from 'react-hook-form'
import { TextInput } from 'react-native-paper'

import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import Ionicons from '@expo/vector-icons/Ionicons'
import { ClientContext } from '../context/ClientContext'
import CustomStatusBar from '../components/CustomStatusBar'
import { GlobalContext } from '../context/GlobalProvider'
import axios from 'axios'
import { AntDesign } from '@expo/vector-icons'
import { Toast } from '../components/Toast'

const ResetPassword = ({ route }: any) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { navigation, colorScheme, isAndroid } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const userId = route.params?.userId || '';

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [apiResError, setApiResError] = useState('');

    const schema = yup.object().shape({
        newpassword: yup
            .string()
            .required('Password is required')
            // .test('api-error', apiResError ? apiResError : '', () => !apiResError)
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_\-\.#])[A-Za-z\d@$!%*?&_\-\.#]{8,}$/,
                'Password must contain at least 8 characters, including uppercase, lowercase, number, and special character'
            )
            .min(8, 'Password must be at least 8 characters'),
        confirmpassword: yup
            .string()
            .oneOf([yup.ref('newpassword')], 'Passwords must match')
            .required('Confirm password is required')
            // .test('api-error', apiResError ? apiResError : '', () => !apiResError)
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
    } = useForm({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: any) => {
        try {
            Keyboard.dismiss();
            setLoading(true);
            // console.log(userId)
            const response = await axios.post(`${api_link}/auth/resetpassword`, {
                "email_or_mobile": userId?.trim(),
                "new_password": data.newpassword?.trim(),
                "confirm_new_password": data.confirmpassword?.trim()
            })

            Toast.show({
                type: 'success',
                message: 'Password updated',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

            if (response.data.success) navigation.navigate('SignIn');

        } catch (error: any) {
            // console.log("Reset Password Error : ", error.response.data);
            setApiResError(error.response.data.message);
        } finally {
            setLoading(false);
        }
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <KeyboardAvoidingView
                behavior={!isAndroid ? 'height' : 'padding'}
                style={{ flex: 1 }}
            >

                <View className='h-[70] w-full bg-secondary items-center justify-center'>
                    <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
                </View>

                <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" className='flex-1'>

                    <View className='w-full h-full space-y-6 px-primary'>

                        <View className='flex-row items-center justify-between mt-5'>

                            <Pressable onPress={() => navigation.goBack()} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-secondary dark:border-white'>
                                <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                            </Pressable>

                            <Text style={Global.text_medium} className='text-xl text-secondary dark:text-white'>Forgot Password</Text>

                            <View className='h-[30px] w-[30px]'>
                                <AntDesign name='arrowleft' size={22} color={colorScheme === 'dark' ? 'white' : 'black'} style={{ display: 'none' }} />
                            </View>

                        </View>

                        <View className='space-y-2'>
                            <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>New Password</Text>
                            <View>
                                <View className='flex-row items-center justify-between'>
                                    <Controller
                                        control={control}
                                        render={({ field: { onChange, onBlur, value } }) => (
                                            <TextInput
                                                className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                                placeholder='Password'
                                                placeholderTextColor='#a8a8a8'
                                                secureTextEntry={!showPassword}
                                                onChangeText={(value)=>{
                                                    onChange(value);
                                                    setApiResError('');
                                                }}
                                                onBlur={onBlur}
                                                error={!!errors.newpassword?.message}
                                                value={value}
                                                mode='outlined'
                                                activeOutlineColor='#a8a8a8'
                                                outlineStyle={{
                                                    borderWidth: 1.2,
                                                    borderRadius: 8,
                                                }}
                                                outlineColor='#a8a8a8'
                                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                            />
                                        )}
                                        name="newpassword"
                                        defaultValue=""
                                    />
                                    <TouchableOpacity activeOpacity={0.9} onPress={() => setShowPassword(!showPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                        <Ionicons name={showPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                    </TouchableOpacity>
                                </View>
                                {errors?.newpassword?.message && (
                                    <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.newpassword?.message }</Text>
                                )}
                            </View>
                        </View>

                        <View className='space-y-2'>
                            <Text className='text-lg tracking-wider text-secondary dark:text-white' style={Global.text_medium}>Confirm New Password</Text>
                            <View>
                                <View className='flex-row items-center justify-between'>
                                    <Controller
                                        control={control}
                                        render={({ field: { onChange, onBlur, value } }) => (
                                            <TextInput
                                                className='w-full pl-1 pr-10 tracking-wide bg-transparent text-text17'
                                                placeholder='Password'
                                                placeholderTextColor='#a8a8a8'
                                                secureTextEntry={!showConfirmPassword}
                                                onChangeText={(value)=>{
                                                    onChange(value);
                                                    setApiResError('');
                                                }}
                                                onBlur={onBlur}
                                                error={!!errors.confirmpassword?.message}
                                                value={value}
                                                mode='outlined'
                                                activeOutlineColor='#a8a8a8'
                                                outlineStyle={{
                                                    borderWidth: 1.2,
                                                    borderRadius: 8,
                                                }}
                                                outlineColor='#a8a8a8'
                                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                                            />
                                        )}
                                        name="confirmpassword"
                                        defaultValue=""
                                    />
                                    <TouchableOpacity activeOpacity={0.9} onPress={() => setShowConfirmPassword(!showConfirmPassword)} className='absolute right-0 z-10 items-center justify-center h-full px-3'>
                                        <Ionicons name={showConfirmPassword ? 'eye-outline' : 'eye-off-outline'} size={22} color={colorScheme === 'dark' ? 'white' : 'black'} />
                                    </TouchableOpacity>
                                </View>
                                {(errors?.confirmpassword?.message || apiResError) && (
                                    <Text className='text-red-500 text-sm' style={Global.text_medium}>* {errors?.confirmpassword?.message || apiResError}</Text>
                                )}
                            </View>
                        </View>

                        <View className='items-center mt-5'>
                            <TouchableOpacity
                                onPress={handleSubmit(onSubmit)}
                                activeOpacity={0.9}
                                className='bg-primary h-[50px] rounded-full items-center justify-center px-5 mb-5'
                            >
                                <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Submit</Text>
                            </TouchableOpacity>
                        </View>

                    </View>

                </ScrollView>

            </KeyboardAvoidingView>

        </View>
    )
}

export default ResetPassword