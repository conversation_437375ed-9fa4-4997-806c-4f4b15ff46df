import { View, Text, StatusBar, TouchableOpacity, useColorScheme } from 'react-native'
import React from 'react'
import Global from '../../globalStyle';
import Animated, { FadeInUp } from 'react-native-reanimated';
import Onbording from '../assets/images/onbording/onBording';
import { Image } from 'expo-image';

interface Props {
    onFinish: () => void;
    setInitialScreen: (initialScreen: string) => void;
}

const Onboarding: React.FC<Props> = ({ onFinish, setInitialScreen }) => {

    const colorScheme = useColorScheme();

    const url = colorScheme === 'dark' ?
        require('../assets/icons/logo/logo-dark-small.png') :
        require('../assets/icons/logo/logo-light-small.png');

    const onboardingVector = colorScheme === 'dark' ?
        require('../assets/images/onboarding-blob-dark.png') :
        require('../assets/images/onboarding-blob-light.png');

    const handleSignIn = () => {
        setInitialScreen('SignIn');
        onFinish();
    }

    const handleSignUp = () => {
        setInitialScreen('SignUp');
        onFinish();
    }

    return (
        <View className='justify-center flex-1 bg-white dark:bg-dark px-primary'>

            <StatusBar backgroundColor={colorScheme === 'dark' ? '#111' : '#fff'} barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} />

            <View className='h-[85%] w-full flex-col justify-between'>

                <Animated.View entering={FadeInUp.delay(400).duration(500).springify()} className='items-center w-full'>
                    <Image source={url} style={{ height: 100, width: 200 }} contentFit='contain' />
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(600).duration(500).springify()} className='items-center w-full'>
                    <Onbording />
                    <Image source={onboardingVector} style={{ height: 300, width: 300, position: 'absolute', zIndex: -1 }} contentFit='contain' />
                </Animated.View>

                <View className='space-y-3'>

                    <Animated.View entering={FadeInUp.delay(800).duration(500)}>
                        <TouchableOpacity
                            activeOpacity={0.8}
                            className='items-center justify-center w-full rounded-full bg-primary h-14'
                            onPress={handleSignIn}
                        >
                            <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Login to your account</Text>
                        </TouchableOpacity>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.delay(1000).duration(500)}>
                        <TouchableOpacity
                            activeOpacity={0.8}
                            className='border-[1px] border-primary h-14 w-full rounded-full items-center justify-center'
                            onPress={handleSignUp}
                        >
                            <Text className='tracking-wide text-text16 text-secondary dark:text-white' style={Global.text_medium}>Create new account</Text>
                        </TouchableOpacity>
                    </Animated.View>

                </View>

            </View>

        </View>
    )
}

export default Onboarding


