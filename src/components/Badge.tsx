import { View, Text } from 'react-native'
import React from 'react'
import Global from '../../globalStyle';

interface BadgeProps {
    name: string;
    backgroundColor?: string;
    color?: string;
    height?: number;
    paddingX?: number;
    borderColor?: string;
}

const Badge: React.FC<BadgeProps> = ({ name, backgroundColor, color, height, paddingX, borderColor }) => {
    return (
        <View className='items-start'>
            <View
                className='rounded-full flex-row items-center justify-center'
                style={{
                    backgroundColor: backgroundColor || '#ececec',
                    height: height || 33,
                    paddingHorizontal: paddingX || 12,
                    borderWidth: borderColor ? 1 : 0,
                    borderColor: borderColor || ''
                }}
            >
                <Text style={[Global.text_medium, { color: color || '#2d2828' }]}>{name}</Text>
            </View>
        </View>
    )
}

export default Badge