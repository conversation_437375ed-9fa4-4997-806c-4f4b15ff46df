import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import Global from '../../globalStyle'

interface ButtonProps {
    onPress?: () => void;
    title: string;
    height?: number;
    color?: string;
    bgColor?: string;
    borderColor?: string;
    borderWidth?: number;
    paddingX?: number;
    icon?: boolean;
    buttonIcon?: any;
    iconPosition?: 'left' | 'right';
    disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({ onPress, icon, iconPosition = 'left', disabled = false, buttonIcon, title, height, color, bgColor, borderColor, paddingX, borderWidth }) => {
    return (
        <TouchableOpacity
            style={{
                height: height || 57,
                backgroundColor: bgColor || '#fdd066',
                borderColor: borderColor || '#fdd066',
                borderWidth: borderWidth || 0.7,
                paddingHorizontal: paddingX || 20
            }}
            onPress={onPress}
            activeOpacity={0.8}
            className='flex-row items-center justify-center space-x-2 rounded-full'
            disabled={disabled}
        >
            {icon && iconPosition === 'left' && <View>
                {buttonIcon}
            </View>}

            <Text
                className='tracking-wide text-center text-text15'
                style={[Global.text_bold, {
                    color: color || '#2d2828'
                }]}
            >
                {title}
            </Text>

            {icon && iconPosition === 'right' && <View>
                {buttonIcon}
            </View>}

        </TouchableOpacity>
    )
}

export default Button