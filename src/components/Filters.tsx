import { View, Text, TouchableOpacity, LayoutAnimation } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import Global from '../../globalStyle'

import Octicons from '@expo/vector-icons/Octicons'
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons'
import { ClientContext } from '../context/ClientContext'
import Location from './filters/Location'
import DateFilter from './filters/DateFilter'
import Price from './filters/Price'
import { useForm } from 'react-hook-form'
import { FilterContext } from '../context/FilterContext'
import Mode from './filters/Mode'
import PostedBy from './filters/PostedBy'
import SortBy from './sortby/SortBy'
import JobType from './filters/JobType'
import Salary from './filters/Salary'
import AreaOfPractice from './filters/AreaOfPractice'
import Experience from './filters/Experience'
import PublishedBy from './filters/PublishedBy'
import Accordion from 'react-native-accordion-wrapper'
import Category from './filters/Category'

interface FilterProps {
    locationFilter?: boolean;
    categoryFilter?: boolean;
    dateFilter?: boolean;
    priceFilter?: boolean;
    modeFilter?: boolean;
    postedByFilter?: boolean;
    jobTypeFilter?: boolean;
    salaryFilter?: boolean;
    ratingFilter?: boolean;
    practiceAreaFilter?: boolean;
    experienceFilter?: boolean;
    hireLawyerFilter?: boolean;
    courseSort?: boolean;
    publishedByFilter?: boolean;
    readingSort?: boolean;
    chipFrom: string;
    competitionSort?: boolean;
    eventSort?: boolean;
}

const Filters: React.FC<FilterProps> = (props) => {

    const { colorScheme, isAndroid, currentCompChip, currentEventChip, currentCourseChip, currentCareerChip, currentReadingsChip } = useContext(ClientContext);
    const { setCompPrice, setSalary, setSelectedCategory } = useContext(FilterContext);

    const [filtersVisible, setFiltersVisible] = useState(false);

    // if (isAndroid && UIManager.setLayoutAnimationEnabledExperimental) {
    //     UIManager.setLayoutAnimationEnabledExperimental(true);
    // }

    const toggleFilters = () => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setFiltersVisible(prev => !prev);
    }

    useEffect(() => {
        resetField('location');
        resetField('from');
        resetField('to');
        resetField('date');
        resetField('mode');
        resetField('posted_by');
        resetField('jobType');
        resetField('salaryFrom');
        resetField('salaryTo');
        resetField('sortBy');
        resetField('lawyerSort');
        resetField('areaPractice');
        resetField('experience');
        resetField('published_by');
        resetField('category');
    }, [currentCompChip, currentEventChip, currentCourseChip, currentCareerChip, currentReadingsChip]);

    const { control, handleSubmit, setValue, watch, resetField } = useForm({
        defaultValues: {
            location: '',
            from: '',
            to: '',
            date: '',
            mode: '',
            posted_by: '',
            jobType: '',
            salaryFrom: '',
            salaryTo: '',
            sortBy: (props.competitionSort || props.eventSort) ? 'latestdatetime' : 'latest',
            lawyerSort: 'all',
            areaPractice: '',
            experience: '',
            published_by: '',
            category: []
        }
    });

    const onSubmit = (data: any) => {
        setFiltersVisible(false);
        setCompPrice({ from: parseInt(data.from), to: parseInt(data.to) });
        setSalary({ from: parseInt(data.salaryFrom), to: parseInt(data.salaryTo) });
        setSelectedCategory(data.category);
    };

    const AccordionSection = ({ title, children, show }: { title: string, children: React.ReactNode, show: boolean | undefined }) => (
        show ? (
            <Accordion
                dataSource={[{ title, child: <View className='-bottom-1'>{children}</View> }]}
                rightChevronIcon={<SimpleLineIcons name='arrow-up' size={16} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
                headerItemsStyle={{
                    backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : '#F8F6F3',
                    height: 55,
                    borderBottomWidth: 0,
                }}
                headerTitleLabelStyle={{
                    color: colorScheme === 'dark' ? "#fff" : '#111',
                    fontSize: 15,
                    fontFamily: 'DMSans-Bold',
                    letterSpacing: 0.5
                }}
            />
        ) : null
    );

    return (
        <View className='w-full'>

            <TouchableOpacity
                activeOpacity={0.8}
                className='px-primary py-3 flex-row bg-[#ececec] items-center space-x-2'
                onPress={toggleFilters}
            >
                <Text className='text-text15 text-secondary' style={Global.text_bold}>Filters / Sort by</Text>
                <Octicons name={filtersVisible ? 'triangle-up' : 'triangle-down'} size={30} color='#000' />
            </TouchableOpacity>

            <View>
                {filtersVisible && (
                    <View
                        className='px-primary bg-cardLight dark:bg-[#2F2F2F] overflow-hidden'
                    >
                        <AccordionSection title="Posted By" show={props.postedByFilter}>
                            <PostedBy control={control} resetField={resetField} chipFrom={props.chipFrom} />
                        </AccordionSection>

                        <AccordionSection title="Location" show={props.locationFilter}>
                            <Location control={control} resetField={resetField} chipFrom={props.chipFrom} />
                        </AccordionSection>

                        <AccordionSection title="Published By" show={props.publishedByFilter}>
                            <PublishedBy control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Date" show={props.dateFilter}>
                            <DateFilter control={control} resetField={resetField} setValue={setValue} watch={watch} />
                        </AccordionSection>

                        <AccordionSection title="Mode" show={props.modeFilter}>
                            <Mode control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Price" show={props.priceFilter}>
                            <Price control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Job Type" show={props.jobTypeFilter}>
                            <JobType control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Salary" show={props.salaryFilter}>
                            <Salary control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Area of Practice" show={props.practiceAreaFilter}>
                            <AreaOfPractice control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Experience" show={props.experienceFilter}>
                            <Experience control={control} resetField={resetField} />
                        </AccordionSection>

                        <AccordionSection title="Category" show={props.categoryFilter}>
                            <Category control={control} resetField={resetField} />
                        </AccordionSection>

                        <View className='items-center my-3'>
                            <TouchableOpacity
                                onPress={handleSubmit(onSubmit)}
                                activeOpacity={0.8}
                                className='bg-primary h-[50px] rounded-full items-center justify-center px-5'
                            >
                                <Text className='tracking-wide text-text16 text-secondary' style={Global.text_bold}>Search</Text>
                            </TouchableOpacity>
                        </View>

                        <AccordionSection title="Sort By" show={props.courseSort || props.readingSort || props.hireLawyerFilter || props.competitionSort || props.eventSort}>
                            <View className='w-full pb-4 bg-cardLight dark:bg-[#2F2F2F]'>
                                <SortBy
                                    hireLawyerFilter={props.hireLawyerFilter}
                                    readingSort={props.readingSort}
                                    courseSort={props.courseSort}
                                    competitionSort={props.competitionSort}
                                    eventSort={props.eventSort}
                                    control={control}
                                    resetField={resetField}
                                />
                            </View>
                        </AccordionSection>

                    </View>
                )}
            </View>

        </View>
    )
}

export default Filters;
