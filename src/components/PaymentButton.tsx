import React, { useContext, useState } from 'react';
import { Text, TouchableOpacity } from 'react-native';
import { Toast } from './Toast';
import { Image } from 'expo-image';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import Global from '../../globalStyle';
import { LikeSaveContext } from '../context/LikeSaveContext';
import PhonePePaymentSDK from "react-native-phonepe-pg";
import { ClientContext } from '../context/ClientContext';
import { GlobalContext } from '../context/GlobalProvider';
import Base64 from 'react-native-base64'
import sha256 from 'sha256';
import axios from 'axios';
import { useFocusEffect } from '@react-navigation/native';

interface PaymentButtonProps {
    loading: boolean;
    total: number;
    category: string;
    type: string;
    data: any;
    navigateToConfirm: string;
    navigateToCancel: string;
}

type User = {
    name: string;
    clientId: string;
    mobile: string
}

type CreatePaymentProps = {
    discountPrice: number;
    gst: number;
    itemId: string;
    modelType: string;
    user: User;
}

const PaymentButton: React.FC<PaymentButtonProps> = ({ loading, total, category, data, navigateToConfirm, navigateToCancel, type }) => {

    //@ts-ignore
    const api_link = process.env.EXPO_PUBLIC_API_URL;

    const { setRegisteredItems } = useContext(LikeSaveContext);
    const { navigation, userData, clientId, token } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const createPaymentDetails = {
        discountPrice: data?.discount_price,
        gst: data?.gst,
        itemId: data?._id,
        modelType: category,
        user: {
            name: `${userData.first_name} ${userData.last_name}`,
            clientId: clientId || '',
            mobile: userData.mobile
        },
    }

    const handlePaymentInitialization = async () => {
        try {
            setLoading(true);
            const isInitialized = await PhonePePaymentSDK.init(
                //@ts-ignore
                process.env.EXPO_PUBLIC_PAYMENT_ENV,
                //@ts-ignore
                process.env.EXPO_PUBLIC_PHONEPE_MERCHANT_ID,
                '',
                false
            );

            if (isInitialized) {
                const { payloadMain, checksum, merchantTransactionId } = await createPayment(createPaymentDetails);
                const isCreatePaymentSuccess = await handleCreatePayment(merchantTransactionId);

                if (isCreatePaymentSuccess) {
                    handleStartTransaction(payloadMain, checksum, merchantTransactionId)
                }
            }

        } catch (error: any) {
            console.log("Payment initialization error : ", error);
        }
    };

    const createPayment = async (props: CreatePaymentProps) => {

        const { discountPrice, gst, user } = props;

        const amount = discountPrice + (discountPrice * (gst / 100));
        const merchantTransactionId = `TXN_${Date.now()}`;

        const data = {
            //@ts-ignore
            merchantId: process.env.EXPO_PUBLIC_PHONEPE_MERCHANT_ID,
            merchantTransactionId,
            merchantUserId: user.clientId,
            name: user.name,
            amount: amount * 100,
            callbackUrl: '',
            mobileNumber: user.mobile,
            paymentInstrument: {
                type: "PAY_PAGE",
            },
        };

        const payload = JSON.stringify(data);
        const payloadMain = Base64.encode(payload);

        //@ts-ignore
        const stringToSign = payloadMain + "/pg/v1/pay" + process.env.EXPO_PUBLIC_PHONEPE_SALT_KEY;

        //@ts-ignore
        const checksum = sha256(stringToSign) + "###" + process.env.EXPO_PUBLIC_PHONEPE_SALT_INDEX;

        return { payloadMain, checksum, merchantTransactionId }
    }

    const handleCreatePayment = async (merchantTransactionId: string) => {
        try {
            const response = await ClientAxiosInstance.post(`/payment/create-payment/mobile`, { itemId: data?._id, modelType: category, merchantTransactionId });
            if (response.data.message === "Seats are not available for this event.") {
                Toast.show({
                    type: 'error',
                    message: response.data.message,
                    duration: 3000,
                    position: 'bottom',
                    animation: 'slide',
                    icon: (
                        <Image
                            source={require('../assets/icons/logo/logo-light-big.png')}
                            style={{ width: 24, height: 24 }}
                            contentFit='contain'
                        />
                    ),
                });
                setLoading(false);
                return false;
            }
            return true;

        } catch (error: any) {
            console.log("Create payment error : ", error.response);
            setLoading(false);
            return false;
        }
    };

    const handleStartTransaction = async (payloadMain: string, checkSum: string, merchantTransactionId: string) => {

        setLoading(true);
        const response = await PhonePePaymentSDK.startTransaction(
            payloadMain, checkSum, 'com.lawcube.in', 'lawcube'
        );

        if (response?.status?.toLowerCase().trim() === 'success' && merchantTransactionId) {
            await handleEndTransaction(merchantTransactionId);
        } else {
            console.log("Start transaction error : ", response);
            setLoading(false);
        }
    }

    const handleEndTransaction = async (merchantTransactionId: string) => {
        try {
            const response = await axios.post(`${api_link}/payment/check-status/${merchantTransactionId}/mobile`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            if (response.data.success) {
                navigation.navigate(navigateToConfirm, { [type]: data });
                setRegisteredItems(prevItems => {
                    const updatedSet = new Set(prevItems);
                    updatedSet.add(data?._id);
                    return updatedSet;
                });
            }
            else {
                navigation.navigate(navigateToCancel, { [type]: data });
            }

        } catch (error: any) {
            console.log("End transaction error : ", JSON.stringify(error));
            if (error.response.data.error === 'Invalid or non-pending payment') {
                navigation.navigate(navigateToConfirm, { [type]: data });
                setRegisteredItems(prevItems => {
                    const updatedSet = new Set(prevItems);
                    updatedSet.add(data?._id);
                    return updatedSet;
                });
            }
        } finally {
            setLoading(false);
        }
    }

    return (
        <TouchableOpacity
            activeOpacity={0.8}
            onPress={handlePaymentInitialization}
            className='w-full h-[57] flex-row items-center justify-center bg-primary rounded-full'
        >
            <Text style={Global.text_bold} className='text-secondary text-text16'>
                Pay &#8377;{loading ? "0.0" : total?.toFixed(2)} for all items
            </Text>

        </TouchableOpacity>
    );
};

export default PaymentButton;