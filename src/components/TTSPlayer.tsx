import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import Slider from '@react-native-community/slider';
import * as Speech from 'expo-speech';
import Icon from '@expo/vector-icons/FontAwesome5';
import Global from '../../globalStyle';
import { htmlToText } from 'html-to-text';

const { width: screenWidth } = Dimensions.get('window');

const TTSPlayer: React.FC<{ text: string }> = ({ text }) => {
    const [paused, setPaused] = useState<boolean>(true);
    const [currentTime, setCurrentTime] = useState<number>(0);
    const [duration, setDuration] = useState<number>(0);
    const [isSeeking, setIsSeeking] = useState<boolean>(false);
    const plainText = useRef<string>('');
    const lastPosition = useRef<number>(0);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        plainText.current = htmlToText(text, {
            selectors: [
                { selector: 'img', format: 'skip' },
                { selector: 'figure', format: 'skip' },
                { selector: 'a', format: 'skip' },
                { selector: 'table', format: 'skip' },
                { selector: 'pre', format: 'skip' },
            ],
        }).replace(/\s+/g, ' ').trim();

        // Estimate duration based on words count
        const words = plainText.current.split(' ').length;
        const averageWPM = 130;
        const estimatedDuration = (words / averageWPM) * 60;
        setDuration(Math.ceil(estimatedDuration));

        setCurrentTime(0);
        lastPosition.current = 0;

        return () => stopSpeech(); // Cleanup on unmount
    }, [text]);

    const startTimer = () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
        intervalRef.current = setInterval(() => {
            setCurrentTime((prev) => {
                if (prev < duration) {
                    lastPosition.current = prev + 1;
                    return prev + 1;
                } else {
                    stopSpeech();
                    return duration;
                }
            });
        }, 1000);
    };

    const stopSpeech = () => {
        Speech.stop();
        setPaused(true);
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };

    const togglePlayPause = () => {
        if (paused) {
            let wordsToSkip = Math.floor(lastPosition.current * (130 / 60)); // Calculate resume point
            const textArray = plainText.current.split(' ');
            let remainingText = textArray.slice(wordsToSkip).join(' ');

            Speech.speak(remainingText, {
                onStart: startTimer,
                onDone: stopSpeech,
                onStopped: stopSpeech,
                onError: (error) => {
                    console.log('Speech error:', error);
                    stopSpeech();
                },
                language: 'en-US',
                pitch: 1,
                rate: 0.8,
            });
        } else {
            stopSpeech();
        }
        setPaused(!paused);
    };

    const onSlideStart = () => {
        stopSpeech();
        setIsSeeking(true);
    };

    const onSlideComplete = (value: number) => {
        setIsSeeking(false);
        setCurrentTime(value);
        lastPosition.current = value;
        if (!paused) {
            togglePlayPause();
        }
    };

    const getFormattedTime = (time: number): string => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    return (
        <View className='flex-row items-center justify-between'>

            <TouchableOpacity activeOpacity={0.8} onPress={togglePlayPause} className="px-3 py-2 bg-[#F3F4F6] rounded-full space-x-2 flex-row items-center">
                <Text style={Global.text_medium}>{paused ? 'Play' : 'Pause'}</Text>
                <Icon name={paused ? 'play' : 'pause'} color="#FFD166" size={15} />
            </TouchableOpacity>

            <Text style={Global.text_medium} className='text-secondary dark:text-white'>{getFormattedTime(currentTime)}</Text>

            <Slider
                style={{ width: screenWidth * 0.4 }}
                value={currentTime}
                minimumValue={0}
                maximumValue={duration}
                onValueChange={setCurrentTime}
                onSlidingStart={onSlideStart}
                onSlidingComplete={onSlideComplete}
                minimumTrackTintColor="#fdd066"
                maximumTrackTintColor="#a8a8a8"
                thumbTintColor="#fdd066"
                disabled
            />

            <Text style={Global.text_medium} className='text-secondary dark:text-white'>{getFormattedTime(duration)}</Text>

        </View>
    );
};

export default TTSPlayer;