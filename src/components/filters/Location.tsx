import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../../globalStyle';
import { Dropdown } from 'react-native-element-dropdown';
import { ClientContext } from '../../context/ClientContext';

import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

import { Controller } from 'react-hook-form';
import { FilterContext } from '../../context/FilterContext';

interface LocationProps {
    control: any;
    resetField: any;
    chipFrom: string;
}

const Location: React.FC<LocationProps> = ({ control, resetField, chipFrom }) => {

    const { colorScheme } = useContext(ClientContext);
    const { selectedLocation, setSelectedLocation, compLocation, eventLocation, careerLocation, lawyerLocation } = useContext(FilterContext);

    const locationData = ['Quiz', 'Moot Court', 'Essay Writing', 'Article Writing', 'Debate'].includes(chipFrom) ? compLocation :
        ['Job Listing', 'Internship'].includes(chipFrom) ? careerLocation :
            ['College Events', 'Seminars', 'Workshop'].includes(chipFrom) ? eventLocation :
                chipFrom === 'HireLawyer' ? lawyerLocation : [];

    const data = Array.isArray(locationData) ? locationData.map((item: any) => (item.length > 0 && {
        label: item,
        value: item,
    })) : [];

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label}
                </Text>
            </View>
        );
    };

    const handleClearFilters = () => {
        setSelectedLocation('');
        resetField('location');
    }


    return (
        <View className='mt-2 space-y-2'>

            <View className='space-y-4'>

                <View className='h-[55px]'>
                    <Controller
                        control={control}
                        name="location"
                        defaultValue={selectedLocation}
                        render={({ field: { onChange, value } }) => (
                            <Dropdown
                                style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                                selectedTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                }}
                                data={data}
                                search={false}
                                labelField="label"
                                valueField="value"
                                placeholder="Location"
                                renderItem={renderItem}
                                onChange={item => {
                                    onChange(item.value);
                                    setSelectedLocation(item.value);
                                }}
                                itemContainerStyle={{
                                    borderBottomWidth: 0.5,
                                    borderBottomColor: '#a8a8a8',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                itemTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                    fontSize: 17
                                }}
                                containerStyle={{
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                showsVerticalScrollIndicator={false}
                                placeholderStyle={{
                                    color: '#a8a8a8',
                                }}
                                keyboardAvoiding
                                dropdownPosition='auto'
                                fontFamily='DMSans-Regular'
                                value={value}
                            />
                        )}
                    />
                </View>

            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: '100%',
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default Location;