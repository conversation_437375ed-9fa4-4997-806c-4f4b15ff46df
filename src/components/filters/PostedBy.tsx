import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { Controller } from 'react-hook-form';
import { Dropdown } from 'react-native-element-dropdown';
import { ClientContext } from '../../context/ClientContext';
import { FilterContext } from '../../context/FilterContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import Global from '../../../globalStyle';
import { CompetitionContext } from '../../context/CompetitionContext';
import { EventContext } from '../../context/EventContext';
import { CourseContext } from '../../context/CourseContext';

interface PostedByProps {
    control: any;
    resetField: any;
    chipFrom: string;
}

const PostedBy: React.FC<PostedByProps> = ({ control, resetField, chipFrom }) => {

    const { colorScheme } = useContext(ClientContext);
    const { setSelectedPostedBy, selectedPostedBy } = useContext(FilterContext);

    const { postedByList: competitionPosted, selectedChip: competitionChip } = useContext(CompetitionContext);
    const { postedByList: eventPosted, selectedChip: eventChip } = useContext(EventContext);

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className={`tracking-wide text-secondary dark:text-white`} style={Global.text_medium}>
                    {item.label}
                </Text>
            </View>
        );
    };

    const datas = [
        ['College Events', 'Seminars', 'Workshop'],
        ['Quiz', 'Moot Court', 'Essay Writing', 'Article Writing', 'Debate'],
        ['Beginner', 'Intermediate', 'Advanced']
    ]

    const handleSelectedMenu = () => {
        if (datas[0].includes(chipFrom)) {
            return eventPosted;
        }
        else if (datas[1].includes(chipFrom)) {
            return competitionPosted;
        }
        return [];
    };

    const [data, setData] = useState<any[]>([]);

    const handleClearFilters = () => {
        setSelectedPostedBy('');
        resetField('posted_by');
    }

    useEffect(() => {
        const data = handleSelectedMenu()?.map((item: any) => ({
            label: item,
            value: item,
        }))
        setData(data);
    }, [competitionChip, eventChip])

    return (
        <View className='space-y-2'>

            <View className='h-[55px]'>
                <Controller
                    control={control}
                    name="posted_by"
                    defaultValue={selectedPostedBy}
                    render={({ field: { onChange, value } }) => (
                        <Dropdown
                            style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                            selectedTextStyle={{
                                color: colorScheme === 'light' ? '#000' : '#fff',
                            }}
                            data={data}
                            search={false}
                            labelField="label"
                            valueField="value"
                            placeholder="Posted By"
                            renderItem={renderItem}
                            onChange={item => {
                                onChange(item.value);
                                setSelectedPostedBy(item.value);
                            }}
                            itemContainerStyle={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: '#a8a8a8',
                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                            }}
                            itemTextStyle={{
                                color: colorScheme === 'light' ? '#000' : '#fff',
                                fontSize: 17
                            }}
                            containerStyle={{
                                borderRadius: 8,
                                overflow: 'hidden',
                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                            }}
                            showsVerticalScrollIndicator={false}
                            placeholderStyle={{
                                color: '#a8a8a8',
                            }}
                            keyboardAvoiding
                            dropdownPosition='auto'
                            fontFamily='DMSans-Regular'
                            value={value}
                        />
                    )}
                />
            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: '100%',
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default PostedBy