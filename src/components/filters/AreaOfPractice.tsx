import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import React, { useContext } from 'react'
import { ClientContext } from '../../context/ClientContext';
import { FontAwesome6 } from '@expo/vector-icons';
import Global from '../../../globalStyle';
import { Controller } from 'react-hook-form';
import { Dropdown } from 'react-native-element-dropdown';
import { FilterContext } from '../../context/FilterContext';

interface AreaOfPracticeProps {
    control: any;
    resetField: any;

}

const AreaOfPractice: React.FC<AreaOfPracticeProps> = ({ control, resetField }) => {

    const { colorScheme } = useContext(ClientContext);
    const { selectedAreaOfPractice, setSelectedAreaOfPractice, areaOfPractice } = useContext(FilterContext);

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' numberOfLines={1} ellipsizeMode='tail' style={Global.text_medium}>
                    {item.label}
                </Text>
            </View>
        );
    };

    const data = Array.isArray(areaOfPractice) ? areaOfPractice.map((item: any) => ({
        label: item,
        value: item,
    })) : [];

    const handleClearFilters = () => {
        setSelectedAreaOfPractice('');
        resetField('areaPractice');
    }

    return (
        <View className='space-y-3'>

            <View className='h-[55px]'>
                <Controller
                    control={control}
                    name="areaPractice"
                    defaultValue={selectedAreaOfPractice}
                    render={({ field: { onChange, value } }) => (
                        <Dropdown
                            style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                            selectedTextStyle={{
                                color: colorScheme === 'light' ? '#000' : '#fff',
                            }}
                            data={data}
                            search={false}
                            labelField="label"
                            valueField="value"
                            renderItem={renderItem}
                            onChange={item => {
                                onChange(item.value);
                                setSelectedAreaOfPractice(item.value);
                            }}
                            itemContainerStyle={{
                                borderBottomWidth: 0.5,
                                borderBottomColor: '#a8a8a8',
                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                            }}
                            itemTextStyle={{
                                color: colorScheme === 'light' ? '#000' : '#fff',
                                fontSize: 17
                            }}
                            containerStyle={{
                                borderRadius: 8,
                                overflow: 'hidden',
                                backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                            }}
                            showsVerticalScrollIndicator={false}
                            placeholderStyle={{
                                color: '#a8a8a8',
                            }}
                            keyboardAvoiding
                            dropdownPosition='auto'
                            fontFamily='DMSans-Regular'
                            value={value}
                        />
                    )}
                />
            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: '100%',
        width: '100%',
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default AreaOfPractice