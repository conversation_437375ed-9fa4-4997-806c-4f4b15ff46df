import { View, Text, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import { ClientContext } from '../../context/ClientContext';
import { Controller } from 'react-hook-form';
import { TextInput } from 'react-native-paper';

import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import Global from '../../../globalStyle';
import { FilterContext } from '../../context/FilterContext';

interface SalaryProps {
    control: any;
    resetField: any;
}

const Salary: React.FC<SalaryProps> = ({ control, resetField }) => {

    const { colorScheme } = useContext(ClientContext);
    const { setSalary } = useContext(FilterContext);

    const handleClearFilters = () => {
        setSalary({
            from: null, to: null
        });
        resetField('salaryFrom');
        resetField('salaryTo');
    }

    return (
        <View className='mb-4 space-y-2'>

            <View className='space-y-3'>

                <View>
                    <Controller
                        control={control}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                contentStyle={{
                                    fontFamily: 'DMSans-Regular'
                                }}
                                className='w-full px-1 tracking-wide text-text17'
                                style={{ backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }}
                                placeholder='From'
                                placeholderTextColor='#a8a8a8'
                                inputMode='numeric'
                                onChangeText={onChange}
                                onBlur={onBlur}
                                value={value}
                                mode='outlined'
                                activeOutlineColor='#a8a8a8'
                                outlineStyle={{
                                    borderWidth: 1.2,
                                    borderRadius: 8
                                }}
                                outlineColor='#a8a8a8'
                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                            />
                        )}
                        name={'salaryFrom'}
                        defaultValue=""
                    />
                </View>

                <View>
                    <Controller
                        control={control}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                contentStyle={{
                                    fontFamily: 'DMSans-Regular'
                                }}
                                className='w-full px-1 tracking-wide text-text17'
                                style={{ backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }}
                                placeholder='To'
                                placeholderTextColor='#a8a8a8'
                                inputMode='numeric'
                                onChangeText={onChange}
                                onBlur={onBlur}
                                value={value}
                                mode='outlined'
                                activeOutlineColor='#a8a8a8'
                                outlineStyle={{
                                    borderWidth: 1.2,
                                    borderRadius: 8
                                }}
                                outlineColor='#a8a8a8'
                                textColor={colorScheme === 'dark' ? '#fff' : 'black'}
                            />
                        )}
                        name={'salaryTo'}
                        defaultValue=""
                    />
                </View>

            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

export default Salary