import { View, Text, TouchableOpacity, Modal, Pressable } from 'react-native';
import React, { useContext, useState } from 'react';
import { Controller } from 'react-hook-form';
import Global from '../../../globalStyle';
import DateTimePicker from 'react-native-ui-datepicker';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { FilterContext } from '../../context/FilterContext';
import { ClientContext } from '../../context/ClientContext';
import { formatDateFilter } from '../../helpers/dateTimeFormat';

interface DateProps {
    control: any;
    watch: any;
    setValue: any;
    resetField: any;
}

const DateFilter: React.FC<DateProps> = ({ watch, control, setValue, resetField }) => {

    const { compDate, setCompDate } = useContext(FilterContext);
    const [show, setShow] = useState(false);
    const { colorScheme } = useContext(ClientContext);


    const watchedDate = watch("date", "");

    const handleClearFilters = () => {
        setCompDate(null);
        resetField('date');
    };

    const handleDateChange = (params: any) => {
        if (params?.date) {
            const selectedDate = new Date(params.date);
            selectedDate.setHours(12, 0, 0, 0);

            setCompDate(selectedDate);
            setValue("date", formatDateFilter(selectedDate));
            setShow(false);
        }
    };

    return (
        <View className='mt-2'>
            <>
                <Modal
                    visible={show}
                    transparent
                    statusBarTranslucent
                    animationType='fade'
                    onRequestClose={() => setShow(false)}
                >
                    <Pressable onPress={() => setShow(false)} className='items-center justify-center flex-1 bg-black/50'>
                        <View className={`rounded-lg p-2 max-w-md mx-10 ${colorScheme === 'dark' ? 'bg-[#4f4d48]' : 'bg-[#fffcf4]'} `}>
                            <DateTimePicker
                                mode="single"
                                date={compDate || new Date()}
                                onChange={handleDateChange}
                                headerButtonsPosition='right'
                                todayContainerStyle={{ borderRadius: 10, borderColor: '#f8dd9f', borderWidth: 1 }}
                                dayContainerStyle={{ borderRadius: 10, borderColor: '#f8dd9f' }}
                                calendarTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-Bold' }}
                                selectedTextStyle={{ color: '#2d2828', fontFamily: 'DMSans-ExtraBold', fontWeight: '700' }}
                                selectedItemColor="#f8dd9f"
                                headerTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-ExtraBold' }}
                                headerButtonColor={`${colorScheme === 'dark' ? '#f8dd9f' : "#4f4d48"}`}
                                headerButtonStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#f8dd9f'}`, borderRadius: 7, padding: 6, }}
                                monthContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#fffcf4'}` }}
                                yearContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#4f4d48' : '#fffcf4'}` }}
                                weekDaysContainerStyle={{ borderBottomColor: '#dedede' }}
                                weekDaysTextStyle={{ color: `${colorScheme === 'dark' ? '#e0e0e0' : '#4f4d48'}`, fontFamily: 'DMSans-Bold' }}
                                headerContainerStyle={{ backgroundColor: `${colorScheme === 'dark' ? '#615f5b' : '#faf4e3'}`, borderRadius: 10 }}
                            />
                        </View>
                    </Pressable>

                </Modal>
            </>

            <View className='mb-2'>
                <Controller
                    control={control}
                    name="date"
                    defaultValue={watchedDate}
                    render={({ field: { value } }) => (
                        <TouchableOpacity
                            activeOpacity={0.8}
                            className='h-[55px] w-full rounded-md bg-white dark:bg-dark items-start justify-center border border-[#a8a8a8] px-3'
                            onPress={() => setShow(true)}
                        >
                            {value ? (
                                <Text style={Global.text_medium}
                                    className='tracking-widest text-text16 text-secondary dark:text-white'
                                >
                                    {value}
                                </Text>
                            ) : (
                                <Text style={Global.text_medium}
                                    className='tracking-widest text-text16 text-greycolor'
                                >
                                    Date
                                </Text>
                            )}
                        </TouchableOpacity>
                    )}
                />
            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>
        </View>
    );
};

export default DateFilter;
