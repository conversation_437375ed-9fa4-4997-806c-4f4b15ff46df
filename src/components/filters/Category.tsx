import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import React, { useContext } from 'react'
import { ClientContext } from '../../context/ClientContext';
import { Feather, FontAwesome6 } from '@expo/vector-icons';
import Global from '../../../globalStyle';
import { Controller } from 'react-hook-form';
import { MultiSelect } from 'react-native-element-dropdown';
import { FilterContext } from '../../context/FilterContext';
import { capitalizeMode } from '../../helpers/getCapitalize';

interface CategoryProps {
    control: any;
    resetField: any;
}

const Category: React.FC<CategoryProps> = ({ control, resetField }) => {

    const { colorScheme } = useContext(ClientContext);
    const { selectedCategory, setSelectedCategory, topCategories } = useContext(FilterContext);

    const renderItem = (item: any, selected: boolean | undefined) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2f2f2f' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
                className='flex-row items-center justify-between'
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label}
                </Text>
                {selected && <Feather name="check" size={18} color={colorScheme === 'dark' ? '#fff' : '#000'} />}
            </View>
        );
    };

    const data = topCategories ? topCategories.map((item: any) => ({
        label: capitalizeMode(item),
        value: item,
    })) : [];

    const handleClearFilters = () => {
        setSelectedCategory([]);
        resetField('category');
    }

    return (
        <View className=''>

            <View className='h-[55px]'>
                <Controller
                    control={control}
                    name="category"
                    defaultValue={selectedCategory}
                    render={({ field: { onChange, value } }) => (
                        <MultiSelect
                            style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                            placeholder={`Category ${value.length > 0 ? `(${value.length})` : ''}`}
                            placeholderStyle={{
                                color: value.length > 0 ? colorScheme === 'dark' ? '#fff' : '#2d2828' : '#a8a8a8',
                            }}
                            containerStyle={{
                                borderRadius: 8,
                                overflow: 'hidden',
                                backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : 'white',
                            }}
                            renderItem={(item, selected) => renderItem(item, selected)}
                            showsVerticalScrollIndicator
                            visibleSelectedItem={false}
                            data={data}
                            labelField="label"
                            valueField="value"
                            value={value}
                            onChange={onChange}
                        />
                    )}
                />
            </View>

            <TouchableOpacity activeOpacity={0.8} onPress={handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: '100%',
        width: '100%',
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default Category;