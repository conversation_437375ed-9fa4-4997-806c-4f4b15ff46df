import { View, Modal } from 'react-native'
import React, { useContext } from 'react';
import { WebView } from 'react-native-webview';
import { ClientContext } from '../context/ClientContext';
import { GlobalContext } from '../context/GlobalProvider';
import { LikeSaveContext } from '../context/LikeSaveContext';
import { ClientAxiosInstance } from '../lib/axiosInstance';

type PaymentModalProps = {
    startPayment: boolean;
    setStartPayment: (val: boolean) => void;
    url: string;
    data: any;
    successScreen: string;
    failScreen: string;
}

const PaymentModal = ({ startPayment, url, setStartPayment, data, successScreen, failScreen }: PaymentModalProps) => {

    const { clientId, token, navigation } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);
    const { setRegisteredItems } = useContext(LikeSaveContext);

    const injectedJavaScript = `
        window.clientID = "${clientId}";
        window.userID = "${token}";
        true;
    `;

    const handlePaymentMethod = async (url: string) => {
        try {
            const segments = url.split('/');
            if (segments[4] === 'validate') {
                await handleEndTransaction(segments[5]);
                setStartPayment(false);
            }

        } catch (error) {
            console.log("Payment method error : ", error);
        }
    }

    const handleEndTransaction = async (merchantTransactionId: string) => {
        try {
            setLoading(true);
            const response = await ClientAxiosInstance.post(`/payment/check-status/${merchantTransactionId}/mobile`, {});

            if (response.data.success) {
                navigation.navigate(successScreen, { data: data });
                setRegisteredItems(prevItems => {
                    const updatedSet = new Set(prevItems);
                    updatedSet.add(data?._id);
                    return updatedSet;
                });
            } else {
                // console.log(data)
                navigation.navigate(failScreen, { data: data });
            }

        } catch (error: any) {
            console.log("End transaction error : ", error.response.data.error);
            if (error.response.data.error === 'Invalid or non-pending payment') {
                navigation.navigate(successScreen, { data: data });
                setRegisteredItems(prevItems => {
                    const updatedSet = new Set(prevItems);
                    updatedSet.add(data?._id);
                    return updatedSet;
                });
            }
        } finally {
            setLoading(false);
        }
    }


    return (
        <Modal
            animationType='slide'
            statusBarTranslucent
            visible={startPayment}
            transparent
        >
            <View className="flex-1 bg-black/20">
                <View className='flex-[0.15]' />
                <View className="flex-[0.85] w-full overflow-hidden bg-white dark:bg-dark rounded-t-xl">
                    <WebView
                        style={{ flex: 1 }}
                        source={{ uri: url }}
                        bounces={false}
                        scalesPageToFit
                        injectedJavaScript={injectedJavaScript}
                        javaScriptEnabled={true}
                        onNavigationStateChange={(navState) => handlePaymentMethod(navState.url)}
                    // originWhitelist={['https://lawcube.org']}
                    />
                </View>
            </View>

        </Modal>
    )
}

export default PaymentModal