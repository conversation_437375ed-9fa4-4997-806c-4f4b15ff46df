import React, { useContext } from 'react';
import {
    Modal,
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableWithoutFeedback,
    KeyboardAvoidingView,
    ScrollView,
    Keyboard,
    Platform,
} from 'react-native';
import Button from './Button';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from '../context/ClientContext';
import Global from '../../globalStyle';
import { Controller, useForm } from 'react-hook-form';
import { GlobalContext } from '../context/GlobalProvider';
import { Toast } from './Toast';
import { Image } from 'expo-image';

interface GetFeedbackProps {
    visible: boolean;
    onClose: () => void;
    chip_id: string;
    feedback_for: string;
}

interface FormData {
    feedback: string;
}

const GetFeedback: React.FC<GetFeedbackProps> = ({ visible, onClose, chip_id, feedback_for }) => {

    const { colorScheme } = useContext(ClientContext);
    const { setLoading } = useContext(GlobalContext);

    const { control, handleSubmit, formState: { errors } } = useForm<FormData>({
        defaultValues: {
            feedback: ''
        },
    });

    const getFeedbackTypeId = (feedback_for: string) => {
        switch (feedback_for) {
            case 'quizfeedback':
                return 'quiz_id';
            case 'mootcourtfeedback':
                return 'mootcourt_id';
            case 'essaywritingfeedback':
                return 'essaywriting_id';
            case 'articlewritingfeedback':
                return 'articlewriting_id';
            case 'debatefeedback':
                return 'debate_id';
            case 'collegeeventfeedback':
                return 'collegeevent_id';
            case 'workshopfeedback':
                return 'workshop_id';
            case 'seminarfeedback':
                return 'seminar_id';
            default:
                return '';
        }
    }

    const onSubmit = async (data: FormData) => {
        try {
            Keyboard.dismiss();
            setLoading(true);
            onClose();

            const feedbackData = {
                feedback: data.feedback,
                [getFeedbackTypeId(feedback_for)]: chip_id,
                feedback_approval_status: 'Pending',
            };

            const feedbackResponse = await ClientAxiosInstance.post(`/${feedback_for}/feedback`, feedbackData);
            console.log(feedbackResponse.data);

            Toast.show({
                type: 'success',
                message: 'Feedback submitted successfully',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });

        } catch (error: any) {
            console.log('Error : ', error.response ? error.response.data : error.message);
            Toast.show({
                type: 'success',
                message: 'Signin successful',
                duration: 3000,
                position: 'bottom',
                animation: 'slide',
                icon: (
                    <Image
                        source={require('../assets/icons/logo/logo-light-big.png')}
                        style={{ width: 24, height: 24 }}
                        contentFit='contain'
                    />
                ),
            });
            onClose();

        } finally {
            setLoading(false);
        }
    };

    const closeFeedback = () => {
        Keyboard.dismiss();
        onClose();
    }

    return (
        <Modal statusBarTranslucent visible={visible} animationType="slide" transparent={true}>

            <TouchableWithoutFeedback onPress={closeFeedback}>

                <View style={styles.modalOverlay}>

                    <KeyboardAvoidingView
                        style={{ flex: 1 }}
                        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    >
                        <ScrollView
                            contentContainerStyle={styles.scrollContent}
                            keyboardShouldPersistTaps="handled"
                        >
                            <View className="w-[90%] sm:w-[70%] bg-white dark:bg-darkcard space-y-5 rounded-md pb-5">

                                <View className="py-5 bg-primary rounded-t-md">
                                    <Text style={Global.text_bold} className="text-center text-secondary text-text20">
                                        Provide your feedback
                                    </Text>
                                </View>

                                <Text style={Global.text_medium} className="text-lg text-center text-secondary dark:text-white">
                                    Please brief us, your experience!
                                </Text>

                                <View className="px-primary">
                                    <Controller
                                        control={control}
                                        name="feedback"
                                        rules={{
                                            required: 'Feedback is required',
                                            minLength: {
                                                value: 3,
                                                message: 'Feedback must be at least 3 characters',
                                            }
                                        }}
                                        render={({ field: { onChange, value } }) => (
                                            <TextInput
                                                style={[
                                                    styles.textArea,
                                                    Global.text_regular,
                                                    { color: colorScheme === 'dark' ? '#fff' : '#2d2828' },
                                                ]}
                                                placeholder="Enter your feedback"
                                                placeholderTextColor="#a8a8a8"
                                                multiline
                                                maxLength={300}
                                                value={value}
                                                onChangeText={onChange}
                                                className="text-secondary dark:text-white"
                                            />
                                        )}
                                    />
                                    {errors.feedback && (
                                        <Text style={Global.text_medium} className="text-red-500 text-text13">
                                            * {errors.feedback.message}
                                        </Text>
                                    )}

                                </View>

                                <View className="px-primary">
                                    <Button onPress={handleSubmit(onSubmit)} title="Submit feedback" color="#2d2828" />
                                </View>

                            </View>

                        </ScrollView>

                    </KeyboardAvoidingView>

                </View>

            </TouchableWithoutFeedback>

        </Modal>
    );
};

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
    },
    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 20,
    },
    textArea: {
        minHeight: 120,
        maxHeight: 150,
        borderColor: '#a8a8a8',
        borderWidth: 1,
        borderRadius: 5,
        paddingHorizontal: 7,
        paddingVertical: 5,
        textAlignVertical: 'top',
        fontSize: 16,
    },
});

export default GetFeedback;
