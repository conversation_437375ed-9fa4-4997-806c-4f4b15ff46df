import React, { useContext } from 'react';
import { View, Text, Pressable } from 'react-native';
import { ClientContext } from '../context/ClientContext';
import { Image } from 'expo-image';
import Feather from '@expo/vector-icons/Feather'
import AntDesign from '@expo/vector-icons/AntDesign'

import Global from '../../globalStyle';

interface Props {
    index?: number;
    name?: string;
    setShowConfirmation?: (value: boolean) => void;
    search?: boolean;
    handleNavigation?: () => void;
    menu?: boolean;
}

const Header: React.FC<Props> = ({ index, name, setShowConfirmation, search, handleNavigation, menu = true }) => {

    const { navigation } = useContext(ClientContext);

    const handleBackNavigation = () => {
        if (setShowConfirmation) setShowConfirmation(true);
        else handleNavigation ? handleNavigation() : navigation.goBack();
    }

    const handleSearchNavigation = () => {
        if (search) navigation.navigate('Search');
    }

    const handleMenu = () => {
        if (menu) navigation.navigate('Menu');
    }

    return (
        <View className="flex-row items-center justify-between w-full bg-secondary px-primary">

            <View className='w-[75%] flex-row items-center space-x-1'>
                {index && ![0, 1, 2, 3, 4].includes(index) &&
                    <Pressable onPress={handleBackNavigation} pressRetentionOffset={40} className='h-[30px] w-[30px] rounded-full justify-center items-center border-[1px] border-white'>
                        <AntDesign name='arrowleft' size={22} color='white' />
                    </Pressable>
                }

                {index === 0 ? (
                    <View className='h-[65px] w-[110px] justify-start items-start z-40'>
                        <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '90%', width: '100%' }} contentFit='contain' />
                    </View>
                ) : (
                    <View className='h-[50px] w-[70%] justify-center items-start'>
                        <Text className='text-lg tracking-wide text-white' style={Global.text_bold}>{name}</Text>
                    </View>
                )}
            </View>

            <View className='flex-row items-center w-[25%] sm:w-[13%] justify-between'>

                <Pressable
                    onPress={handleSearchNavigation}
                    pressRetentionOffset={40}
                >
                    <Feather name='search' size={28} color='white' />
                </Pressable>

                <Pressable onPress={handleMenu} pressRetentionOffset={40}>
                    <View className="space-y-[6px] flex flex-col items-end">
                        <View className="h-[2.7px] rounded-md bg-white w-[25px]" />
                        <View className="h-[2.7px] rounded-md bg-white w-[25px]" />
                        <View className="h-[2.7px] rounded-md bg-white w-[25px]" />
                    </View>
                </Pressable>

            </View>

        </View>
    );
};

export default Header;