import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import React, { useContext } from 'react'
import { Controller } from 'react-hook-form';
import { Dropdown } from 'react-native-element-dropdown';
import { ClientContext } from '../../context/ClientContext';
import { FilterContext } from '../../context/FilterContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import Global from '../../../globalStyle';

interface SortByProps {
    hireLawyerFilter: boolean | undefined;
    courseSort: boolean | undefined;
    readingSort: boolean | undefined;
    competitionSort: boolean | undefined;
    eventSort: boolean | undefined;
    control: any;
    resetField: any;
}

const SortBy: React.FC<SortByProps> = ({ hireLawyerFilter, control, resetField, courseSort, readingSort, competitionSort, eventSort }) => {

    const { colorScheme } = useContext(ClientContext);
    const { setSelectedSort, selectedSort, setSelectedLawyerSort, selectedLawyerSort, setCompEventSelectedSort, compEventSelectedSort } = useContext(FilterContext);

    const data = [
        {
            label: 'Latest',
            value: 'latestdatetime',
        },
        {
            label: 'High to Low',
            value: 'highprice',
        },
        {
            label: 'Low to High',
            value: 'lowprice',
        }
    ]

    const hireLawyerData = [
        {
            label: 'All',
            value: 'all',
        },
        {
            label: 'A to Z',
            value: 'A_to_Z',
        },
        {
            label: 'Top Rated',
            value: 'top_rated',
        },
    ]

    const courseData = [
        {
            label: 'Latest',
            value: 'latest',
        },
        {
            label: 'High to Low',
            value: 'highprice',
        },
        {
            label: 'Low to High',
            value: 'lowprice',
        }
    ]

    const readingsData = [
        {
            label: 'Latest',
            value: 'latest',
        },
        {
            label: 'By Views',
            value: 'viewscount',
        },
    ]

    const renderItem = (item: any) => {
        return (
            <View
                style={[
                    styles.item,
                    {
                        backgroundColor: colorScheme === 'dark' ? '#2F2F2F' : '#fff',
                        borderBottomColor: '#a8a8a8',
                    },
                ]}
            >
                <Text className='tracking-wide text-secondary dark:text-white' style={Global.text_medium}>
                    {item.label}
                </Text>
            </View>
        );
    };

    const handleClearFilters = () => {
        setSelectedSort('latest');
        setCompEventSelectedSort('latestdatetime');
        resetField('sortBy');
    }

    const handleLawyerFilters = () => {
        setSelectedLawyerSort('all');
        resetField('lawyerSort');
    }

    return (
        <View className='space-y-3'>

            {hireLawyerFilter ? (
                <View className='h-[55px]'>
                    <Controller
                        control={control}
                        name="lawyerSort"
                        defaultValue={selectedLawyerSort}
                        render={({ field: { onChange, value } }) => (
                            <Dropdown
                                style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                                selectedTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                }}
                                data={hireLawyerData}
                                search={false}
                                labelField="label"
                                valueField="value"
                                renderItem={renderItem}
                                onChange={item => {
                                    onChange(item.value);
                                    setSelectedLawyerSort(item.value);
                                }}
                                itemContainerStyle={{
                                    borderBottomWidth: 0.5,
                                    borderBottomColor: '#a8a8a8',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                itemTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                    fontSize: 17
                                }}
                                containerStyle={{
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                showsVerticalScrollIndicator={false}
                                placeholderStyle={{
                                    color: '#a8a8a8',
                                }}
                                keyboardAvoiding
                                dropdownPosition='auto'
                                fontFamily='DMSans-Regular'
                                value={value}
                            />
                        )}
                    />
                </View>
            ) : (competitionSort || eventSort) ? (
                <View className='h-[55px]'>
                    <Controller
                        control={control}
                        name="sortBy"
                        defaultValue={compEventSelectedSort}
                        render={({ field: { onChange, value } }) => (
                            <Dropdown
                                style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                                selectedTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                }}
                                data={data}
                                search={false}
                                labelField="label"
                                valueField="value"
                                renderItem={renderItem}
                                onChange={item => {
                                    onChange(item.value);
                                    setCompEventSelectedSort(item.value);
                                }}
                                itemContainerStyle={{
                                    borderBottomWidth: 0.5,
                                    borderBottomColor: '#a8a8a8',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                itemTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                    fontSize: 17
                                }}
                                containerStyle={{
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                showsVerticalScrollIndicator={false}
                                placeholderStyle={{
                                    color: '#a8a8a8',
                                }}
                                keyboardAvoiding
                                dropdownPosition='auto'
                                fontFamily='DMSans-Regular'
                                value={value}
                            />
                        )}
                    />
                </View>
            ):(
                <View className='h-[55px]'>
                    <Controller
                        control={control}
                        name="sortBy"
                        defaultValue={selectedSort}
                        render={({ field: { onChange, value } }) => (
                            <Dropdown
                                style={[styles.dropdown, { backgroundColor: colorScheme === 'dark' ? '#000' : '#fff', borderColor: '#a8a8a8' }]}
                                selectedTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                }}
                                data={readingSort ? readingsData : courseData}
                                search={false}
                                labelField="label"
                                valueField="value"
                                renderItem={renderItem}
                                onChange={item => {
                                    onChange(item.value);
                                    setSelectedSort(item.value);
                                }}
                                itemContainerStyle={{
                                    borderBottomWidth: 0.5,
                                    borderBottomColor: '#a8a8a8',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                itemTextStyle={{
                                    color: colorScheme === 'light' ? '#000' : '#fff',
                                    fontSize: 17
                                }}
                                containerStyle={{
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                    backgroundColor: colorScheme === 'dark' ? '#2d2828' : '#fff',
                                }}
                                showsVerticalScrollIndicator={false}
                                placeholderStyle={{
                                    color: '#a8a8a8',
                                }}
                                keyboardAvoiding
                                dropdownPosition='auto'
                                fontFamily='DMSans-Regular'
                                value={value}
                            />
                        )}
                    />
                </View>
            )}

            <TouchableOpacity activeOpacity={0.8} onPress={hireLawyerFilter ? handleLawyerFilters : handleClearFilters} className='flex-row items-center self-center h-8 space-x-1'>
                <FontAwesome6 name='trash' size={16} color='#a8a8a8' />
                <Text className='underline text-text15 text-greycolor' style={Global.text_bold}>Remove filter</Text>
            </TouchableOpacity>

        </View>
    )
}

const styles = StyleSheet.create({
    dropdown: {
        height: '100%',
        width: '100%',
        borderWidth: 1,
        borderRadius: 8,
        paddingLeft: 16,
        paddingRight: 8,
        overflow: 'hidden'
    },
    item: {
        padding: 16,
        borderBottomWidth: 0.5,
    }
});

export default SortBy