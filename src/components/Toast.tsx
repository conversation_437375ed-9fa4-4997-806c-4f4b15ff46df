import React, { useEffect, useState } from 'react';
import styled from 'styled-components/native';
import { Text, ViewStyle, Keyboard, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { Easing, useSharedValue, withTiming, useAnimatedStyle, runOnJS } from 'react-native-reanimated';

type ToastType = 'success' | 'warning' | 'error' | 'info';
type ToastPosition = 'top' | 'bottom' | 'center';
type AnimationType = 'fade' | 'slide' | 'slideLeft' | 'slideRight';

interface ToastProps {
	type: ToastType;
	message: string;
	fontFamily?: string;
	position?: ToastPosition;
	customStyle?: ViewStyle;
	duration?: number;
	onClose?: () => void;
	animation?: AnimationType;
	icon?: React.ReactNode;
}

const getBackgroundColor = (type: ToastType) => {
	switch (type) {
		case 'success':
			return '#e0f7ea';
		case 'warning':
			return '#fef7ec';
		case 'error':
			return '#fae1db';
		case 'info':
			return '#e0f3f8';
		default:
			return '#fff';
	}
};

const getBorderColor = (type: ToastType) => {
	switch (type) {
		case 'success':
			return '#1f8722';
		case 'warning':
			return '#f08135';
		case 'error':
			return '#d9100a';
		case 'info':
			return '#0288d1';
		default:
			return '#ddd';
	}
};

const ToastContainer = styled(Animated.View) <{ type: ToastType, position: ToastPosition }>`
  flex-direction: row;
  align-items: center;
  padding: 10px;
  border-radius: 25px;
  margin: 10px;
  background-color: ${({ type }) => getBackgroundColor(type)};
  border: 1px solid ${({ type }) => getBorderColor(type)};
  position: absolute;
  max-width: 80%;
  align-self: center;
  ${({ position }) => position === 'top' && 'top: 50px;'}
  ${({ position }) => position === 'bottom' && 'bottom: 40px;'}
  ${({ position }) => position === 'center' && 'top: 50%;'}
`;

const IconContainer = styled.View`
  margin-right: 10px;
`;

const Toast: React.FC<ToastProps> & { show: (props: ToastProps) => void } = ({
	type,
	message,
	fontFamily,
	position = 'top',
	customStyle,
	duration = 3000,
	onClose,
	animation = 'fade',
	icon,
}) => {
	const opacity = useSharedValue(0);
	const translateY = useSharedValue(0);
	const translateX = useSharedValue(0);

	useEffect(() => {
		const showAnimation = withTiming(1, { duration: 500, easing: Easing.inOut(Easing.ease) });
		const hideAnimation = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, () => {
			if (onClose) {
				runOnJS(onClose)();
			}
		});

		switch (animation) {
			case 'slide':
				translateY.value = position === 'bottom' ? 50 : -50;
				break;
			case 'slideLeft':
				translateX.value = -200;
				break;
			case 'slideRight':
				translateX.value = 200;
				break;
			default:
				break;
		}

		opacity.value = showAnimation;
		translateY.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) });
		translateX.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) });

		const timer = setTimeout(() => {
			opacity.value = hideAnimation;
			translateY.value = withTiming(animation === 'slide' ? (position === 'bottom' ? 50 : -50) : 0, { duration: 500, easing: Easing.inOut(Easing.ease) });
			translateX.value = withTiming(
				animation === 'slideLeft' ? -200 : animation === 'slideRight' ? 200 : 0,
				{ duration: 500, easing: Easing.inOut(Easing.ease) },
			);
		}, duration);

		return () => {
			clearTimeout(timer);
		};
	}, [animation, duration, onClose, position]);

	const animatedStyle = useAnimatedStyle(() => {
		return {
			opacity: opacity.value,
			transform:
				animation === 'slide'
					? [{ translateY: translateY.value }]
					: animation === 'slideLeft' || animation === 'slideRight'
						? [{ translateX: translateX.value }]
						: [],
		};
	});

	const iconName =
		type === 'success' ? 'checkmark-circle' :
			type === 'warning' ? 'warning' :
				type === 'error' ? 'close-circle' :
					'information-circle';

	const iconColor =
		type === 'success' ? '#28a745' :
			type === 'warning' ? '#856404' :
				type === 'error' ? '#dc3545' :
					'#0288d1';

	return (
		<ToastContainer type={type} position={position} style={[customStyle || {}, animatedStyle, { zIndex: 9999 }]}>
			<IconContainer>
				{icon || <Ionicons name={iconName} size={24} color={iconColor} />}
			</IconContainer>
			<Text style={{ fontFamily: fontFamily || 'DMSans-Medium' }}>{message}</Text>
		</ToastContainer>
	);
};

Toast.show = (props: ToastProps) => {
	const toastManager = ToastManager.getInstance();
	toastManager.show(props);
};

class ToastManager {
	private static instance: ToastManager | null = null;
	private setToastProps: ((props: ToastProps) => void) | null = null;

	private constructor() { }

	public static getInstance(): ToastManager {
		if (!ToastManager.instance) {
			ToastManager.instance = new ToastManager();
		}
		return ToastManager.instance;
	}

	public register(setToastProps: (props: ToastProps) => void) {
		this.setToastProps = setToastProps;
	}

	public show(props: ToastProps) {
		if (this.setToastProps) {
			this.setToastProps(props);
		}
	}
}

const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [toastProps, setToastProps] = useState<ToastProps | null>(null);
	const [keyboardHeight, setKeyboardHeight] = useState(0);

	useEffect(() => {
		if (Platform.OS === 'ios') {
			const toastManager = ToastManager.getInstance();
			toastManager.register(setToastProps);

			const keyboardShowListener = Keyboard.addListener(
				Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
				(event) => {
					setKeyboardHeight(event.endCoordinates.height);
				}
			);

			const keyboardHideListener = Keyboard.addListener(
				Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
				() => {
					setKeyboardHeight(0);
				}
			);

			return () => {
				keyboardShowListener.remove();
				keyboardHideListener.remove();
			};
		}
	}, []);

	useEffect(() => {
		const toastManager = ToastManager.getInstance();
		toastManager.register(setToastProps);
	}, []);

	return (
		<>
			{children}
			{toastProps && (
				<Toast
					customStyle={{
						bottom:
							toastProps.position === 'bottom'
								? keyboardHeight + 20
								: 'auto',
					}}
					{...toastProps}
					onClose={() => setToastProps(null)}
				/>
			)}
		</>
	);
};


export { Toast, ToastProvider };
