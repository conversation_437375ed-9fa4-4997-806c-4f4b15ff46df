import { View, Text } from 'react-native'
import React, { useContext } from 'react'
import Global from '../../globalStyle'
import { ClientContext } from '../context/ClientContext'
import Trophy from '../assets/description/Trophy'
import Notepad from '../assets/description/Notepad'
import Participate from '../assets/description/Participate'

const FaqImage = () => {

    const { colorScheme } = useContext(ClientContext);

    return (
        <View className='flex-row items-center justify-center w-full'>

            <View className='w-[90px] h-[90px] items-center justify-center space-y-[2px] rounded-full' style={{ borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }}>
                <Notepad />
                <Text className='text-secondary dark:text-white text-[11px]' style={Global.text_medium}>Register</Text>
            </View>

            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

            <View className='w-[90px] h-[90px] items-center justify-center space-y-[2px] rounded-full' style={{ borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }}>
                <Participate />
                <Text className='text-secondary dark:text-white text-[11px]' style={Global.text_medium}>Participate</Text>
            </View>

            <View style={{ flex: 1, borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }} />

            <View className='w-[90px] h-[90px] items-center justify-center space-y-[2px] rounded-full' style={{ borderWidth: 0.5, borderStyle: 'dashed', borderColor: colorScheme === 'dark' ? '#fff' : '#2d2828' }}>
                <Trophy />
                <Text className='text-secondary dark:text-white text-[11px]' style={Global.text_medium}>Win</Text>
            </View>

        </View>
    )
}

export default FaqImage