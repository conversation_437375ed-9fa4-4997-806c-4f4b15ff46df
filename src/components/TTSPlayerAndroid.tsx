import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import Slider from '@react-native-community/slider';
import * as Speech from 'expo-speech';
import Icon from '@expo/vector-icons/FontAwesome5';
import Global from '../../globalStyle';
import { htmlToText } from 'html-to-text';

const { width: screenWidth } = Dimensions.get('window');
const CHUNK_SIZE = 3990; // Max characters per speech request

const TTSPlayerAndroid: React.FC<{ text: string }> = ({ text }) => {
    const [paused, setPaused] = useState<boolean>(true);
    const [currentTime, setCurrentTime] = useState<number>(0);
    const [duration, setDuration] = useState<number>(0);
    const [currentChunkIndex, setCurrentChunkIndex] = useState<number>(0);
    const [lastPosition, setLastPosition] = useState<number>(0);

    const plainText = useRef<string>('');
    const chunks = useRef<string[]>([]);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        // Convert HTML to plain text
        plainText.current = htmlToText(text, {
            selectors: [
                { selector: 'img', format: 'skip' },
                { selector: 'figure', format: 'skip' },
                { selector: 'a', format: 'skip' },
                { selector: 'table', format: 'skip' },
                { selector: 'pre', format: 'skip' },
            ],
        }).replace(/\s+/g, ' ').trim();

        // Split text into chunks
        chunks.current = [];
        for (let i = 0; i < plainText.current.length; i += CHUNK_SIZE) {
            chunks.current.push(plainText.current.slice(i, i + CHUNK_SIZE));
        }

        // Estimate duration based on word count
        const words = plainText.current.split(' ').length;
        const averageWPM = 130;
        const estimatedDuration = (words / averageWPM) * 60;
        setDuration(Math.ceil(estimatedDuration));

        setCurrentTime(0);
        setLastPosition(0);

        return () => stopSpeech(); // Cleanup on unmount
    }, [text]);

    const startTimer = () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
        intervalRef.current = setInterval(() => {
            setCurrentTime((prev) => {
                if (prev < duration) {
                    setLastPosition(prev + 1);
                    return prev + 1;
                } else {
                    stopSpeech();
                    return duration;
                }
            });
        }, 1000);
    };

    const stopSpeech = () => {
        Speech.stop();
        setPaused(true);
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };

    const playChunk = (index: number, startPosition: number = 0) => {
        if (index >= chunks.current.length) {
            stopSpeech();
            return;
        }

        let words = chunks.current[index].split(' ');
        let remainingText = words.slice(startPosition).join(' ');

        Speech.speak(remainingText, {
            onStart: startTimer,
            onDone: () => {
                setCurrentChunkIndex(index + 1);
                setLastPosition(0); // Reset for the next chunk
                playChunk(index + 1);
            },
            onStopped: stopSpeech,
            onError: (error) => {
                console.log('Speech error:', error);
                stopSpeech();
            },
            language: 'en-US',
            pitch: 1,
            rate: 0.8,
        });
    };

    const togglePlayPause = () => {
        if (paused) {
            playChunk(currentChunkIndex, lastPosition);
        } else {
            stopSpeech();
        }
        setPaused(!paused);
    };

    const getFormattedTime = (time: number): string => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    return (
        <View className='flex-row items-center justify-between'>

            <TouchableOpacity activeOpacity={0.8} onPress={togglePlayPause} className="px-3 py-2 bg-[#F3F4F6] rounded-full space-x-2 flex-row items-center">
                <Text style={Global.text_medium}>{paused ? 'Play' : 'Pause'}</Text>
                <Icon name={paused ? 'play' : 'pause'} color="#FFD166" size={15} />
            </TouchableOpacity>

            <Text style={Global.text_medium} className='text-secondary dark:text-white'>{getFormattedTime(currentTime)}</Text>

            <Slider
                style={{ width: screenWidth * 0.4 }}
                value={currentTime}
                minimumValue={0}
                maximumValue={duration}
                minimumTrackTintColor="#fdd066"
                maximumTrackTintColor="#a8a8a8"
                thumbTintColor="#fdd066"
                disabled
            />

            <Text style={Global.text_medium} className='text-secondary dark:text-white'>{getFormattedTime(duration)}</Text>

        </View>
    );
};

export default TTSPlayerAndroid;