import React from 'react';
import { View, StatusBar, StatusBarStyle, Platform, StyleSheet, SafeAreaView } from 'react-native';

interface StatusBarProps {
    backgroundColor?: string;
    barStyle?: StatusBarStyle;
}

const CustomStatusBar: React.FC<StatusBarProps> = ({ backgroundColor, barStyle }) => {
    return (
        <View style={[styles.container, { backgroundColor: backgroundColor || '#2D2828' }]}>
            <SafeAreaView style={styles.statusBar}>
                <StatusBar
                    barStyle={barStyle || 'light-content'}
                    translucent={true}
                    animated={true}
                    backgroundColor={backgroundColor || '#2D2828'}
                />
            </SafeAreaView>
        </View>
    );
};

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? StatusBar.currentHeight : 0;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#2D2828',
    },
    statusBar: {
        height: STATUSBAR_HEIGHT,
    },
});

export default CustomStatusBar;
