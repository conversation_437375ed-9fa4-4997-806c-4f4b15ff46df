import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { reloadAppAsync } from "expo";
import CustomStatusBar from '../components/CustomStatusBar'
import Global from '../../globalStyle'
import { MaterialCommunityIcons } from '@expo/vector-icons'
import { Image } from 'expo-image';

const NoInternet = () => {

    const handleReloadPage = async () => {
        await reloadAppAsync();
    }

    return (
        <View className='flex-1 bg-white dark:bg-dark'>

            <CustomStatusBar />

            <View className='h-[70] w-full bg-secondary items-center justify-center'>
                <Image source={require('../assets/icons/logo/logo-dark-small.png')} style={{ height: '70%', width: 150 }} contentFit='contain' />
            </View>

            <View className='h-full w-full'>

                <View className='h-3/5 items-center justify-center space-y-9'>

                    <View className='space-y-2'>
                        <Text className='text-center text-2xl text-secondary dark:text-white tracking-wider' style={Global.text_bold}>Oops!</Text>
                        <Text className='text-center text-lg text-secondary dark:text-white tracking-wider' style={Global.text_regular}>Something went wrong</Text>
                    </View>

                    <View className='items-center'>
                        <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={handleReloadPage}
                            className='bg-primary h-[50px] space-x-2 rounded-full flex-row items-center justify-center px-5'
                        >
                            <Text className='text-text16 tracking-wide text-secondary' style={Global.text_bold}>Reload Page</Text>
                            <MaterialCommunityIcons name='reload' size={25} color='black' />
                        </TouchableOpacity>
                    </View>

                </View>

            </View>

        </View>
    )
}

export default NoInternet