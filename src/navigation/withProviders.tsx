import React, { ComponentType, useMemo } from 'react';

const withProviders = (Component: ComponentType<any>, Providers: ComponentType<any>[]) => {
	return (props: any) => {
		const WrappedComponent = useMemo(() => {
			return Providers.reduceRight(
				(AccumulatedComponent, Provider) => {
					return (providerProps: any) => (
						<Provider>
							<AccumulatedComponent {...providerProps} />
						</Provider>
					);
				},
				(providerProps: any) => <Component {...providerProps} />
			);
		}, [Providers, Component]);

		return <WrappedComponent {...props} />;
	};
};

export default withProviders;