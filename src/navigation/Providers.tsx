import CareerProvider from "../context/CareerContext";
import CompetitionProvider from "../context/CompetitionContext";
import CourseProvider from "../context/CourseContext";
import EventProvider from "../context/EventContext";
import HireLawyerProvider from "../context/HireLawyerContext";
import ReadingsProvider from "../context/ReadingsContext";
import Career from "../screens/careers/Career";

import Competition from "../screens/competition/Competition";
import Courses from "../screens/courses/Courses";
import Events from "../screens/events/Events";
import HireLawyerHome from "../screens/hire-lawyer/HireLawyerHome";
import Readings from "../screens/readings/Readings";

export const CompetitionsWithProvider = () => (
    <CompetitionProvider>
        <Competition />
    </CompetitionProvider>
);

export const EventsWithProvider = () => (
    <EventProvider>
        <Events />
    </EventProvider>
);

export const CareersWithProvider = () => (
    <CareerProvider>
        <Career />
    </CareerProvider>
);

export const LawyersWithProvider = () => (
    <HireLawyerProvider>
        <HireLawyerHome />
    </HireLawyerProvider>
);

export const CoursesWithProvider = () => (
    <CourseProvider>
        <Courses />
    </CourseProvider>
);

export const ReadingsWithProvider = () => (
    <ReadingsProvider>
        <Readings />
    </ReadingsProvider>
);
