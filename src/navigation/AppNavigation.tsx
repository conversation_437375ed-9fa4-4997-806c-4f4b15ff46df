import * as React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Auth, Main } from './NavigationScreens';
import ClientProvider, { ClientContext } from '../context/ClientContext';
import GlobalProvider, { GlobalContext } from '../context/GlobalProvider';
import FilterProvider from '../context/FilterContext';
import { ToastProvider } from '../components/Toast';
import LikeSaveProvider from '../context/LikeSaveContext';
import SearchProvider from '../context/SearchContext';
import { Linking } from 'react-native';
import { ClientAxiosInstance } from '../lib/axiosInstance';

const Render = ({ initialScreen }: { initialScreen: string }) => {
	const { token } = React.useContext(ClientContext);
	return token ? <Main /> : <Auth initialScreen={initialScreen} />;
};

const AppNavigation = ({ initialScreen }: { initialScreen: string }) => {
	const navigateRef: any = React.createRef();
	const { setLoading } = React.useContext(GlobalContext);

	const linking = {
		prefixes: ['https://www.lawcube.org/', 'lawcube://'],
		config: {
			screens: {
				Readings: 'readings',
			},
		}
	};

	React.useEffect(() => {
		const handleInitialURL = async () => {
			try {
				setLoading(true);
				const initialUrl = await Linking.getInitialURL();

				if (initialUrl) {
					const path = initialUrl.replace(linking.prefixes[0], '');
					const segments = path.split('/');

					console.log(segments)

					if (segments.length >= 3) {
						const [type, id] = [segments[1], segments[2]];
						try {
							const response = await ClientAxiosInstance.get(`/${type}/${id}`);
							const data = response.data.data;
							setLoading(false);
							navigateRef.current.navigate('ReadingDetail', { item: data, selectedChipData: [data] });
						} catch (apiError) {
							console.log('Error fetching deep-link content:', apiError);
						}
					} else {
						console.log('Invalid deep-link format.');
					}
				}
			} catch (error) {
				console.log('Error handling initial URL:', error);
			} finally {
				setLoading(false);
			}
		};

		handleInitialURL();
	}, []);

	return (
		<NavigationContainer
			ref={(navigatorRef) => {
				if (navigatorRef) {
					navigateRef.current = navigatorRef;
				}
			}}
			linking={linking}
		>
			<ClientProvider>
				<GlobalProvider>
					<LikeSaveProvider>
						<FilterProvider>
							<SearchProvider>
								<SafeAreaProvider>
									<ToastProvider>
										<Render initialScreen={initialScreen} />
									</ToastProvider>
								</SafeAreaProvider>
							</SearchProvider>
						</FilterProvider>
					</LikeSaveProvider>
				</GlobalProvider>
			</ClientProvider>
		</NavigationContainer>
	);
};

export default AppNavigation;