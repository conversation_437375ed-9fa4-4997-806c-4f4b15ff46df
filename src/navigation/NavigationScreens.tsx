import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import ExploreHome from '../screens/explore/ExploreHome';
import MainHome from '../screens/home/<USER>';
import DiscussionHome from '../screens/discussion/DiscussionHome';
import ProfileHome from '../screens/profile/ProfileHome';

import SignIn from '../auth/SignIn';
import SignUp from '../auth/SignUp';
import OtpVerify from '../auth/OtpVerify';
import ForgotPassword from '../auth/ForgotPassword';

import { Dimensions, Platform, Text, View } from 'react-native';
import TermsConditions from "../screens/menu/screens/Terms&Condition";
import HomeIcon1, { HomeIcon2 } from '../assets/icons/bottom-tab/HomeIcon';
import ExploreIcon1, { ExploreIcon2 } from '../assets/icons/bottom-tab/ExploreIcon';
import HireLawyer1, { HireLawyer2 } from '../assets/icons/bottom-tab/HireLawyerIcon';
import Discussion1, { Discussion2 } from '../assets/icons/bottom-tab/DiscussionIcon';
import Profile1, { Profile2 } from '../assets/icons/bottom-tab/ProfileIcon';
import PostToUs from "../screens/home/<USER>/PostToUs";
import CompetitionDetails from '../screens/competition/screens/CompetitonDetails'
import CompetitionCheckout from "../screens/competition/screens/CompetitionCheckout";
import QuizRules from "../screens/competition/screens/QuizRules";
import Quiz from "../screens/competition/screens/Quiz";
import QuizResult from "../screens/competition/screens/QuizResult";
import Menu from "../screens/menu/Menu";
import CareerDetails from "../screens/careers/screens/CareerDetails";
import EventDetails from "../screens/events/screens/EventDetails";
import EventCheckout from "../screens/events/screens/EventCheckout";
import Registration from "../screens/careers/screens/Registration";
import ReadingDetail from "../screens/readings/screens/ReadingDetail";
import LawyerDetails from "../screens/hire-lawyer/screens/LawyerDetails";
import HireLawyerRegistration from "../screens/hire-lawyer/screens/HireLawyerRegistration";
import SubmissionSuccess from "../screens/hire-lawyer/screens/SubmissionSuccess";
import AdvocateRegistration from "../screens/hire-lawyer/screens/AdvocateRegistration";
import Courses from "../screens/courses/Courses";
import CourseDetails from "../screens/courses/screens/CourseDetails";
import CourseCheckout from "../screens/courses/screens/CourseCheckout";
import BookedCourse from "../screens/courses/screens/BookedCourse";
import QuizAnswers from "../screens/competition/screens/QuizAnswers";
import Search from "../screens/search/Search";
import ChangePassword from "../auth/ChangePassword";
import DiscussionDetail from "../screens/discussion/screens/DiscussionDetail";
import Global from "../../globalStyle";
import HelpSupport from "../screens/menu/screens/HelpSupport";
import { SocketProvider } from "../context/SocketContext";
import ResetPassword from "../auth/ResetPassword";
import CourseQuiz from "../screens/courses/screens/CourseQuiz";
import CourseQuizRules from "../screens/courses/screens/CourseQuizRules";
import CourseQuizResult from "../screens/courses/screens/CourseQuizResult";
import CourseQuizAnswers from "../screens/courses/screens/CourseQuizAnswers";
import EditAdvocateProfile from "../screens/hire-lawyer/screens/EditAdvocateProfile";
import PrivacyPolicies from "../screens/menu/screens/PrivacyPolicies";
import EventPaymentCancelled from "../screens/events/payment/EventPaymentCancelled";
import EventPaymentConfirmation from "../screens/events/payment/EventPaymentConfirmation";
import CompPaymentConfirmation from "../screens/competition/payment/CompPaymentConfirmation";
import CompPaymentCancelled from "../screens/competition/payment/CompPaymentCancelled";
import CoursePaymentConfirmation from "../screens/courses/payment/CoursePaymentConfirmation";
import CoursePaymentCancelled from "../screens/courses/payment/CoursePaymentCancelled";
import AdvocateConfirmation from "../screens/hire-lawyer/screens/AdvocateConfirmation";
import RefundPolicies from "../screens/menu/screens/RefundPolicies";
import { CareersWithProvider, CompetitionsWithProvider, CoursesWithProvider, EventsWithProvider, LawyersWithProvider, ReadingsWithProvider } from "./Providers";
import withProviders from "./withProviders";

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

export const Auth = ({ initialScreen }: { initialScreen: string }) => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName={initialScreen}>
        <Stack.Screen name="SignIn" component={SignIn} />
        <Stack.Screen name="SignUp" component={SignUp} />
        <Stack.Screen name="OtpVerify" component={OtpVerify} />
        <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
        <Stack.Screen name="ResetPassword" component={ResetPassword} />
    </Stack.Navigator>
);

const HomeScreens: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="MainHome">

        <Stack.Screen name="MainHome" component={MainHome} />

        <Stack.Screen name="Competition" component={CompetitionsWithProvider} />

        <Stack.Screen name="Events" component={EventsWithProvider} />

        <Stack.Screen name="Career" component={CareersWithProvider} />

        <Stack.Screen name="Readings" component={ReadingsWithProvider} />

        <Stack.Screen name="Courses" component={CoursesWithProvider} />

    </Stack.Navigator>
);

const ExploreScreens: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="ExploreHome">
        <Stack.Screen name="ExploreHome" component={ExploreHome} />
    </Stack.Navigator>
);

const HireLawyerScreens: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="HireLawyerHome">
        <Stack.Screen name="HireLawyerHome" component={LawyersWithProvider} />
    </Stack.Navigator>
);

const DiscussionScreens: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="DiscussionHome">
        <Stack.Screen name="DiscussionHome" component={withProviders(DiscussionHome, [SocketProvider])} />
        <Stack.Screen name="DiscussionDetail" component={withProviders(DiscussionDetail, [SocketProvider])} />
    </Stack.Navigator>
);

const ProfileScreens: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="ProfileHome">
        <Stack.Screen name="ProfileHome" component={ProfileHome} />
    </Stack.Navigator>
);

const MainTabs: React.FC = () => {

    const { width, height } = Dimensions.get('window');
    const isLargeDevice = width >= 768;
    const mini = width >= 600;
    const large = width >= 950;
    const { height: screenHeight } = Dimensions.get('window');
    const tabBarHeight =
        Platform.OS === 'ios' && screenHeight <= 667
            ? 70
            : Platform.OS === 'android'
                ? 85
                : 90;

    return (
        <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarHideOnKeyboard: true,
                headerShown: false,
                tabBarStyle: {
                    height: tabBarHeight,
                    width: '100%',
                    backgroundColor: '#2D2828',
                    borderTopColor: '#1F1F1F',
                    borderTopLeftRadius: 20,
                    borderTopRightRadius: 20,
                    position: 'absolute',
                    bottom: 0,
                },
                tabBarLabel: ({ focused }) => {
                    return (
                        <Text style={[{
                            fontSize: isLargeDevice ? width * 0.018 : (mini ? width * 0.020 : width * 0.03),
                            letterSpacing: isLargeDevice ? width * 0.001 : width * 0.001,
                            color: focused ? '#FDD066' : '#fff',
                            left: isLargeDevice ? 30 : 0,
                            top: large ? 5 : 0,
                        }, Global.text_regular]}>
                            {route.name}
                        </Text>
                    );
                },
                tabBarIcon: ({ focused }) => {
                    const iconSize = isLargeDevice ? width * 0.030 : (mini ? width * 0.03 : width * 0.07);
                    if (route.name === 'Home') {
                        return (
                            <View style={{ paddingRight: width * 0.005 }}>
                                {focused ? <HomeIcon1 width={iconSize} height={iconSize} /> : <HomeIcon2 width={iconSize} height={iconSize} />}
                            </View>
                        );
                    } else if (route.name === 'Explore') {
                        return (
                            <View>
                                {focused ? <ExploreIcon1 width={iconSize} height={iconSize} /> : <ExploreIcon2 width={iconSize} height={iconSize} />}
                            </View>
                        );
                    } else if (route.name === 'Hire Lawyer') {
                        return (
                            <View>
                                {focused ? <HireLawyer1 width={iconSize} height={iconSize} /> : <HireLawyer2 width={iconSize} height={iconSize} />}
                            </View>
                        );
                    } else if (route.name === 'Discussion') {
                        return (
                            <View>
                                {focused ? <Discussion1 width={iconSize} height={iconSize} /> : <Discussion2 width={iconSize} height={iconSize} />}
                            </View>
                        );
                    } else if (route.name === 'Profile') {
                        return (
                            <View>
                                {focused ? <Profile1 width={iconSize} height={iconSize} /> : <Profile2 width={iconSize} height={iconSize} />}
                            </View>
                        );
                    }
                }
            })}>
            <Tab.Screen name="Home" component={HomeScreens} />
            <Tab.Screen name="Explore" component={ExploreScreens} />
            <Tab.Screen name="Hire Lawyer" component={HireLawyerScreens} />
            <Tab.Screen name="Discussion" component={DiscussionScreens} />
            <Tab.Screen name="Profile" component={ProfileScreens} />
        </Tab.Navigator>
    );
};

export const Main: React.FC = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }}>

        <Stack.Screen name="MainTabs" component={MainTabs} />

        <Stack.Screen name="Menu" component={Menu} />
        <Stack.Screen name="HelpSupport" component={HelpSupport} />
        <Stack.Screen name="PrivacyPolicies" component={PrivacyPolicies} />
        <Stack.Screen name="RefundPolicies" component={RefundPolicies} />
        <Stack.Screen name="PostToUs" component={PostToUs} />
        <Stack.Screen name="TermsConditions" component={TermsConditions} />

        <Stack.Screen name="CompetitionDetails" component={CompetitionDetails} />
        <Stack.Screen name="CompetitionCheckout" component={CompetitionCheckout} />
        <Stack.Screen name="CompPaymentCancelled" component={CompPaymentCancelled} options={{ gestureEnabled: false }} />
        <Stack.Screen name="CompPaymentConfirmation" component={CompPaymentConfirmation} options={{ gestureEnabled: false }} />

        <Stack.Screen name="QuizRules" component={QuizRules} />
        <Stack.Screen name="Quiz" component={Quiz} />
        <Stack.Screen name="QuizResult" component={QuizResult} />
        <Stack.Screen name="QuizAnswers" component={QuizAnswers} />

        <Stack.Screen name="EventDetails" component={EventDetails} />
        <Stack.Screen name="EventCheckout" component={EventCheckout} />
        <Stack.Screen name="EventPaymentCancelled" component={EventPaymentCancelled} options={{ gestureEnabled: false }} />
        <Stack.Screen name="EventPaymentConfirmation" component={EventPaymentConfirmation} options={{ gestureEnabled: false }} />

        <Stack.Screen name="Search" component={Search} />

        <Stack.Screen name="ReadingDetail" component={ReadingDetail} />

        <Stack.Screen name="ChangePassword" component={ChangePassword} />

        <Stack.Screen name="CareerDetails" component={CareerDetails} />
        <Stack.Screen name="Registration" component={Registration} />

        <Stack.Screen name="CourseDetails" component={CourseDetails} />
        <Stack.Screen name="CourseCheckout" component={CourseCheckout} />
        <Stack.Screen name="BookedCourse" component={BookedCourse} />
        <Stack.Screen name="CourseQuizRules" component={CourseQuizRules} />
        <Stack.Screen name="CourseQuiz" component={CourseQuiz} />
        <Stack.Screen name="CourseQuizResult" component={CourseQuizResult} />
        <Stack.Screen name="CourseQuizAnswers" component={CourseQuizAnswers} />
        <Stack.Screen name="CoursePaymentConfirmation" component={CoursePaymentConfirmation} options={{ gestureEnabled: false }} />
        <Stack.Screen name="CoursePaymentCancellated" component={CoursePaymentCancelled} options={{ gestureEnabled: false }} />

        <Stack.Screen name="LawyerDetails" component={LawyerDetails} />
        <Stack.Screen name="HireLawyerRegistration" component={HireLawyerRegistration} />
        <Stack.Screen name="SubmissionSuccess" component={SubmissionSuccess} />
        <Stack.Screen name="AdvocateRegistration" component={AdvocateRegistration} />
        <Stack.Screen name="EditAdvocateProfile" component={EditAdvocateProfile} />
        <Stack.Screen name="AdvocateConfirmation" component={AdvocateConfirmation} />

    </Stack.Navigator>
);