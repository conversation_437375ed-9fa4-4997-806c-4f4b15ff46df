import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { FilterContext } from './FilterContext';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';
interface EventContextValue {
    fetchData: () => void;
    selectedChipData: any[];
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalPages: number;
    selectedChip: string;
    setSelectedChip: (value: string) => void;
    showPagination: boolean;
    setShowPagination: (value: boolean) => void;
    loading: boolean;
    setLoading: (value: boolean) => void;
    chip: string | null;
    postedByList: any[];
    refreshing: boolean;
    onRefresh: () => void;
}

export const EventContext = createContext<EventContextValue>({
    fetchData: () => { },
    selectedChipData: [],
    currentPage: 1,
    setCurrentPage: () => { },
    totalPages: 0,
    selectedChip: '',
    setSelectedChip: () => { },
    showPagination: true,
    setShowPagination: () => { },
    loading: true,
    setLoading: () => { },
    chip: 'collegeevent',
    postedByList: [],
    refreshing: false,
    onRefresh: () => { }
});

const EventProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { selectedLocation, compDate, compPrice, selectedMode, selectedPostedBy, compEventSelectedSort,
        setSelectedLocation, setCompDate, setCompPrice, setSelectedMode, selectedCategory, setSelectedCategory,
        setSelectedPostedBy, setCompLocation, setCompEventSelectedSort, eventLocation, setEventLocation } = useContext(FilterContext);

    const { currentEventChip, isLarge } = useContext(ClientContext);

    const [selectedChipData, setSelectedChipData] = useState<any[]>([]);
    const [postedByList, setPostedByList] = useState<any[]>([]);

    const [selectedChip, setSelectedChip] = useState(currentEventChip);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const [refreshing, setRefreshing] = useState(false);
    const onRefresh = useCallback(() => {
        setRefreshing(true);
    }, []);

    const chip = useMemo(() => {
        switch (selectedChip) {
            case 'College Events': return 'collegeevent';
            case 'Workshop': return 'workshop';
            case 'Seminars': return 'seminar';
            default: return null;
        }
    }, [selectedChip]);

    const searchChip = useMemo(() => {
        switch (selectedChip) {
            case 'College Events': return 'searchCollegeevent';
            case 'Workshop': return 'searchWorkshop';
            case 'Seminars': return 'searchSeminar';
            default: return null;
        }
    }, [selectedChip]);

    const formatDate = useCallback((date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }, []);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const limit = isLarge ? 6 : 3;
            let url = `/${chip}/${searchChip}?page=${currentPage}&limit=${limit}&sort=${compEventSelectedSort}`;

            if (selectedLocation) url += `&location=${selectedLocation}`;
            if (selectedMode) url += `&mode=${selectedMode}`;
            if (compPrice.from || compPrice.to) url += `&discount_price=${compPrice.from},${compPrice.to}`;
            if (selectedPostedBy) url += `&posted_by=${selectedPostedBy}`;
            if (compDate) {
                setCurrentPage(1);
                url += `&date_time=${formatDate(new Date(compDate))}`;
            }
            if (selectedCategory.length > 0) {
                const category = selectedCategory.join(',');
                url += `&global_category_name=${category}`;
            }

            const response = await ClientAxiosInstance.get(url);

            const totalDataCount = response?.data?.total_count;
            const totalPages = Math.ceil(totalDataCount / limit);
            setTotalPages(totalPages > 0 ? totalPages : 1);
            setShowPagination(totalPages > 1);

            setSelectedChipData(response.data.data);

            if (postedByList.length < response.data.posted_by_list.length) {
                const filteredPostedBy = response.data.posted_by_list.filter((postedBy: string) => postedBy)
                setPostedByList(filteredPostedBy);
            }

            if (eventLocation.length < response.data.location_list.length) {
                const filteredLocation = response.data.location_list.filter((location: string) => location)
                setEventLocation(filteredLocation);
            }

        } catch (error) {
            console.log("Fetching Event Data : ", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [chip, currentPage, compEventSelectedSort, selectedLocation, selectedMode, compPrice, selectedCategory, selectedPostedBy]);

    useEffect(() => {
        fetchData();
    }, [chip, currentPage, compEventSelectedSort, selectedLocation, selectedMode, compPrice, selectedCategory, selectedPostedBy]);

    useEffect(() => {
        setCurrentPage(1);
        setCompLocation([]);
        setSelectedLocation('');
        setCompDate(null);
        setCompPrice({ from: null, to: null });
        setSelectedMode('');
        setSelectedPostedBy('');
        setCompEventSelectedSort('latestdatetime');
        setPostedByList([]);
        setSelectedCategory([]);
        setSelectedChip(currentEventChip);
        fetchData();
    }, [selectedChip, currentEventChip]);

    return (
        <EventContext.Provider value={{
            selectedChipData,
            fetchData,
            totalPages,
            currentPage,
            setCurrentPage,
            selectedChip,
            setSelectedChip,
            showPagination,
            setShowPagination,
            loading,
            setLoading,
            chip,
            postedByList,
            onRefresh, refreshing
        }}>
            {children}
        </EventContext.Provider>
    );
};

export default EventProvider;
