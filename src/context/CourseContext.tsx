import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { FilterContext } from './FilterContext';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';

interface CourseContextValue {
    fetchData: () => void;
    selectedChipData: any[];
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalPages: number;
    selectedChip: string;
    setSelectedChip: (value: string) => void;
    showPagination: boolean;
    setShowPagination: (value: boolean) => void;
    loading: boolean;
    setLoading: (value: boolean) => void;
    refreshing: boolean;
    onRefresh: () => void;
}

export const CourseContext = createContext<CourseContextValue>({
    fetchData: () => { },
    selectedChipData: [],
    currentPage: 1,
    setCurrentPage: () => { },
    totalPages: 0,
    selectedChip: '',
    setSelectedChip: () => { },
    showPagination: true,
    setShowPagination: () => { },
    loading: true,
    setLoading: () => { },
    refreshing: false,
    onRefresh: () => { }
});

const CourseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { selectedSort, selectedCategory, setSelectedCategory, setSelectedSort, compPrice, setCompPrice } = useContext(FilterContext);

    const { currentCourseChip, isLarge } = useContext(ClientContext);

    const [selectedChipData, setSelectedChipData] = useState<any[]>([]);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const [refreshing, setRefreshing] = useState(false);
    const onRefresh = useCallback(() => {
        setRefreshing(true);
    }, []);

    const [selectedChip, setSelectedChip] = useState(currentCourseChip);

    const chip = useMemo(() => {
        switch (selectedChip) {
            case 'Beginner':
                return 'Beginner';
            case 'Intermediate':
                return 'Intermediate';
            case 'Advanced':
                return 'Expert';
            default:
                return null;
        }
    }, [selectedChip]);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const limit = isLarge ? 6 : 3;

            let url = `/course/searchCourse?page=${currentPage}&limit=${limit}&sort=${selectedSort}&course_level=${chip}`;
            if (compPrice.from || compPrice.to) {
                url += `&discount_price=${compPrice.from},${compPrice.to}`;
            }
            if (selectedCategory.length > 0) {
                const category = selectedCategory.join(',');
                url += `&global_category_name=${category}`;
            }
            const response = await ClientAxiosInstance.get(url);

            const totalDataCount = response?.data?.total_count;
            const totalPages = Math.ceil(totalDataCount / limit);

            setTotalPages(totalPages > 0 ? totalPages : 1);
            setShowPagination(totalPages > 1);
            setSelectedChipData(response.data.data);

        } catch (error: any) {
            console.log('Fetching Course Data:', error.response.data);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [chip, selectedChip, currentPage, selectedSort, compPrice, selectedCategory]);

    useEffect(() => {
        fetchData();
    }, [currentPage, selectedChip, selectedSort, compPrice, selectedCategory, fetchData, refreshing]);

    useEffect(() => {
        setCurrentPage(1);
        setSelectedSort('latest');
        setCompPrice({ from: null, to: null });
        setSelectedCategory([]);
        setSelectedChip(currentCourseChip);
    }, [selectedChip, currentCourseChip]);

    const values: CourseContextValue = useMemo(() => ({
        selectedChipData,
        fetchData,
        currentPage,
        setCurrentPage,
        totalPages,
        selectedChip,
        setSelectedChip,
        showPagination,
        setShowPagination,
        loading,
        setLoading,
        onRefresh,
        refreshing
    }), [
        selectedChipData,
        fetchData,
        currentPage,
        totalPages,
        selectedChip,
        showPagination,
        loading
    ]);

    return (
        <CourseContext.Provider value={values}>
            {children}
        </CourseContext.Provider>
    );
};

export default CourseProvider;
