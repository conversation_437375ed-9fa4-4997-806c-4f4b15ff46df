import React, { createContext, useContext, useEffect, useState } from 'react';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';

interface FilterContextValue {
    selectedLocation: string;
    setSelectedLocation: (value: string) => void;
    compLocation: any[];
    setCompLocation: (value: any[]) => void;
    eventLocation: any[];
    setEventLocation: (value: any[]) => void;
    careerLocation: any[];
    setCareerLocation: (value: any[]) => void;
    lawyerLocation: any[];
    setLawyerLocation: (value: any[]) => void;
    compDate: Date | null;
    setCompDate: (value: Date | null) => void;
    compPrice: {
        from: number | null, to: number | null
    };
    setCompPrice: (value: {
        from: number | null, to: number | null
    }) => void;
    selectedMode: string;
    setSelectedMode: (value: string) => void;
    selectedSort: string;
    setSelectedSort: (value: string) => void;
    compEventSelectedSort: string;
    setCompEventSelectedSort: (value: string) => void;
    selectedLawyerSort: string;
    setSelectedLawyerSort: (value: string) => void;
    selectedPostedBy: string;
    setSelectedPostedBy: (value: string) => void;
    selectedJobType: string;
    setSelectedJobType: (value: string) => void;
    salary: {
        from: number | null, to: number | null
    };
    setSalary: (value: {
        from: number | null, to: number | null
    }) => void;
    selectedAreaOfPractice: string;
    setSelectedAreaOfPractice: (value: string) => void;
    areaOfPractice: any[];
    setAreaOfPractice: (value: any[]) => void;
    setSelectedExperience: (value: string) => void;
    selectedExperience: string;
    publishedByList: any[];
    setPublishedByList: (value: any[]) => void;
    selectedPublishedBy: string;
    setSelectedPublishedBy: (value: string) => void;
    setSelectedCategory: (value: string[]) => void;
    selectedCategory: string[];
    topCategories: string[];
    lawyerCategory: any[];
}

export const FilterContext = createContext<FilterContextValue>({
    selectedLocation: '',
    setSelectedLocation: () => { },
    compLocation: [],
    setCompLocation: () => { },
    eventLocation: [],
    setEventLocation: () => { },
    careerLocation: [],
    lawyerLocation: [],
    setCareerLocation: () => { },
    setLawyerLocation: () => { },
    compDate: null,
    setCompDate: () => { },
    compPrice: {
        from: null, to: null
    },
    setCompPrice: () => { },
    selectedMode: '',
    setSelectedMode: () => { },
    selectedPostedBy: '',
    setSelectedPostedBy: () => { },
    selectedSort: 'latest',
    setSelectedSort: () => { },
    compEventSelectedSort: 'latestdatetime',
    setCompEventSelectedSort: () => { },
    selectedLawyerSort: 'all',
    setSelectedLawyerSort: () => { },
    selectedJobType: '',
    setSelectedJobType: () => { },
    salary: {
        from: null, to: null
    },
    setSalary: () => { },
    selectedAreaOfPractice: '',
    setSelectedAreaOfPractice: () => { },
    areaOfPractice: [],
    setAreaOfPractice: () => { },
    selectedExperience: '',
    setSelectedExperience: () => { },
    publishedByList: [],
    setPublishedByList: () => { },
    selectedPublishedBy: '',
    setSelectedPublishedBy: () => { },
    selectedCategory: [],
    setSelectedCategory: () => { },
    topCategories: [],
    lawyerCategory: []
});


const FilterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { selectedJobTypeHome } = useContext(ClientContext);

    const [selectedMode, setSelectedMode] = useState('');
    const [selectedSort, setSelectedSort] = useState('latest');
    const [compEventSelectedSort, setCompEventSelectedSort] = useState('latestdatetime');
    const [compDate, setCompDate] = useState<Date | null>(null);
    const [compPrice, setCompPrice] = useState<{ from: number | null; to: number | null }>({ from: null, to: null });

    const [compLocation, setCompLocation] = useState<any[]>([]);
    const [eventLocation, setEventLocation] = useState<any[]>([]);
    const [careerLocation, setCareerLocation] = useState<any[]>([]);
    const [lawyerLocation, setLawyerLocation] = useState<any[]>([]);
    const [selectedLocation, setSelectedLocation] = useState('');

    const [selectedCategory, setSelectedCategory] = useState<string[]>([]);

    const [topCategories, setTopCategories] = useState<string[]>([]);

    const [selectedPostedBy, setSelectedPostedBy] = useState('');

    const [selectedJobType, setSelectedJobType] = useState(selectedJobTypeHome);
    const [salary, setSalary] = useState<{ from: number | null; to: number | null }>({ from: null, to: null });

    const [areaOfPractice, setAreaOfPractice] = useState<any[]>([]);
    const [selectedAreaOfPractice, setSelectedAreaOfPractice] = useState('');
    const [selectedExperience, setSelectedExperience] = useState('');

    const [publishedByList, setPublishedByList] = useState<any[]>([]);
    const [selectedPublishedBy, setSelectedPublishedBy] = useState('');

    const [selectedLawyerSort, setSelectedLawyerSort] = useState('all');
    const [lawyerCategory, setLawyerCategory] = useState<any[]>([]);

    useEffect(() => {
        const getCategories = async () => {
            try {
                const response = await ClientAxiosInstance.get(`/globalcategory/globalcategories`);
                const category = response.data.data.map((category: any) => category.global_category_name);
                setTopCategories(category);
                setLawyerCategory(response.data.data);

            } catch (error: any) {
                console.log("Get Categories Error: ", error.response);
            }
        }
        getCategories();

    }, []);

    const values: FilterContextValue = {
        compLocation, setCompLocation,
        eventLocation, setCareerLocation,
        careerLocation, setEventLocation,
        selectedLocation, setSelectedLocation,
        lawyerLocation, setLawyerLocation,
        compDate, setCompDate,
        compPrice, setCompPrice,
        selectedMode, setSelectedMode,
        selectedPostedBy, setSelectedPostedBy,
        selectedSort, setSelectedSort,
        selectedJobType, setSelectedJobType,
        salary, setSalary,
        selectedAreaOfPractice, setSelectedAreaOfPractice,
        areaOfPractice, setAreaOfPractice,
        selectedExperience, setSelectedExperience,
        selectedLawyerSort, setSelectedLawyerSort,
        publishedByList, setPublishedByList,
        selectedPublishedBy, setSelectedPublishedBy,
        selectedCategory, setSelectedCategory,
        topCategories, lawyerCategory,
        compEventSelectedSort, setCompEventSelectedSort
    };

    return (
        <FilterContext.Provider value={values}>
            {children}
        </FilterContext.Provider>
    );
};

export default FilterProvider;