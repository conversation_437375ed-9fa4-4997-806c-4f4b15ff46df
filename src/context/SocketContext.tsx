import React, { createContext, useEffect, useState, useContext, useMemo } from 'react';
import { io } from 'socket.io-client';
import { ClientContext } from './ClientContext';
import axios from 'axios';

//@ts-ignore
const api_link = process.env.EXPO_PUBLIC_API_URL;
const socket = io(api_link);
const ClientAxiosInstance = axios.create({ baseURL: api_link });

interface Message {
	_id: string;
	text: string;
	senderType: string;
	senderId: string;
	createdAt: string;
	user: {
		_id: string;
		name: string;
	};
}

interface Discussion {
	total_message_count: number;
	participants: never[];
	createdby_user: any;
	pinned: unknown;
	_id: string;
	title: string;
	creatorId: string;
	createdAt: string;
}

interface SocketContextType {
	messageSenders: Message[];
	discussions: Discussion[];
	sendMessage: (discussionId: string, message: string, senderId: string | null, senderType: string, first_name: string) => void;
	submitNestedReply: (discussionId: string, replyToId: string, message: string, senderId: string, first_name: string, profile_image: string, senderType: string) => void;
	createDiscussion: (discussionData: any) => void;
	getMessageSenders: (discussionId: string) => void;
	pinDiscussion: (discussionId: string) => void;
	unpinDiscussion: (discussionId: string) => void;
	socket: any;
	joinDiscussion: (discussionId: string) => void;
	setDiscussion: (discussionId: any) => void;
	setSelectedChip: React.Dispatch<React.SetStateAction<string>>;
	selectedChip: string;
	currentPage: number;
	setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
	totalPages: number;
	setTotalPages: React.Dispatch<React.SetStateAction<number>>;
	searchQuery: string;
	setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
	activeTab: number;
	setActiveTab: React.Dispatch<React.SetStateAction<number>>;
	loading: boolean;
	setLoading: React.Dispatch<React.SetStateAction<boolean>>;
	showPagination: boolean;
	fetchDiscussions: any;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
	const [messageSenders, setMessageSenders] = useState<Message[]>([]);
	const [discussions, setDiscussions] = useState<Discussion[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(10);
	const [searchQuery, setSearchQuery] = useState('');
	const [activeTab, setActiveTab] = useState(0);
	const [selectedChip, setSelectedChip] = useState<string>('All');
	const { clientId } = useContext(ClientContext);
	const [loading, setLoading] = useState(true);
	const [showPagination, setShowPagination] = useState(false);

	const getChipQueryParams = (chip: string) => {
		switch (chip) {
			case 'Pinned':
				return '&pinnedOnly=true';
			case 'My Discussion':
				return `&createdby_user=${clientId}`;
			case 'Interacted':
				return '&interaction=true';
			default:
				return '';
		}
	};

	const fetchDiscussions = async () => {
		try {
			setLoading(true);
			const chipParams = getChipQueryParams(selectedChip);
			const response = await ClientAxiosInstance.get(
				`/discussion/listDiscussions?userId=${clientId}&page=${currentPage}&limit=10${searchQuery && searchQuery.length > 2 ? `&discussion_title=${searchQuery}` : ""}${chipParams}`
			);
			const newDiscussions = response.data.discussions;
			setTotalPages(Math.ceil(response.data.totalDiscussions / 10));
			setShowPagination(response.data.totalDiscussions > 10);
			setDiscussions(newDiscussions);

		} catch (error:any) {
			console.log('Error fetching discussions:', error.response?.data || error.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		socket.on('connectionStatus', status => {
			console.log(status);
		});

		fetchDiscussions();

		return () => {
			socket.off('discussionCreated');
			socket.off('messageSenders');
		};
	}, [currentPage, totalPages, searchQuery, selectedChip, clientId]);

	const getMessageSenders = (discussionId: string) => {
		socket.emit("getMessageSenders", discussionId)
		socket.on("messageSenders", (messageSendersData) => {
			setMessageSenders(messageSendersData);
		});
	};

	const createDiscussion = (discussionData: any) => {
		socket.emit('createDiscussion', discussionData, (response:any) => {
			if (response) {
				setDiscussions(prevDiscussions => [response, ...prevDiscussions]);
			}
		});
	};

	const sendMessage = (discussionId:string , message:string , senderId:string | null , senderType:string , first_name:string ) => {
	    socket.emit('sendMessage', { discussionId , message , senderId , senderType , first_name });
    };

    const submitNestedReply = (discussionId:string , replyToId:string , message:string , senderId:string | null , first_name:string , profile_image:string , senderType:string ) => {
        socket.emit('submitNestedReply', { discussionId , replyToId , message , senderId , first_name , profile_image , senderType });
    };

    const pinDiscussion = (discussionId:string ) => {
        socket.emit('pinDiscussion', { discussionId , userId : clientId , userType : 'user' });
    };

    const unpinDiscussion = (discussionId:string ) => {
        socket.emit('unpinDiscussion', { discussionId , userId : clientId , userType : 'user' });
		setTimeout(() => fetchDiscussions(), 100);
    };

    const joinDiscussion = (discussionId:string ) => {
        socket.emit('joinDiscussion', { discussionId });
    };

    const value = useMemo(() => ({
        messageSenders,
        discussions,
        sendMessage,
        submitNestedReply,
        createDiscussion,
        getMessageSenders,
        pinDiscussion,
        unpinDiscussion,
        joinDiscussion,
        socket,
        currentPage,
        setCurrentPage,
        totalPages,
        setTotalPages,
        searchQuery,
        setSearchQuery,
        activeTab,
        setActiveTab,
        setSelectedChip,
        selectedChip,
        loading,
        setLoading,
        showPagination,
        fetchDiscussions
    }), [
        messageSenders,
        discussions,
        currentPage,
        totalPages,
        searchQuery,
        activeTab,
        selectedChip,
        loading
    ]);

	return (
	    <SocketContext.Provider value={value}>
	        {children}
	    </SocketContext.Provider>
    );
};

export const useSocket = () => {
	const context = useContext(SocketContext);
	if (!context) {
	    throw new Error("useSocket must be used within a SocketProvider");
    }
	return context;
};