import React, { createContext, useCallback, useEffect, useMemo, useState } from 'react';
import { ClientAxiosInstance } from '../lib/axiosInstance';

interface SearchContextValue {
    searchQuery: string;
    setSearchQuery: (val: string) => void;
    loading: boolean;
    searchedData: any[];
    setSearchedData: (val: any[]) => void;
}

export const SearchContext = createContext<SearchContextValue>({
    searchQuery: '',
    setSearchQuery: () => { },
    loading: false,
    searchedData: [],
    setSearchedData: () => { }
});

const SearchProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const [loading, setLoading] = useState<boolean>(false);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [searchedData, setSearchedData] = useState<any[]>([]);

    const getSearches = useCallback(async () => {
        if (!searchQuery.trim()) {
            setSearchedData([]);
            return;
        }

        try {
            setLoading(true);

            const response = await ClientAxiosInstance.get(`auth/globalSearch?query=${searchQuery.toLowerCase()}`);
            const results = [
                ...response.data.articles || [],
                ...response.data.blogs || [],
                ...response.data.journals || [],
                ...response.data.news || [],
                ...response.data.articlewritings || [],
                ...response.data.essaywritings || [],
                ...response.data.debates || [],
                ...response.data.mootcourts || [],
                ...response.data.quizes || [],
                ...response.data.collegeevents || [],
                ...response.data.seminars || [],
                ...response.data.workshops || [],
                ...response.data.courses || [],
                ...response.data.freelancers || [],
                ...response.data.internships || [],
                ...response.data.jobs || []
            ];

            setSearchedData(results);

        } catch (error) {
            console.log("Error fetching search results: ", error);
        } finally {
            setLoading(false);
        }
    }, [searchQuery]);

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchQuery.trim()) {
                getSearches();
            } else {
                setSearchedData([]);
            }
        }, 200);

        return () => clearTimeout(timeoutId);
    }, [searchQuery, getSearches]);

    const values = useMemo(() => ({
        searchQuery,
        setSearchQuery,
        loading,
        searchedData,
        setSearchedData
    }), [searchQuery, loading, searchedData]);

    return (
        <SearchContext.Provider value={values}>
            {children}
        </SearchContext.Provider>
    );
};

export default SearchProvider;
