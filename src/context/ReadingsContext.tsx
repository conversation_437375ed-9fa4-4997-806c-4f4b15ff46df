import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { FilterContext } from './FilterContext';
import { ClientContext } from './ClientContext';

interface ReadingsContextValue {
    fetchData: () => void;
    selectedChipData: any[];
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalPages: number;
    selectedChip: string;
    setSelectedChip: (value: string) => void;
    showPagination: boolean;
    setShowPagination: (value: boolean) => void;
    chip: string;
    loading: boolean;
    refreshing: boolean;
    onRefresh: () => void;
}

export const ReadingsContext = createContext<ReadingsContextValue>({
    fetchData: () => { },
    selectedChipData: [],
    currentPage: 1,
    setCurrentPage: () => { },
    totalPages: 0,
    selectedChip: '',
    setSelectedChip: () => { },
    showPagination: true,
    setShowPagination: () => { },
    chip: '',
    loading: false,
    refreshing: false,
    onRefresh: () => { }
});

const ReadingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { compDate, selectedSort, setCompDate, setSelectedSort, selectedCategory, setSelectedCategory } = useContext(FilterContext);

    const [selectedChipData, setSelectedChipData] = useState<any[]>([]);

    const [showPagination, setShowPagination] = useState(false);

    const [loading, setLoading] = useState(true);

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const [refreshing, setRefreshing] = useState(false);
    const onRefresh = useCallback(() => {
        setRefreshing(true);
    }, []);

    const { currentReadingsChip, isLarge } = React.useContext(ClientContext);
    const [selectedChip, setSelectedChip] = useState(currentReadingsChip);

    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const chip = selectedChip === 'Blogs' ? 'blog' :
        selectedChip === 'News' ? 'news' :
            selectedChip === 'Articles' ? 'article' :
                selectedChip === 'Journals' ? 'journal' : '';

    const searchChip = selectedChip === 'Blogs' ? 'searchBlog' :
        selectedChip === 'News' ? 'searchNews' :
            selectedChip === 'Articles' ? 'searchArticle' :
                selectedChip === 'Journals' ? 'searchJournal' : '';

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);

            const limit = isLarge ? 6 : 3;
            let url = `/${chip}/${searchChip}?page=${currentPage}&limit=${limit}&sort=${selectedSort}&approval_status=Approved`;

            if (compDate) {
                const formattedDate = formatDate(new Date(compDate));
                setCurrentPage(1);
                url += `&createdAt=${formattedDate}`;
            }
            if (selectedCategory.length > 0) {
                const category = selectedCategory.join(',');
                url += `&global_category_name=${category}`;
            }

            const response = await ClientAxiosInstance.get(url);

            const totalDataCount = response?.data?.total_count;
            const totalPages = Math.ceil(totalDataCount / limit);
            setTotalPages(totalPages > 0 ? totalPages : 1);
            setShowPagination(totalPages > 1);

            const responseData: any[] = response.data.data;
            setSelectedChipData(responseData);

        } catch (error) {
            console.log("Fetching Readings Data : ", error);

        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [currentPage, selectedChip, selectedCategory, selectedSort, compDate, refreshing])

    useEffect(() => {
        fetchData();
    }, [fetchData, currentPage, selectedChip, selectedCategory, selectedSort, compDate, refreshing]);

    useEffect(() => {
        setCurrentPage(1);
        setCompDate(null);
        setSelectedSort('latest');
        setSelectedCategory([]);
        setSelectedChip(currentReadingsChip);
    }, [selectedChip, currentReadingsChip]);

    const values: ReadingsContextValue = {
        selectedChipData,
        fetchData,
        totalPages,
        currentPage,
        setCurrentPage,
        selectedChip,
        setSelectedChip,
        showPagination,
        setShowPagination,
        chip, loading,
        refreshing,
        onRefresh
    };

    return (
        <ReadingsContext.Provider value={values}>
            {children}
        </ReadingsContext.Provider>
    );
};

export default ReadingsProvider;
