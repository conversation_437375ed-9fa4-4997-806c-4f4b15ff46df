import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { FilterContext } from './FilterContext';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';

interface CareerContextValue {
    fetchData: () => void;
    selectedChipData: any[];
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalPages: number;
    selectedChip: string;
    setSelectedChip: (value: string) => void;
    showPagination: boolean;
    setShowPagination: (value: boolean) => void;
    loading: boolean;
    setLoading: (value: boolean) => void;
    chip: string | null;
    refreshing: boolean;
    onRefresh: () => void;
}

export const CareerContext = createContext<CareerContextValue>({
    fetchData: () => { },
    selectedChipData: [],
    currentPage: 1,
    setCurrentPage: () => { },
    totalPages: 0,
    selectedChip: '',
    setSelectedChip: () => { },
    showPagination: true,
    setShowPagination: () => { },
    loading: true,
    setLoading: () => { },
    chip: 'job',
    refreshing: false,
    onRefresh: () => { }
});

const CareerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { selectedLocation, setSelectedLocation, setSelectedCategory,
     salary, setSalary, setSelectedJobType, selectedJobType, selectedCategory } = useContext(FilterContext);

    const { currentCareerChip, selectedJobTypeHome, isLarge } = useContext(ClientContext);

    const [selectedChipData, setSelectedChipData] = useState<any[]>([]);
    const [careerLocation, setCareerLocation] = useState<any[]>([]);

    const [selectedChip, setSelectedChip] = useState(currentCareerChip);

    const [showPagination, setShowPagination] = useState(false);
    const [loading, setLoading] = useState(true);

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const [refreshing, setRefreshing] = useState(false);
    const onRefresh = useCallback(() => {
        setRefreshing(true);
    }, []);

    const chip = useMemo(() => selectedChip === 'Job Listing' ? 'job' : selectedChip === 'Internship' ? 'internship' : null, [selectedChip]);

    const searchChip = useMemo(() => selectedChip === 'Job Listing' ? 'searchJob' : selectedChip === 'Internship' ? 'searchInternship' : null, [selectedChip]);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const limit = isLarge ? 6 : 3;
            let url = `/${chip}/${searchChip}?page=${currentPage}&limit=${limit}&approval_status=Approved`;

            if (selectedLocation) url += `&location=${selectedLocation}`;
            if (salary.from || salary.to) url += `&job_salary_range=${salary.from},${salary.to}`;
            if (selectedJobTypeHome || selectedJobType) url += `&job_type=${selectedJobTypeHome || selectedJobType}`;
            if (selectedCategory.length > 0) {
                const category = selectedCategory.join(',');
                url += `&global_category_name=${category}`;
            }

            const response = await ClientAxiosInstance.get(url);
            const totalDataCount = response?.data?.total_count;
            const totalPages = Math.ceil(totalDataCount / limit);
            setTotalPages(totalPages > 0 ? totalPages : 1);
            setShowPagination(totalPages > 1);

            setSelectedChipData(response?.data?.data || []);

            if (careerLocation?.length < response?.data?.location_list?.length) {
                const filteredLocation = response.data.location_list.filter((location: string) => location)
                setCareerLocation(filteredLocation);
            }

        } catch (error) {
            console.log("Fetching Career Data:", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [chip, searchChip, currentPage, selectedLocation, salary, selectedJobType, selectedCategory, careerLocation]);

    useEffect(() => {
        fetchData();
    }, [currentPage, selectedChip, fetchData, refreshing]);

    useEffect(() => {
        setCurrentPage(1);
        setCareerLocation([]);
        setSelectedLocation('');
        setSalary({ from: null, to: null });
        setSelectedJobType('');
        setSelectedCategory([]);
        setSelectedChip(currentCareerChip);
    }, [selectedChip, currentCareerChip]);

    useEffect(() => {
        fetchData();
    }, [selectedLocation, salary, selectedJobType, fetchData, selectedCategory]);

    const values = useMemo(() => ({
        selectedChipData,
        fetchData,
        totalPages,
        currentPage,
        setCurrentPage,
        selectedChip,
        setSelectedChip,
        showPagination,
        setShowPagination,
        loading,
        setLoading,
        chip, refreshing, setRefreshing, onRefresh
    }), [selectedChipData, totalPages, currentPage, selectedChip, showPagination, loading, chip]);

    return <CareerContext.Provider value={values}>{children}</CareerContext.Provider>;
};

export default CareerProvider;
