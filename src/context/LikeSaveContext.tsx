import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';

interface LikeSaveContextValue {
	saveCard: (id: string, currentChip: string) => void;
	handleView: (id: string, currentChip: string) => void;
	isCardSaved: (id: string) => boolean;
	isCardLiked: (id: string) => boolean;
	registeredItems: Set<string> | null;
	setRegisteredItems: React.Dispatch<React.SetStateAction<Set<string> | null>>;
	likeReadings: (currentChip: string, blogId: string) => void;

}

export const LikeSaveContext = createContext<LikeSaveContextValue>({
	saveCard: () => { },
	isCardSaved: () => false,
	isCardLiked: () => false,
	handleView: () => { },
	registeredItems: null,
	setRegisteredItems: () => { },
	likeReadings: () => { }
});

const LikeSaveProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

	const [savedItems, setSavedItems] = useState<Set<string>>(new Set());
	const [likedItems, setLikedItems] = useState<Set<string>>(new Set());

	const { token, clientId, readingsLikedItems } = useContext(ClientContext);

	useEffect(()=>{
		if(readingsLikedItems){
            setLikedItems(new Set(readingsLikedItems.map(item => item)));
        }
	},[readingsLikedItems])

	const getSelectedChip = (currentChip: string) => {
		switch (currentChip) {
			case 'Quiz': return 'quiz';
			case 'Moot Court': return 'mootcourt';
			case 'Essay Writing': return 'essaywriting';
			case 'Article Writing': return 'articlewriting';
			case 'Debate': return 'debate';
			case 'College Events': return 'collegeevent';
			case 'Seminars': return 'seminar';
			case 'Workshop': return 'workshop';
			case 'Job Listing': return 'job';
			case 'Internship': return 'internship';
			case 'Beginner': return 'course';
			case 'Intermediate': return 'course';
			case 'Expert': return 'course';
			case 'Blogs': return 'blog';
			case 'News': return 'news';
			case 'Articles': return 'article';
			case 'Journals': return 'journal';
			default: return '';
		}
	}

	const savedSubmenus = ['Competitions', 'Events', 'Readings', 'Careers', 'Courses'];

	const getSavedItems = useCallback(async () => {
		try {
			if (!token) return;
			const newSavedItems = new Set<string>();

			await Promise.all(
				savedSubmenus.map(async (submenu) => {
					const endpoint = `/auth/saveditem/${submenu.toLowerCase()}?page=1`;

					const response = await ClientAxiosInstance.get(endpoint);
					const savedItemArray = response.data[submenu.toLowerCase()] || [];

					savedItemArray.forEach((item: any) => {
						if (item._id) {
							newSavedItems.add(item._id);
						}
					});
				})
			);
			setSavedItems(newSavedItems);

		} catch (error: any) {
			console.log('Error fetching saved items : ', error.response.data);
		}
	}, [savedSubmenus, savedItems]);

	const saveCard = useCallback(async (id: string, currentChip: string) => {
		const updatedSavedItems = new Set(savedItems);
		updatedSavedItems.has(id) ? updatedSavedItems.delete(id) : updatedSavedItems.add(id);
		setSavedItems(updatedSavedItems);

		try {
			const chip = getSelectedChip(currentChip);
			const response = await ClientAxiosInstance.post(`/auth/save/${chip}/${id}`, {});
			console.log(response.data.message);

		} catch (error: any) {
			console.log("Save Card Error : ", error?.response?.data || error.message);
			const updatedSavedItems = new Set(savedItems);
			updatedSavedItems.has(id) ? updatedSavedItems.delete(id) : updatedSavedItems.add(id);
			setSavedItems(updatedSavedItems);
		}
	}, [getSavedItems, clientId]);

	const isCardSaved = useCallback((id: string) => savedItems.has(id), [savedItems]);

	const handleView = async (id: string, currentChip: string) => {
		try {
			const chip = getSelectedChip(currentChip);
			const response = await ClientAxiosInstance.post(`/${chip}/view/${id}`);
			// console.log(response.data);

		} catch (error) {
			console.log("View Count Update Error: ", error);
		}
	};

	const likeReadings = useCallback(async (currentChip: string, blogId: string) => {
		const updatedLikedItems = new Set(likedItems);
		updatedLikedItems.has(blogId) ? updatedLikedItems.delete(blogId) : updatedLikedItems.add(blogId);
		setLikedItems(updatedLikedItems);

		try {
			const chip = getSelectedChip(currentChip);
			const response = await ClientAxiosInstance.post(`/${chip}/like/${blogId}`, {});
			console.log(response.data.message);

		} catch (error: any) {
			console.log("Like Card Error : ", error?.response?.data || error.message);

			const updatedLikedItems = new Set(likedItems);
			updatedLikedItems.has(blogId) ? updatedLikedItems.delete(blogId) : updatedLikedItems.add(blogId);
			setLikedItems(updatedLikedItems);
		}
	}, [likedItems, clientId]);

	const isCardLiked = useCallback((id: string) => likedItems.has(id), [likedItems]);

	useEffect(() => {
		getSavedItems();
	}, [clientId]);

	const [registeredItems, setRegisteredItems] = useState<Set<string> | null>(new Set());

	const values = useMemo(() => ({
		saveCard,
		isCardSaved,
		handleView,
		registeredItems,
		setRegisteredItems,
		likeReadings,
		isCardLiked
	}), [saveCard, isCardSaved, handleView, registeredItems]);

	return <LikeSaveContext.Provider value={values}>{children}</LikeSaveContext.Provider>;
};

export default LikeSaveProvider;
