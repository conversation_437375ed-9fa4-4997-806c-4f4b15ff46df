import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { FilterContext } from './FilterContext';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { ClientContext } from './ClientContext';

interface HireLawyerContextValue {
	fetchData: () => void;
	lawyers: any[];
	currentPage: number;
	setCurrentPage: (page: number) => void;
	totalPages: number;
	showPagination: boolean;
	setShowPagination: (value: boolean) => void;
	loading: boolean;
	setLoading: (value: boolean) => void;
	isRegistered: boolean;
	setRegistered: (value: boolean) => void;
	onRefresh: () => void;
	refreshing: boolean;
}

export const HireLawyerContext = createContext<HireLawyerContextValue>({
	fetchData: () => { },
	lawyers: [],
	currentPage: 1,
	setCurrentPage: () => { },
	totalPages: 0,
	showPagination: true,
	setShowPagination: () => { },
	loading: true,
	setLoading: () => { },
	isRegistered: false,
	setRegistered: () => { },
	onRefresh: () => { },
	refreshing: false,
});


const HireLawyerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

	const { selectedLocation, areaOfPractice, setAreaOfPractice, selectedCategory,
		selectedAreaOfPractice, selectedExperience, selectedLawyerSort, setLawyerLocation, lawyerLocation
	} = useContext(FilterContext);

	const { userData, isLarge } = useContext(ClientContext);

	const [lawyers, setLawyers] = useState<any[]>([]);
	const [showPagination, setShowPagination] = useState(false);
	const [loading, setLoading] = useState(true);
	const [isRegistered, setRegistered] = useState(false);

	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(0);

	const [refreshing, setRefreshing] = useState(false);
	const onRefresh = useCallback(() => {
		setRefreshing(true);
	}, []);

	const fetchData = useCallback(async () => {
		try {
			setLoading(true);
			const limit = isLarge ? 6 : 3;

			let url = userData?.freelancer_id ?
				`/freelancer/searchFreelancer?page=${currentPage}&limit=${limit}&freelancer_id=${userData?.freelancer_id}&sort=${selectedLawyerSort}&approval_status=Approved` :
				`/freelancer/searchFreelancer?page=${currentPage}&limit=${limit}&sort=${selectedLawyerSort}&approval_status=Approved`;

			if (selectedLocation) {
				url += `&company_location=${selectedLocation}`
			}
			if (selectedAreaOfPractice) {
				url += `&area_of_practice=${selectedAreaOfPractice}`
			}
			if (selectedExperience) {
				url += `&years_of_experience=${selectedExperience}`
			}
			if (selectedCategory.length > 0) {
				const category = selectedCategory.join(',');
				url += `&global_category_name=${category}`;
			}

			const response = await ClientAxiosInstance.get(url);

			const totalDataCount = response?.data?.total_count;
			const totalPages = Math.ceil(totalDataCount / limit);
			setTotalPages(totalPages > 0 ? totalPages : 1);
			setShowPagination(totalPages > 1);

			const responseData = response.data.data;
			setLawyers(responseData);

			if (areaOfPractice?.length < response.data?.area_of_practice_list?.length) {
				const filteredPractice = response.data.area_of_practice_list.filter((practices: string) => practices)
				setAreaOfPractice(filteredPractice);
			}

			if (lawyerLocation?.length < response.data?.location_list?.length) {
				const filteredLocation = response.data.location_list.filter((location: string) => location)
				setLawyerLocation(filteredLocation);
			}

		} catch (error) {
			console.log("Fetching Lawyer Data : ", error);
		}
		finally {
			setLoading(false);
			setRefreshing(false);
		}
	}, [selectedLocation, selectedAreaOfPractice, selectedExperience, currentPage, selectedCategory, refreshing])

	useEffect(() => {
		fetchData();
	}, [selectedLocation, selectedAreaOfPractice, selectedExperience, currentPage, selectedCategory, refreshing, fetchData]);

	const values: HireLawyerContextValue = {
		lawyers,
		fetchData, totalPages,
		currentPage, setCurrentPage,
		showPagination, setShowPagination,
		loading, setLoading,
		isRegistered,
		setRegistered,
		onRefresh,
		refreshing
	};

	return (
		<HireLawyerContext.Provider value={values}>
			{children}
		</HireLawyerContext.Provider>
	);
};

export default HireLawyerProvider;
