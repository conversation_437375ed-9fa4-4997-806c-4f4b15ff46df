import React from 'react';
import { use<PERSON>onts, DMSans_400Regular, DMSans_400Regular_Italic, DMSans_500Medium, DMSans_500Medium_Italic, DMSans_700Bold, DMSans_700Bold_Italic, } from '@expo-google-fonts/dm-sans';
import { Lora_600SemiBold, Lora_700Bold, Lora_500Medium, Lora_500Medium_Italic, Lora_700Bold_Italic, Lora_600SemiBold_Italic, Lora_400Regular } from '@expo-google-fonts/lora';

interface CustomFontProviderProps {
	children: React.ReactNode;
}

const CustomFontProvider: React.FC<CustomFontProviderProps> = ({ children }) => {
	const [fontsLoaded] = useFonts({
		'DMSans-Regular': DMSans_400Regular,
		'DMSans-RegularItalic': DMSans_400Regular_Italic,
		'DMSans-Medium': DMSans_500Medium,
		'DMSans-MediumItalic': DMSans_500Medium_Italic,
		'DMSans-Bold': DMSans_700Bold,
		'DMSans-BoldItalic': DMSans_700Bold_Italic,
		'Lora-regular': Lora_400Regular,
		'Lora-SemiBold': Lora_600SemiBold,
		'Lora-Bold': Lora_700Bold,
		'Lora-Medium': Lora_500Medium,
		'Lora-MediumItalic': Lora_500Medium_Italic,
		'Lora-BoldItalic': Lora_700Bold_Italic,
		'Lora-SemiBoldItalic': Lora_600SemiBold_Italic,
	});

	if (!fontsLoaded) {
		return null;
	}

	return <>{children}</>;
};

export default CustomFontProvider;
