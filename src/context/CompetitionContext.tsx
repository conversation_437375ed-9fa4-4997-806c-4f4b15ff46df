import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { FilterContext } from './FilterContext';
import { ClientContext } from './ClientContext';

interface CompetitionContextValue {
    fetchData: () => void;
    selectedChipData: any[];
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalPages: number;
    selectedChip: string;
    setSelectedChip: (value: string) => void;
    showPagination: boolean;
    setShowPagination: (value: boolean) => void;
    loading: boolean;
    setLoading: (value: boolean) => void;
    chip: string | null;
    postedByList: any[];
    refreshing: boolean;
    onRefresh: () => void;
}

export const CompetitionContext = createContext<CompetitionContextValue>({
    fetchData: () => { },
    selectedChipData: [],
    currentPage: 1,
    setCurrentPage: () => { },
    totalPages: 0,
    selectedChip: '',
    setSelectedChip: () => { },
    showPagination: true,
    setShowPagination: () => { },
    loading: true,
    setLoading: () => { },
    chip: 'quiz',
    postedByList: [],
    refreshing: false,
    onRefresh: () => { }
});


const CompetitionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const { selectedLocation, compDate, compPrice, selectedMode, selectedPostedBy, compEventSelectedSort,
        setSelectedLocation, setCompDate, setCompPrice, setSelectedMode,
        setSelectedPostedBy, setCompEventSelectedSort, compLocation, setCompLocation, selectedCategory, setSelectedCategory
    } = useContext(FilterContext);

    const { currentCompChip, isLarge } = useContext(ClientContext);
    const limit = isLarge ? 6 : 3;

    const [selectedChipData, setSelectedChipData] = useState<any[]>([]);
    const [postedByList, setPostedByList] = useState<any[]>([]);

    const [selectedChip, setSelectedChip] = useState(currentCompChip);
    const [showPagination, setShowPagination] = useState(false);

    const [loading, setLoading] = useState(true);

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const [refreshing, setRefreshing] = useState(false);
    const onRefresh = useCallback(() => {
        setRefreshing(true);
    }, []);

    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const chip = useMemo(() => {
        switch (selectedChip) {
            case 'Quiz': return 'quiz';
            case 'Moot Court': return 'mootcourt';
            case 'Essay Writing': return 'essaywriting';
            case 'Article Writing': return 'articlewriting';
            case 'Debate': return 'debate';
            default: return null;
        }
    }, [selectedChip]);

    const searchChip = useMemo(() => {
        switch (selectedChip) {
            case 'Quiz': return 'searchQuizes';
            case 'Moot Court': return 'searchMootcourt';
            case 'Essay Writing': return 'searchEssaywriting';
            case 'Article Writing': return 'searchArticlewriting';
            case 'Debate': return 'searchDebate';
            default: return null;
        }
    }, [selectedChip]);


    const dateChip = selectedChip === 'Quiz' ? 'date_time' :
        selectedChip === 'Moot Court' ? 'date_time' :
            selectedChip === 'Essay Writing' ? 'submission_date' :
                selectedChip === 'Article Writing' ? 'submission_date' :
                    selectedChip === 'Debate' ? 'date_time' : null;

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);

            let url = `/${chip}/${searchChip}?page=${currentPage}&limit=${limit}&sort=${compEventSelectedSort}`;

            if (selectedChip !== 'Quiz') {
                url += `&approval_status=Approved`;
            }
            if (selectedLocation) {
                url += `&location=${selectedLocation}`;
            }
            if (selectedMode) {
                url += `&mode=${selectedMode}`;
            }
            if (compPrice.from || compPrice.to) {
                url += `&discount_price=${compPrice.from},${compPrice.to}`;
            }
            if (selectedPostedBy) {
                url += `&posted_by=${selectedPostedBy}`;
            }
            if (compDate) {
                const formattedDate = formatDate(new Date(compDate));
                setCurrentPage(1);
                url += `&${dateChip}=${formattedDate}`;
            }
            if (selectedCategory.length > 0) {
                const category = selectedCategory.join(',');
                url += `&global_category_name=${category}`;
            }

            const response = await ClientAxiosInstance.get(url);

            const responseData: any[] = response.data.data;
            setSelectedChipData(responseData);

            const totalDataCount = response?.data?.total_count;
            const totalPages = Math.ceil(totalDataCount / limit);
            setTotalPages(totalPages > 0 ? totalPages : 1);
            setShowPagination(totalPages > 1);

            if (postedByList?.length < response?.data?.posted_by_list?.length) {
                const filteredPostedByList = response.data.posted_by_list.filter((postedBy: string) => postedBy)
                setPostedByList(filteredPostedByList);
            }
            if (compLocation?.length < response?.data?.location_list?.length) {
                const filteredLocation = response.data.location_list.filter((location: string) => location)
                setCompLocation(filteredLocation);
            }

        } catch (error) {
            console.log("Fetching Competition Data:", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [chip, selectedChip, searchChip, currentPage, compEventSelectedSort, selectedLocation, selectedMode, compPrice, selectedPostedBy, compDate, postedByList, compLocation, selectedCategory, dateChip]);


    useEffect(() => {
        setSelectedChip(currentCompChip);
    }, [currentCompChip]);

    useEffect(() => {
        fetchData();
    }, [currentPage, selectedChip, refreshing, selectedLocation, selectedMode, selectedPostedBy, selectedCategory, compEventSelectedSort, compDate, compPrice]);

    useEffect(() => {
        setCurrentPage(1);
        setCompLocation([]);
        setSelectedLocation('');
        setCompDate(null);
        setCompPrice({ from: null, to: null });
        setSelectedMode('');
        setSelectedPostedBy('');
        setCompEventSelectedSort('latestdatetime');
        setPostedByList([]);
        setSelectedCategory([]);

        fetchData();
    }, [selectedChip]);

    const values: CompetitionContextValue = {
        selectedChipData,
        fetchData, totalPages,
        currentPage, setCurrentPage,
        selectedChip, setSelectedChip,
        showPagination, setShowPagination,
        loading, setLoading, chip, postedByList,
        refreshing, onRefresh
    };

    return (
        <CompetitionContext.Provider value={values}>
            {children}
        </CompetitionContext.Provider>
    );
};

export default CompetitionProvider;
