import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useColorScheme } from 'nativewind';
import { ClientAxiosInstance } from '../lib/axiosInstance';
import { Dimensions, Image, Platform } from 'react-native';

interface ClientContextValue {
    setToken: React.Dispatch<React.SetStateAction<string | null>>;
    token: string | null;
    navigation: any;
    userData: UserData;
    setUserData: (data: Partial<UserData>) => void;
    getClientData: () => void;
    clientId: string | null;
    setClientId: (clientId: string | null) => void;
    colorScheme: string | null | undefined;
    toggleColorScheme: (value: boolean) => void;
    currentCompChip: string,
    setCurrentCompChip: (val: string) => void,
    currentEventChip: string,
    setCurrentEventChip: (val: string) => void,
    currentCourseChip: string,
    setCurrentCourseChip: (val: string) => void,
    currentCareerChip: string,
    setCurrentCareerChip: (val: string) => void,
    currentReadingsChip: string,
    setCurrentReadingsChip: (val: string) => void;
    registeredCourses: any[];
    registeredComps: any[];
    registeredEvents: any[];
    registeredCareers: any[];
    loading: boolean;
    selectedJobTypeHome: string;
    setSelectedJobTypeHome: (jobType: string) => void;
    userSavedItems: any[];
    isAndroid: boolean;
    isLarge: boolean;
    isMedium: boolean;
    readingsLikedItems: string[];
    showAdvocateCard: boolean;
    setShowAdvocateCard: (value: boolean) => void;
}

export const ClientContext = createContext<ClientContextValue>({
    setToken: () => { },
    token: null,
    userData: {
        id: '',
        first_name: '',
        last_name: '',
        email: '',
        mobile: '',
        occupation: '',
        registration_date: '',
        user_profile: '',
        attended_quizzes: [],
        freelancer_id: '',
        contacted_freelancers: [],
        completed_courses: [],
        completed_quizes: [],
    },
    navigation: null,
    setUserData: () => { },
    clientId: null,
    setClientId: () => { },
    getClientData: () => { },
    colorScheme: null,
    toggleColorScheme: () => { },
    currentCompChip: '',
    setCurrentCompChip: () => { },
    currentEventChip: '',
    setCurrentEventChip: () => { },
    currentCourseChip: '',
    setCurrentCourseChip: () => { },
    currentCareerChip: '',
    setCurrentCareerChip: () => { },
    currentReadingsChip: '',
    setCurrentReadingsChip: () => { },
    registeredCourses: [],
    registeredComps: [],
    registeredEvents: [],
    registeredCareers: [],
    loading: false,
    setSelectedJobTypeHome: () => { },
    selectedJobTypeHome: '',
    userSavedItems: [],
    isAndroid: false,
    isMedium: false,
    isLarge: false,
    readingsLikedItems: [],
    showAdvocateCard: false,
    setShowAdvocateCard: () => { },
});

interface UserData {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    mobile: string;
    occupation: string;
    registration_date: string;
    user_profile: string;
    attended_quizzes: any[];
    freelancer_id: string;
    contacted_freelancers: any[];
    completed_courses: any[];
    completed_quizes: any[];
}

type ColorSchemeSystem = 'light' | 'dark' | 'system';
const { width } = Dimensions.get('window');

const ClientProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const navigation = useNavigation();
    const [selectedJobTypeHome, setSelectedJobTypeHome] = useState('');

    const isAndroid = Platform.OS === 'android';
    const isLarge = width > 600;
    const isMedium = width >= 400 && width <= 600;

    const [userData, setUserDataState] = useState<UserData>({
        id: '',
        first_name: '',
        last_name: '',
        email: '',
        mobile: '',
        occupation: '',
        registration_date: '',
        user_profile: '',
        attended_quizzes: [],
        freelancer_id: '',
        contacted_freelancers: [],
        completed_courses: [],
        completed_quizes: []
    });

    const [registeredComps, setRegisteredComps] = useState<any[]>([]);
    const [registeredEvents, setRegisteredEvents] = useState<any[]>([]);
    const [registeredCareers, setRegisteredCareers] = useState<any[]>([]);
    const [registeredCourses, setRegisteredCourses] = useState<any[]>([]);
    const [userSavedItems, setUserSavedItems] = useState<any[]>([]);
    const [showAdvocateCard, setShowAdvocateCard] = useState(true);
    const [readingsLikedItems, setReadingsLikedItems] = useState<any[]>([]);

    const [loading, setLoading] = useState(true);

    const [token, setToken] = useState<string | null>(null);
    const [clientId, setClientId] = useState<string | null>(null);

    const { colorScheme, setColorScheme } = useColorScheme();

    const setUserData = (data: Partial<UserData>) => {
        setUserDataState(prevData => ({
            ...prevData,
            ...data,
        }));
    };

    const getToken = async () => {
        try {
            const token = await AsyncStorage.getItem('LAWCUBE_TOKEN');
            setToken(token);
        } catch (error) {
            console.log('Error fetching token:', error);
        }
    };

    const getUserId = async () => {
        try {
            const userId = await AsyncStorage.getItem('LAWCUBE_USERID');
            setClientId(userId);
        } catch (error) {
            console.log('Error fetching user id:', error);
        }
    };

    const getClientData = async () => {
        try {
            setLoading(true);
            if (!token) return;

            const response = await ClientAxiosInstance.get(`/auth/${clientId}`);
            const data = response?.data?.data;

            setUserData({
                id: data?._id,
                first_name: data?.first_name,
                last_name: data?.last_name,
                email: data?.email,
                mobile: data?.mobile,
                occupation: data?.occupation,
                registration_date: data?.registration_date,
                user_profile: data?.user_profile || '',
                attended_quizzes: data?.attended_quizzes || [],
                freelancer_id: data?.freelancer_id || '',
                contacted_freelancers: data?.contacted_freelancers || [],
                completed_courses: data?.completed_courses || [],
                completed_quizes: data?.completed_quizes || [],
            });

            setReadingsLikedItems([
                ...data?.liked_blogs?.map((blog: any) => blog?.itemId),
                ...data?.liked_article?.map((article: any) => article?.itemId),
                ...data?.liked_journal?.map((journal: any) => journal?.itemId),
                ...data?.liked_news?.map((news: any) => news?.itemId)
            ])

            setUserSavedItems(data?.saved);
            setRegisteredCourses(data?.registered_courses?.map((item: any) => item?.course_id?._id));
            setRegisteredCareers([
                {
                    'Job Listing': data?.registered_jobs?.map((item: any) => item?.job_id?._id),
                    'Internship': data?.registered_internships?.map((item: any) => item?.internship_id?._id)
                }
            ]);
            setRegisteredComps([
                {
                    'Quiz': data?.registered_quiz?.map((comp: any) => comp?.quiz_id?._id),
                    'Moot Court': data?.registered_mootcourts?.map((comp: any) => comp?.mootcourt_id?._id),
                    'Essay Writing': data?.registered_essaywritings?.map((comp: any) => comp?.essaywriting_id?._id),
                    'Article Writing': data?.registered_articlewritings?.map((comp: any) => comp?.articlewriting_id?._id),
                    'Debate': data?.registered_debates?.map((comp: any) => comp?.debate_id?._id),
                }
            ]);
            setRegisteredEvents([
                ...data?.registered_seminars?.map((event: any) => event?.seminar_id?._id),
                ...data?.registered_workshops?.map((event: any) => event?.workshop_id?._id),
                ...data?.registered_collegeevents?.map((event: any) => event?.collegeevent_id?._id)
            ]);

        } catch (error) {
            console.log('Error fetching user data error : ', error);
        } finally {
            setLoading(false);
        }
    };

    const getTheme = async () => {
        try {
            const storedTheme = await AsyncStorage.getItem('LAWCUBE_THEME');
            if (storedTheme) {
                setColorScheme(storedTheme as ColorSchemeSystem);
            }
        } catch (error) {
            console.log("Error fetching theme from AsyncStorage:", error);
        }
    };

    const toggleColorScheme = async (isDarkMode: boolean) => {
        const newScheme = isDarkMode ? 'dark' : 'light';
        setColorScheme(newScheme);

        try {
            await AsyncStorage.setItem('LAWCUBE_THEME', newScheme);
        } catch (error) {
            console.log("Error saving theme to AsyncStorage:", error);
        }
    };

    useEffect(() => {
        getToken();
        getUserId();
        getClientData();
        getTheme();
    }, []);

    useEffect(() => {
        getClientData();
    }, [clientId]);

    const [currentCompChip, setCurrentCompChip] = useState<string>('Quiz');
    const [currentEventChip, setCurrentEventChip] = useState<string>('College Events');
    const [currentCourseChip, setCurrentCourseChip] = useState<string>('Beginner');
    const [currentCareerChip, setCurrentCareerChip] = useState<string>('Job Listing');
    const [currentReadingsChip, setCurrentReadingsChip] = useState<string>('Blogs');

    const values: ClientContextValue = {
        userData, setUserData, setToken, token, setClientId, clientId,
        navigation, colorScheme, getClientData, registeredCourses,
        toggleColorScheme, currentCompChip, setCurrentCompChip,
        currentEventChip, setCurrentEventChip, currentCourseChip, setCurrentCourseChip,
        currentCareerChip, setCurrentCareerChip, currentReadingsChip, setCurrentReadingsChip,
        registeredComps, registeredEvents, registeredCareers, userSavedItems,
        loading, selectedJobTypeHome, setSelectedJobTypeHome, isAndroid, isLarge, isMedium,
        readingsLikedItems, showAdvocateCard, setShowAdvocateCard,
    };

    return (
        <ClientContext.Provider value={values}>
            {children}
        </ClientContext.Provider>
    );
};

export default ClientProvider;
